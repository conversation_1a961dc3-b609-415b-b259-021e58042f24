package com.controller.drive;

import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.constant.enums.drive.WpDriveHistoryActionEnum;
import com.constant.enums.drive.WpDriveUploadTypeEnum;
import com.dto.drive.*;
import com.entity.wpDrive.WpDriveConfig;
import com.service.drive.drive.DriveService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/drive")
@Slf4j
public class DriveController {

    private final DriveService driveService;

    public DriveController(DriveService driveService) {
        this.driveService = driveService;
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @GetMapping()
    public ResponseEntity<List<S3ObjectSummary>> getList(
            @RequestParam(name = "bucketName", required = false, defaultValue = "") String bucketName,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "label,asc") String sort) {
        log.info("--- Execute getListDrive method: Start--");
        List<S3ObjectSummary> res = driveService.getList(bucketName, page, size, sort);
        log.info("--- Execute getListDrive method: End--");
        return ResponseEntity.ok().body(res);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @GetMapping("/get-file-folder-list")
    @Operation(description = "Xem danh sách file và thư mục trong drive")
    @ResponseStatus(HttpStatus.OK)
    public AllListScreenDTO getList(
            @RequestParam(value = "type", required = false, defaultValue = "-1") List<Integer> type,
            @RequestParam(value = "fileSize", required = false, defaultValue = "-1") Integer fileSize,
            @RequestParam(value = "ownerId", required = false, defaultValue = "-1") List<Long> ownerId,
            @RequestParam(value = "searchDateType", required = false, defaultValue = "0") Integer searchDateType,
            @RequestParam(value = "startDate", required = false, defaultValue = "01/01/1970") @DateTimeFormat(pattern = "dd/MM/yyyy") Date startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "01/01/1970") @DateTimeFormat(pattern = "dd/MM/yyyy") Date endDate,
            @RequestParam(value = "searchValue", required = false, defaultValue = "") String searchValue,
            @RequestParam(value = "screen", required = false, defaultValue = "") String screen
    ) {
        return driveService.getListFileFolder(screen, type, fileSize, ownerId, searchDateType, startDate, endDate, searchValue);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_DETAIL')")
    @GetMapping("/file-folder-detail")
    @Operation(description = "Xem thông tin chi tiết file và thư mục trong drive")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<FileFolderDetailDTO> getDetail(
            @RequestParam(value = "fileFolderId") Long fileFolderId
    ) {
        return ResponseEntity.ok().body(driveService.getDetail(fileFolderId));
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @GetMapping("/file-inside-folder")
    @Operation(description = "Xem thông tin file thư mục trong thư mục trong drive")
    @ResponseStatus(HttpStatus.OK)
    public AllListScreenDTO getFileFolderInFolder(
            @RequestParam(value = "folderId") Long folderId,
            @RequestParam(value = "screen", required = false, defaultValue = "") String screen,
            @RequestParam(value = "actionType", required = false, defaultValue = "DETAIL") String actionType,
            @RequestParam(value = "type", required = false, defaultValue = "-1") List<Integer> type,
            @RequestParam(value = "fileSize", required = false, defaultValue = "-1") Integer fileSize,
            @RequestParam(value = "ownerId", required = false, defaultValue = "-1") List<Long> ownerId,
            @RequestParam(value = "searchDateType", required = false, defaultValue = "0") Integer searchDateType,
            @RequestParam(value = "startDate", required = false, defaultValue = "01/01/1970") @DateTimeFormat(pattern = "dd/MM/yyyy") Date startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "01/01/1970") @DateTimeFormat(pattern = "dd/MM/yyyy") Date endDate,
            @RequestParam(value = "searchValue", required = false, defaultValue = "") String searchValue
    ) {
        return driveService.getFileFolderInFolder(screen, folderId, actionType, type, fileSize, ownerId, searchDateType, startDate, endDate, searchValue);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PutMapping("/liked-file-folder/{fileFolderId}")
    @Operation(description = "Thêm vào yêu thích file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void likedFileFolder(
            @PathVariable Long fileFolderId
    ) {
        driveService.likedFileFolder(fileFolderId);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_SHARE')")
    @PutMapping("/share-file-folder")
    @Operation(description = "Chia sẻ file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void shareFileFolder(
            @RequestBody ActionDTO action
    ) {
        driveService.shareFileFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PutMapping("/edit-name-file-folder")
    @Operation(description = "Sửa tên file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void editFileFolder(
            @RequestBody ActionDTO action
    ) {
        driveService.editFileFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PutMapping("/pin-file-folder/{fileFolderId}")
    @Operation(description = "Ghim file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void pinFileFolder(
            @PathVariable Long fileFolderId
    ) {
        driveService.pinFileFolder(fileFolderId);
    }

    @PutMapping("/check-name-in-folder")
    @Operation(description = "Kiểm tra tên file hoặc thư mục đã tồn tại trong thư mục chưa")
    @ResponseStatus(HttpStatus.OK)
    public boolean checkNameInFolder(
            @RequestBody ActionDTO action
    ) {
        return driveService.checkNameInFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PutMapping("/move-file-folder")
    @Operation(description = "Di chuyển file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void moveFileFolder(
            @RequestBody ActionDTO action
    ) {
        driveService.moveFileFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_DELETE')")
    @PutMapping("/move-file-folder-bin")
    @Operation(description = "Chuyển file hoặc thư mục vào thùng rác")
    @ResponseStatus(HttpStatus.OK)
    public void moveToBin(
            @RequestBody ActionDTO action
    ) {
        driveService.moveToBin(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_RESTORE')")
    @PutMapping("/restore-file-folder")
    @Operation(description = "Khôi phục file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void restoreFileFolder(
            @RequestBody ActionDTO action
    ) {
        driveService.restoreFileFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_DELETE')")
    @PutMapping("/delete-file-folder")
    @Operation(description = "Xóa file hoặc thư mục")
    @ResponseStatus(HttpStatus.OK)
    public void deleteFileFolder(
            @RequestBody ActionDTO action
    ) {
        driveService.deleteFileFolder(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @GetMapping("/cloud-connection-info")
    @Operation(description = "Lấy thông tin kết nối đến cloud")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<List<CloudConnectionInforITF>> getCloudConnectionInfor() {
        return ResponseEntity.ok().body(driveService.getCloudConnectionInfor());
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PostMapping("/save-cloud-connection")
    @Operation(description = "Lưu thông tin kết nối đến cloud")
    @ResponseStatus(HttpStatus.OK)
    public void saveCloudConnection(
            @RequestBody CloudConnectionInforDTO cloudConnectionInfor
    ) {
        driveService.saveCloudConnection(cloudConnectionInfor);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @PutMapping("/disconnect-cloud-connection/{id}")
    @Operation(description = "Ngắt kết nối đến cloud")
    @ResponseStatus(HttpStatus.OK)
    public void disconnectCloudConnection(
            @PathVariable Long id
    ) {
        driveService.disconnectCloudConnection(id);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_SHARE')")
    @GetMapping("/get-list-combobox-member-share")
    @Operation(description = "Lấy danh sách thành viên để chia sẻ")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<List<MemberShareITF>> getListComboboxMemberShare(
            @RequestParam(value = "searchValue", required = false, defaultValue = "") String searchValue,
            @RequestParam(value = "driveId", required = false, defaultValue = "-1") Long driveId
    ) {
        return ResponseEntity.ok().body(driveService.getListComboboxMemberShare(searchValue, driveId));
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_SHARE')")
    @GetMapping("/get-link-share/{fileFolderId}")
    @Operation(description = "Lấy link chia sẻ liên kết")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<String> getLinkShare(
            @PathVariable Long fileFolderId
    ) {
        return ResponseEntity.ok().body(driveService.getLinkShare(fileFolderId));
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_SHARE')")
    @PutMapping("/update-member-shared")
    @Operation(description = "Cập nhật thành viên được chia sẻ")
    @ResponseStatus(HttpStatus.OK)
    public void updateMemberShared(
            @RequestBody ActionDTO action
    ) {
        driveService.updateMemberShared(action);
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_UPLOAD')")
    @PostMapping(value = "/upload-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Void> uploadFile(@RequestParam(value = "file") MultipartFile multipartFile, String filePath, String fileName,
                                           WpDriveUploadTypeEnum actionType) throws IOException {
        driveService.uploadFile(multipartFile, filePath, fileName, actionType);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_UPLOAD')")
    @PostMapping(value = "/create-folder")
    public ResponseEntity<Void> createFolder(@RequestBody DriveCreateFolderReqDTO paramDTO) {
        driveService.createFolder(paramDTO.getFolderPath(), paramDTO.getFolderName(), paramDTO.getActionType(), WpDriveHistoryActionEnum.CREATE);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_UPLOAD')")
    @PostMapping(value = "/upload-folder", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Void> uploadFolder(@RequestParam(value = "files") MultipartFile[] multipartFiles, String folderPath,
                                             String folderName, WpDriveUploadTypeEnum actionType) {
        driveService.uploadFolder(multipartFiles, folderPath, folderName, actionType);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_DOWNLOAD')")
    @GetMapping(value = "/download-file/{id}")
    @Operation(description = "Download file")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> downloadFile(@PathVariable Long id, HttpServletResponse response) throws IOException {
        driveService.downloadFile(id, response);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_DOWNLOAD')")
    @GetMapping(value = "/download-files")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> downloadFiles(
            HttpServletResponse response,
            @RequestParam(name = "ids", required = false, defaultValue = "-1") List<Long> ids) throws IOException {
        if (!CollectionUtils.isEmpty(ids)) driveService.downloadFiles(ids, response);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_LIST')")
    @GetMapping(value = "/system-param")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<StorageDTO> getCurrentStorage() {
        return ResponseEntity.ok().body(driveService.getCurrentStorage());
    }

    /**
     * API Lưu cấu hình úng dụng drive
     */
    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_CONFIG')")
    @PostMapping(value = "/save-config")
    public ResponseEntity<Void> saveConfig(@RequestBody SaveConfigReqDTO paramDTO) throws IOException {
        driveService.saveConfig(paramDTO.getSortType(), paramDTO.getAutoDelete(), paramDTO.getShowRecently(), paramDTO.getDraggable(), paramDTO.getCheckedDate());
        return ResponseEntity.ok().build();
    }

    /**
     * API Lấy cấu hình úng dụng drive
     */
    @PreAuthorize("@dynamicAccessAspect.hasAnyAuthority('DRIVE_CONFIG')")
    @GetMapping(value = "/drive-config")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<WpDriveConfig> getDriveConfig() {
        return ResponseEntity.ok().body(driveService.getDriveConfig());
    }
}
