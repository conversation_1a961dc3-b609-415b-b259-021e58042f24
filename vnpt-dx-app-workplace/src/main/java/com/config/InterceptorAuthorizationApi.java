package com.config;

import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import com.onedx.common.dto.oauth2.CustomUserDetails;


@SuppressWarnings("deprecation")
@Component
public class InterceptorAuthorizationApi extends HandlerInterceptorAdapter {

    @Autowired
    private TokenStore tokenStore;

    MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        String method = request.getMethod();
        if (!method.equals("OPTIONS")) {
            String authorization = request.getHeader("Authorization");
            if (authorization != null && authorization.toLowerCase().contains("bearer")) {
                RedisTokenStore redisStore = (RedisTokenStore) tokenStore;
                OAuth2Authentication authentication = redisStore.readAuthentication(authorization.substring(7));
                checkUser(authentication);

                // bổ sung bearerToken vào trong UserDetails phục vụ tích hợp IDC Portals
                SecurityContext securityContext = SecurityContextHolder.getContext();
                if (securityContext != null && securityContext.getAuthentication() != null &&
                    securityContext.getAuthentication().getPrincipal() != null) {
                    ((CustomUserDetails) securityContext.getAuthentication().getPrincipal()).setBearerToken(authorization);
                }
            }
        }
        return true;
    }

    private void checkUser(OAuth2Authentication authentication) {
        if (authentication != null) {
            CustomUserDetails user = (CustomUserDetails) authentication.getPrincipal();
            // Nếu user đang login có parent bị inactive thì user cũng không được vào hệ
            // thống
            if (!user.isEnabled() || (!Objects.equals(user.getParentId(), -1L) && !user.isParentEnabled())) {
                throw new DisabledException(this.messages
                    .getMessage("AbstractUserDetailsAuthenticationProvider.disabled", "User is disabled"));
            }
        }
    }

}
