package com.util;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.constants.values.APIKeyConst;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.security.roles.RoleType;
import lombok.val;


@SuppressWarnings("deprecation")
public class AuthUtil {

    public static final Long PARENT_ID = -1L;

    private AuthUtil() {
    }

    public static OAuth2Request getOauth2Authentication() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return ((OAuth2Authentication) securityContext.getAuthentication()).getOAuth2Request();
    }

    public static String getUserLogin() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication.getName();
    }

    public static Optional<String> getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(extractPrincipal(securityContext.getAuthentication()));
    }

    /**
     * lay thong tin nguoi dung hien tai
     *
     * @return Thong tin user
     */
    public static CustomUserDetails getCurrentUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        if (securityContext == null || securityContext.getAuthentication() == null
            || securityContext.getAuthentication().getPrincipal() == null) {
            return null;
        }
        Object principal = securityContext.getAuthentication().getPrincipal();
        if (principal instanceof String && principal.equals("anonymousUser")) {
            return null;
        }
        if (principal instanceof CustomUserDetails) {
            return (CustomUserDetails) principal;
        }
        return null;
    }

    public static CustomUserDetails getLoggedInUser() {
        CustomUserDetails userDetails = getCurrentUser();
        if (userDetails == null) {
            throw new AccessDeniedException("You are not a logged in user");
        }
        return userDetails;
    }

    public static Long getCurrentUserId() {
        CustomUserDetails customUserDetails = getCurrentUser();
        if (customUserDetails != null) {
            return customUserDetails.getId();
        }
        return null;
    }

    public static UUID getCurrentUserUUIID() {
        CustomUserDetails customUserDetails = getCurrentUser();
        if (customUserDetails != null) {
            return customUserDetails.getSmeUUID();
        }
        return null;
    }

    public static Set<String> getOauthRequestScope() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof OAuth2Authentication) {
            return ((OAuth2Authentication) authentication).getOAuth2Request().getScope();
        }
        return null;
    }

    private static String extractPrincipal(Authentication authentication) {
        if (authentication == null) {
            return null;
        } else if (authentication.getPrincipal() instanceof UserDetails) {
            UserDetails springSecurityUser = (UserDetails) authentication.getPrincipal();
            return springSecurityUser.getUsername();
        } else if (authentication.getPrincipal() instanceof String) {
            return (String) authentication.getPrincipal();
        }
        return null;
    }

    /**
     * Lay ma nguoi dung cua nguoi quan ly
     *
     * @return userId
     */
    public static Long getCurrentParentId() {
        CustomUserDetails userDetails = getCurrentUser();
        if (userDetails == null) {
            return null;
        }
        Long parentId = userDetails.getParentId();
        if (Objects.equals(parentId, -1L)) {
            return userDetails.getId();
        }
        return parentId;
    }

    /**
     * lay user name cua nguoi dung
     */
    public static Optional<String> getUsernameCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(extractPrincipal(securityContext.getAuthentication()));
    }


    /**
     * Kiểm tra role
     *
     * @param listRole the list role
     * @return the boolean
     */
    public static boolean checkUserRoles(List<String> listRole) {
        CustomUserDetails userDetails = getCurrentUser();
        if (userDetails == null) {
            return false;
        }
        return getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList()).stream().anyMatch(listRole::contains) ||
            ((Set<String>) Objects.requireNonNull(getCurrentUser().getAttribute(APIKeyConst.API_KEY_WP))).stream().anyMatch(listRole::contains);
    }

    /**
     * Kiểm tra role
     *
     * @param listRole the list role
     * @return the boolean
     */
    public static boolean anyMatchUserRole(List<String> listRole, List<String> listUserRole) {
        if (CollectionUtils.isEmpty(listRole) || CollectionUtils.isEmpty(listUserRole)) {
            return false;
        }
        return listUserRole.stream().anyMatch(listRole::contains);
    }

    /**
     * lấy url hiện tại
     *
     * @return the boolean
     */
    public static String getCurrentRequest() {
        val currentRequest = ((ServletRequestAttributes) RequestContextHolder
            .currentRequestAttributes()).getRequest();
        val url = currentRequest.getRequestURL();
        val method = currentRequest.getMethod();
        return method.concat(" ").concat(url.toString());
    }

    /**
     * Kiểm tra role theo portal
     *
     * @return the boolean
     */
    public static boolean checkUserRolesByPortal(List<PortalType> portalType) {
        if (portalType.contains(PortalType.ADMIN) &&
            AuthUtil
                .checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN_PROVINCE.getValue()))) {
            return true;
        }
        if (portalType.contains(PortalType.DEV) && AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(),
            RoleType.DEVELOPER_BUSINESS.getValue(), RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.CUSTOMER_SUPPORT.getValue()))) {
            return true;
        }
        return portalType.contains(PortalType.SME) &&
            AuthUtil.checkUserRoles(Arrays.asList(RoleType.SME.getValue(), RoleType.EMPLOYEE.getValue()));
    }

    /**
     * Kiểm tra role theo portal
     *
     * @return portalType
     */
    public static PortalType getPortalOfUserRoles() {
        if (checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN_PROVINCE.getValue(),
            RoleType.FULL_ADMIN.getValue()))) {
            return PortalType.ADMIN;
        }
        if (checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_BUSINESS.getValue(),
            RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.CUSTOMER_SUPPORT.getValue(), RoleType.FULL_DEV.getValue()))) {
            return PortalType.DEV;
        }
        return PortalType.SME;
    }

    public static String getCurrentBearerSMEToken() {
        CustomUserDetails customUserDetails = getCurrentUser();
        if (Objects.nonNull(customUserDetails)) {
            return customUserDetails.getSmeBearerToken();
        }
        return null;
    }

    public static String getCurrentSMERefreshToken() {
        CustomUserDetails customUserDetails = getCurrentUser();
        if (Objects.nonNull(customUserDetails)) {
            return customUserDetails.getSmeRefreshToken();
        }
        return null;
    }

    public static boolean isSuperAdmin() {
        return checkUserRoles(Collections.singletonList(RoleType.FULL_ADMIN.getValue()));
    }

    public static UUID getCurrentParentUUID() {
        CustomUserDetails userDetails = getCurrentUser();
        if (userDetails == null) {
            return null;
        }
        UUID parentSmeUUID = userDetails.getParentSmeUUID();
        if (Objects.equals(userDetails.getParentId(), -1L)) {
            return userDetails.getSmeUUID();
        }
        return parentSmeUUID;
    }

    public static String getCurrentApiKey() {
        CustomUserDetails userDetails = getCurrentUser();
        if (userDetails == null) {
            return null;
        }
        return userDetails.getApikey();
    }
}
