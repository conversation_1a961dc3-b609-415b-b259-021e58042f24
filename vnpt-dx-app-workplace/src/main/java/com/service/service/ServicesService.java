package com.service.service;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dto.categories.CategoriesApplicationDTO;
import com.dto.services.ApplicationCurrentResponseDTO;
import com.dto.services.ApplicationDetailResponseDTO;
import com.dto.services.ApplicationPopularResponseDTO;
import com.dto.services.ApplicationSearchResponseDTO;

public interface ServicesService {

    List<ApplicationSearchResponseDTO> getListWorkplace(String workplaceName);

    List<ApplicationSearchResponseDTO> getListWorkplaceLastSeen(String serviceName);

    List<CategoriesApplicationDTO> getLstCategoriesApplicationService(String name);

    Page<ApplicationSearchResponseDTO> findAllMyApp(String serviceName,List<Integer> status, String authorization, List<Long> supplierId , Pageable pageable);

    ApplicationDetailResponseDTO getAppDetail(Long id);

    Page<ApplicationSearchResponseDTO> getAllApplications(String serviceName, Pageable pageable);

    Page<ApplicationCurrentResponseDTO> getAllAppCurrent(List<Long> categoriesIds, List<Long> userIds, String serviceName, Pageable pageable);

    List<ApplicationPopularResponseDTO> getPopularApplication(Integer limit, String serviceName, Integer type);

    Long saveWorkplaceLastSeen(Long serviceId);
}
