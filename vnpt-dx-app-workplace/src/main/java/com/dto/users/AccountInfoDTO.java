package com.dto.users;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import com.onedx.common.constants.values.CharacterConstant;
import lombok.Data;

@Data
public class AccountInfoDTO {

    private Long id;

    private String username;

    private String smeUUID;

    private String nameWP;

    private List<String> lstPermission = new ArrayList<>();

    public AccountInfoDTO(IUserLogoutInfoDTO iUserLoginNameDTO) {
        BeanUtils.copyProperties(iUserLoginNameDTO, this, "lstPermission");
        if (Objects.nonNull(iUserLoginNameDTO.getLstPermission())) {
            this.lstPermission = Arrays.asList(iUserLoginNameDTO.getLstPermission().split(CharacterConstant.COMMA));
        }
    }

}
