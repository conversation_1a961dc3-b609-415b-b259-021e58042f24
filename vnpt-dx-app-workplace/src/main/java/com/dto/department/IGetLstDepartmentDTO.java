package com.dto.department;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;

public interface IGetLstDepartmentDTO {

    Long getId();

    String getName();

    String getCode();

    Long getManagerId();

    String getManagerName();

    String getManagerAvatar();

    String getManagerEmail();

    String getManagerPhone();

    String getManagerFirstName();

    Long getParentId();

    Integer getNumOfStaffs();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
    Date getLastModifiedAt();

    Integer getStatus();

    Boolean getIsFiltered();

}
