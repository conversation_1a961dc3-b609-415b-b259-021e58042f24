package com.entity.common.geoSync;

import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.dto.common.geoSync.DistrictInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Table(schema = "dx20_common", name = "district")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class District {

    @Id
    @Column(name = "_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long _id;
    @Column(name = "id")
    private Long id;
    @Column(name = "code")
    private String code;
    @Column(name = "name")
    private String name;
    @Column(name = "province_id")
    private Long provinceId;

    public District(DistrictInfoDTO dto, Long provinceId) {
        this.id = dto.getId();
        this.code = dto.getCode();
        this.name = dto.getName();
        this.provinceId = provinceId;
    }

    public int compareWith(District other) {
        if (!Objects.equals(getProvinceId(), other.getProvinceId())) {
            return (int) (getProvinceId() - other.getProvinceId());
        }
        return (int) (getId() - other.getId());
    }
}
