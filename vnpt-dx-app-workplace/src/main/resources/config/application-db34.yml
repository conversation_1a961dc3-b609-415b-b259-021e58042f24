spring:
  flyway:
    enabled: false
    url: *******************************************
    user: vnpt
    password: vnpt@654321
    schemas: vnpt_dev
  application:
    name: demo
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://************:5432/vnpt_dx?currentSchema=vnpt_dev
    username: vnptposgre
    password: vnpt@654321
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    show-sql: true
    hibernate:
      # Hibernate_sequence' doesn't exist
      use-new-id-generator-mappings: false
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        default_schema: vnpt_dev
        format_sql: false
  redis:
    host: ************
    port: 6379
    password: vnptredis@2021

  security:
    oauth2:
      client:
        registration:
          vnpt-idp:
            clientId: Alq21cWI_Pm0LqvlVNkmZG0ivMwa
            clientSecret: Pk3hbR0drmcbSoIMyVdldA6jVbWxRbE_Iy17Rlpl2XAa
            redirectUri: "https://workplace-staging.onesme.vn/oauth2/redirect/vnpt-idp"
            authorizationGrantType: authorization_code
            scope:
              - email
              - openId
        provider:
          vnpt-idp:
            authorizationUri: https://idp-sme.vnpt.vn/oauth2/authorize
            tokenUri: https://idp-sme.vnpt.vn/oauth2/token
            userInfoUri: https://idp-sme.vnpt.vn/oauth2/userinfo
            revoke-token-uri: https://idp-sme.vnpt.vn/oauth2/revoke

server:
  port: 8085
  prefix: DEV
  servlet:
    context-path: /workplace
web:
  host: https://stagingonedx.vnpt-technology.vn:6443
  workplace: https://workplace-staging.onesme.vn
batch:
  active: false
  limit:
    day:
      ev13: 1
  core_thread: 10
  max_thread: 10
  queue_capacity: 100

email:
  from: <EMAIL>
  password: Onedx@123!@
  core_thread_send_mail: 1
  max_thread_send_mail: 1
  queue_capacity_send_mail: 500
  core_thread_read_mail: 1
  max_thread_read_mail: 1
  queue_capacity_read_mail: 2

oneSme:
  url-api-login-endpoint: /auth-server/oauth/login
  url-api-verify-otp-endpoint: /auth-server/verify-otp-login
  url-api-profile-endpoint: /auth-server/api/users/profile
  url-api-verify-token-endpoint: /auth-server/verify-accessToken
  url-api-send-notif-login-first-time: /auth-server/api/users-sme/send-notif-login-first-time
  url-api-create-employee-endpoint: /auth-server/api/users-sme/smeemployees
  url-api-put-employee-endpoint: /auth-server/api/users-sme/employees/{id}
  url-api-get-detail-employee-endpoint: /auth-server/api/users-sme/employees/{id}
  url-api-delete-employee-endpoint: /auth-server/api/users-sme/{id}
  url-api-get-list-employee-endpoint: /auth-server/api/users-sme/get-list-employee
  url-api-get-basic-user-info-endpoint: /auth-server/api/users/get-basic-user-info
  url-api-get-list-basic-user-info-endpoint: /auth-server/api/users/get-list-basic-user-info
  url-api-create-department-endpoint: /api/user-portal/department/sme
  url-api-update-department-endpoint: /api/user-portal/department/{id}/sme
  url-api-detail-department-endpoint: /api/user-portal/department/{id}/sme
  url-api-update-status-department-endpoint: /api/user-portal/department/{id}/status/{status}/sme?isConfirm=%s
  url-api-get-list-tree-department-endpoint: /api/user-portal/tree-department
  url-api-delete-department-endpoint: /api/user-portal/department/{id}/sme?isConfirm=%s
  url-api-get-list-user-endpoint: /api/user-portal/department/sme/users?searchUser=%s&departmentSearch=%s&page=%s&size=%s&sort=%s
  url-api-register-employee-endpoint: /auth-server/api/users-sme/register
  url-api-import-employee-endpoint: /auth-server/api/users-sme/wp-import-employee
  url-api-change-status-employee-endpoint: /auth-server/api/users-sme/%s/status/%s
  url-api-invite-employee: /auth-server/api/users-sme/send-mail-invite-user
  url-api-check-exists-employee-email: /auth-server/api/users-sme/check-exists-employee-email?email=%s
  url-api-delete-list-employee: /auth-server/api/users-sme/delete-list-employee
  url-api-delete-avatar-user-login-endpoint: /auth-server/api/users-sme/delete-avatar
  url-api-delete-avatar-endpoint: /auth-server/api/users-sme/delete-avatar/%s
  url-api-update-profile-by-id-endpoint: /auth-server/api/users/%s
  url-api-update-business-endpoint: /auth-server/api/users/sme
  url-api-get-business-endpoint: /auth-server/api/users/business
  url-api-get-business-by-id-endpoint: /auth-server/api/users/%s/business?portalType=%s
  url-api-represent-endpoint: /auth-server/api/users/represent
  url-api-represent-by-id-endpoint: /auth-server/api/users/%s/represent
  url-api-get-list-address-endpoint: /api/sme-portal/address/getAddress/%s/%s
  url-api-address-detail-endpoint: /api/sme-portal/address/%s
  url-api-create-address-endpoint: /api/sme-portal/address
  url-api-get-default-address-endpoint: /api/sme-portal/address/getDefaultAddress/%s/%s
  url-api-create-employee-3rd-party-endpoint: /auth-server/api/users-sme/3rd-party/create-employee
  url-api-update-employee-3rd-party-endpoint: /auth-server/api/users-sme/3rd-party/update-employee
  url-api-cancel-subscription-endpoint: /api/sme-portal/subscription/pricing/cancel/%s
  url-api-get-list-categories-application-service: /api/portal/service/categories-application?name=%s
  url-api-call-transaction-one-sme: /auth-server/api/users-sme/transaction-onesme
  url-api-call-transaction-one-sme-all-sub: /auth-server/api/users-sme/transaction-onesme-all-sub
  url-api-update-business-name-endpoint: /auth-server/api/users/sme-name

config-auth:
  url: http://localhost:8082/auth-server
  client-id: vnpt_clientid
  client-secret: secret

config-workplace:
  url: http://localhost:8085/workplace
  client-id: vnpt_workplace
  client-secret: secret

dxservice:
  auth: http://localhost:8082
  service: http://localhost:8083

idg-config:
  access-key: TjTjKsdqVx6lUCl78ERe
  secret-key: TzPyeWMztNmqlepIH3CdBqBz6fbFG4H4rfcm9kqR
  endpoint: https://s3-vnpt-tech-dev.idg.vnpt.vn
  username: u4jm0BtyzgvmTSFh
  password: b9M7vfb1Wa4CL8e5MbET5dyk4JTNi8xy
  region: vn-north-ntl-cntt

workplace:
  vnptidp:
    clientId: Alq21cWI_Pm0LqvlVNkmZG0ivMwa
    clientSecret: Pk3hbR0drmcbSoIMyVdldA6jVbWxRbE_Iy17Rlpl2XAa
    passwordGrantType: password
    scope: openid
    adminName: admin
    adminPassword: v_}xhA!t@b52
    registerUri: https://gateway-onegov.vnpt.vn/admin/api/users/register
    tokenUri: https://gateway-onegov.vnpt.vn/oauth2/token
    changePasswordUri: https://gateway-onegov.vnpt.vn/admin/api/account/change-password
    changePasswordAdminUri: https://gateway-onegov.vnpt.vn/admin/api/account/reset-password
    activateUserUri: https://gateway-onegov.vnpt.vn/admin/api/users/activated
    deactivateUserUri: https://gateway-onegov.vnpt.vn/api/users/deactivated
    userInfoUri: http://gateway-onegov.vnpt.vn/admin/api/users
    updateUserUri: https://gateway-onegov.vnpt.vn/admin/api/users/login