package com.model.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.model.dto.UserDTO;
import com.model.entity.security.User;

@Mapper(componentModel = "spring", uses = {RoleMapper.class, UserFieldMapper.class})
public interface UserMapper extends EntityMapper<UserDTO, User> {

	@Mapping(source = "e.gender", target = "gender", qualifiedByName = "convertUserGender")
	UserDTO toDto(User e);
	
	@Mapping(source = "dto.gender", target = "gender", qualifiedByName = "convertGenderToDB")
	@Mapping(source = "phoneNumber", target = "phoneSmsGateway", qualifiedByName = "convertPhoneSmsGatewayToDB")
	User toEntity(UserDTO dto);
}
