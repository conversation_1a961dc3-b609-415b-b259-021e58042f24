package com.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR> HuyNV
 * @version    : 1.0
 * 05/02/2021
 */
@Entity
@Table(name = "business_size")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessSize implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
}
