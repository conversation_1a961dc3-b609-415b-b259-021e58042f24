package com.model.entity.actionHistory;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryObjectTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "action_history")
public class ActionHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    Long id;

    @Column(name = "action_code")
    String actionCode;

    @Column(name = "action_name")
    String actionName;

    @Column(name = "action_type")
    Integer actionType;

    @Column(name = "object_type")
    @NotNull
    Integer objectType;

    @Column(name = "object_id")
    @NotNull
    Long objectId;

    @Column(name = "content")
    String content;

    @Column(name = "created_at")
    @NotNull
    @CreatedDate
    LocalDateTime createdAt;

    @Column(name = "created_by")
    @NotNull
    Long createdBy;

    public ActionHistory(ActionHistoryTypeEnum actionType, ActionHistoryObjectTypeEnum objectType, Long objectId, Long createdBy,
        String content) {
        this.actionCode = actionType.getCode();
        this.actionName = actionType.getName();
        this.actionType = actionType.getType();
        this.objectType = objectType.getValue();
        this.objectId = objectId;
        this.createdAt = LocalDateTime.now();
        this.createdBy = createdBy;
        this.content = content;
    }
}
