package com.model.entity.customerContact;

import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import com.component.BaseEntity;
import org.hibernate.annotations.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.converter.ListConverter;
import com.onedx.common.converter.SetConverter;
import com.onedx.common.converter.SetLongConverter;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.dto.customerContact.ContactMessageDTO;
import com.onedx.common.dto.interestProduct.InterestProductDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Entity
@Table(name = "customer_contact")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@Getter
@Setter
@Slf4j
public class CustomerContact extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /*
     * Thông tin của doanh nghiệp
     */
    private String tin; // Mã số thuế doanh nghiệp
    private String name; // Tên doanh nghiệp
    private Long avatarFileId; // Id file avatar doanh nghiệp
    private String address; // Địa chỉ của doanh nghiệp
    private String phone; // Số điện thoại doanh nghiệp
    private String fax; // Số fax
    private String email; // Email của doanh nghiệp
    private Long businessAreaId; // Id ngành nghề kinh doanh
    @Transient
    private String businessAreaCode; // Mã ngành nghề kinh doanh
    private String businessAreaName; // Tên ngành nghề kinh doanh
    private Long streetId; // Id đường
    private String streetName; // Tên đường
    private Long wardId; // Id xã
    private String wardCode; // Mã xã
    private String wardName; // Tên xã
    private Long districtId; // Id huyện
    private String districtCode; // Mã huyện
    private String districtName; // Tên huyện
    private Long provinceId; // Id tỉnh
    private String provinceCode; // Mã tỉnh
    private String provinceName; // Tên tỉnh
    private Long regionId; // Id miền
    private String regionName; // Tên miền
    private Long nationId; // Id quốc gia
    @Transient
    private String nation; // Quốc gia
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date foundingDate; // Ngày thành lập
    private Long businessTypeId; // Id loại hình doanh nghiệp
    @Transient
    private String businessTypeName; // Tên loại hình doanh nghiệp
    @Transient
    private String businessTypeCode; // Mã loại hình doanh nghiệp
    private String businessRegistrationNo; // Số giấy phép ĐKKD
    private String businessRegistrationAddress; // Địa chỉ đăng ký doanh nghiệp
    @Convert(converter = SetLongConverter.class)
    private Set<Long> businessRegistrationFileIds; // Danh sách id các file đăng kí kinh doanh doanh nghiệp
    @Transient
    private String businessSize; // Quy mô
    private Long businessSizeId; // Id quy mô
    private String businessBlock; // Doanh nghiệp/ hộ gia đình
    private String taxDepartment; // Chi cục thuế
    private String setupAddress; // Địa chỉ lắp đặt
    private String socialInsuranceNo; // Số BHXH
    private String operator; // Nhà mạng: Vinaphone, Viettel, Mobifone
    private String generalDesc; // Giới thiệu chung doanh nghiệp
    private Integer status; // Trạng thái hoạt động doanh nghiệp
    private Integer archiveStatus = 0; // Trạng thái lưu trữ

    /*
     * Thông tin của người đại diện
     */
    private String repName; // Tên người đại diện
    private Long repAvatarFileId; // Id file avatar người đại diện doanh nghiệp
    private Integer repSex; // Giới tính
    private String repPosition; // Chức vụ
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date repBirthday; // Ngày sinh
    @Transient
    private String repNationality; // Quốc tịch
    private Long repNationalityId; // Id quốc tịch
    @Transient
    private String repFolk; // Dân tộc
    private Long repFolkId; // Id dân tộc
    private Long repIdentityType; // ID loại giấy chứng thực
    private String repIdentityNo; // Số chứng thực cá nhân
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date repIdentityDate; // Ngày cấp
    private String repIdentityAddress; // Nơi cấp
    private String repPermanentResidence; // Nơi đăng ký hộ khẩu
    private String repCurrentResidence; // Chỗ ở hiện tại
    private String repGeneralDesc; // Giới thiệu chung
    @Convert(converter = SetLongConverter.class)
    private Set<Long> repIdentityFileIds; // Danh sách id các file CMTND/CCCD của người đại diện doanh nghiệp

    /*
     * Thông tin của người liên hệ
     */
    private String contactName; // Tên người liên hệ
    private Long contactAvatarFileId; // Id file avatar liên hệ doanh nghiệp
    @Convert(converter = ListConverter.class)
    private List<String> contactPhones; // Danh sách số điện thoại liên hệ
    @Convert(converter = ListConverter.class)
    private List<String> contactEmails; // Danh sách email liên hệ
    @Convert(converter = SetConverter.class)
    private Set<String> contactSocialNetworkUrls; // Mạng xã hội
    private String contactPosition; // Chức vụ
    @Transient
    private String contactProvince; // Tỉnh thành
    private Long contactProvinceId; // ID tỉnh thành
    private String contactAddress; // Địa chỉ
    private String contactOrganization; // Tên tổ chức
    private String contactMessage; // Lời nhắn
    private String contactGeneralDesc; // Giới thiệu chung

    /*
     * Thông tin khác
     */
    private Integer createdSource; // Nguồn khách hàng
    private String referEmail; // Nhân viên/Khách hàng

    private Long assigneeId;

    @Type(type = "json")
    @Column(columnDefinition = "jsonb")
    private List<InterestProductDTO> lstInterestProduct;
    @Type(type = "json")
    @Column(columnDefinition = "jsonb")
    private List<ContactMessageDTO> lstMessage;
}
