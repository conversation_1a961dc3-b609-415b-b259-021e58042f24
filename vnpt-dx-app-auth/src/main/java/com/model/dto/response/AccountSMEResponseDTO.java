package com.model.dto.response;

import com.enums.CreateTypeEnum;
import com.onedx.common.constants.enums.Status;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0
 * 06/04/2021
 */
@Data
public class AccountSMEResponseDTO {

    @Schema(description = "Số điện thoại của developer", example = "**********")
    private String phoneNumber;

    @JsonProperty("firstname")
    @Schema(description = "Tên tài khoản", example = "Huy")
    private String firstName;

    @JsonProperty("lastname")
    @Schema(description = "Họ + tên đệm của tài khoản", example = "Trịnh")
    private String lastName;

    @Schema(description = "Email tài khoản", example = "<EMAIL>")
    private String email;

    @Schema(description = "Danh sách ID vai trò của tài khoản", example = "[3,6]")
    private Set<Long> roles;

    @Schema(description = "Trạng thái hoạt động của tài khoản", example = "ACTIVE|INACTIVE")
    private Status status;

    @Schema(description = "TechId", example = "1234")
    private String techId;

    @Schema(description = "Kiểu tạo tài khoản", example = "EXTERNAL")
    private CreateTypeEnum createType;

    @Schema(description = "Id", example = "1")
    private Long id;

    @Schema(description = "Địa chỉ", example = "50 Phạm Hùng")
    private String address;

    @Schema(description = "Id cha", example = "1")
    private Long parentId;
}
