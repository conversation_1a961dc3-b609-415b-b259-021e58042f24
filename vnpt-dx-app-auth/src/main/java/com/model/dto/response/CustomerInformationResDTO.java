package com.model.dto.response;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> DangNDH
 * @version : 1.0 10/06/2021
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerInformationResDTO {

    String customerCode;

    String company;

    String address;

    String tin;

    String businessAreaCode;

    String businessAreaName;

    String phoneNumber;

    String lastName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
    Date birthday;

    Integer gender;

    String email;

    Long provinceId;

    Long districtId;

    Long wardId;

    Long streetId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
    Date contractCreationDate;
}
