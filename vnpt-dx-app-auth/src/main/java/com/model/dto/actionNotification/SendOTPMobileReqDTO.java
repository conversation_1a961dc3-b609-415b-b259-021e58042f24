package com.model.dto.actionNotification;

import javax.validation.constraints.NotBlank;
import com.exception.MessageKeyConstant;
import com.enums.otp.ActionTypeOTPEnum;
import com.enums.otp.SendOTPTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SendOTPMobileReqDTO {

    private SendOTPTypeEnum sendType;
    private ActionTypeOTPEnum actionType;
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String username;
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String deviceId;
}