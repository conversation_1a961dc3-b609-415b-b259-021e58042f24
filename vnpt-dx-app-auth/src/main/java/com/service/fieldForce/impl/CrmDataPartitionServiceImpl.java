package com.service.fieldForce.impl;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.onedx.common.dto.base.CommonIdNameDTO;
import com.exception.ErrorKey;
import com.onedx.common.exception.ExceptionFactory;
import com.exception.Resources;
import com.model.entity.crm.CrmDataCondition;
import com.model.entity.crm.CrmDataPartition;
import com.model.entity.crm.CrmMappingUserPartition;
import com.repository.crm.CrmDataConditionRepository;
import com.repository.crm.CrmDataPartitionRepository;
import com.repository.crm.CrmMappingUserPartitionRepository;
import com.service.fieldForce.CrmDataPartitionService;

@Service
public class CrmDataPartitionServiceImpl implements CrmDataPartitionService {

    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private CrmDataPartitionRepository partitionRepository;
    @Autowired
    private CrmDataConditionRepository conditionRepository;
    @Autowired
    private CrmMappingUserPartitionRepository mappingUserPartitionRepository;

    @Override
    public CrmDataPartition findById(Long partitionId) {
        return partitionRepository.findById(partitionId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.DATA_PARTITION, ErrorKey.ID, String.valueOf(partitionId)));
    }

    @Override
    public void addAmId(Set<Long> lstAddAmPartitionId, Long userId) {
        if (lstAddAmPartitionId == null || lstAddAmPartitionId.isEmpty() || userId == null) {
            return;
        }
        lstAddAmPartitionId.forEach(partitionId -> {
            CrmDataPartition partition = findById(partitionId);
            Set<Long> lstAmId = Arrays.stream(partition.getLstAmId()).collect(Collectors.toSet());
            lstAmId.add(userId);
            partition.setLstAmId(lstAmId.toArray(new Long[0]));
            partitionRepository.save(partition);
        });
    }

    @Override
    public void removeAdminAmId(Set<Long> lstRemoveAmPartitionId, Long userId) {
        if (lstRemoveAmPartitionId == null || lstRemoveAmPartitionId.isEmpty() || userId == null) {
            return;
        }
        lstRemoveAmPartitionId.forEach(partitionId -> {
            CrmDataPartition partition = findById(partitionId);
            // Xóa khỏi danh sách admin
            Set<Long> lstAdminId = Arrays.stream(partition.getLstAdminId()).collect(Collectors.toSet());
            lstAdminId.remove(userId);
            partition.setLstAdminId(lstAdminId.toArray(new Long[0]));
            // Xóa khỏi danh sách am
            Set<Long> lstAmId = Arrays.stream(partition.getLstAmId()).collect(Collectors.toSet());
            lstAmId.remove(userId);
            partition.setLstAmId(lstAmId.toArray(new Long[0]));
            partitionRepository.save(partition);
        });
    }

    @Override
    public void addObjectId(Long partitionId, Integer objectType, Set<Long> lstNewObjectId) {
        CrmDataCondition condition = conditionRepository.findFirstByPartitionIdAndObjectType(partitionId, objectType)
            .orElse(new CrmDataCondition(partitionId, objectType));

        // Lưu thông tin lstObjectId của partition
        Set<Long> lstObjectId = new HashSet<>(Arrays.asList(condition.getLstObjectId()));
        lstObjectId.addAll(lstNewObjectId);
        condition.setLstObjectId(lstObjectId.toArray(new Long[0]));
        conditionRepository.save(condition);

        // Lưu thông tin mapping user-partition
        Set<CrmMappingUserPartition> lstMapping = lstNewObjectId.stream()
            .map(objectId -> mappingUserPartitionRepository.findById(objectId).orElse(new CrmMappingUserPartition(objectId, new Long[0])))
            .peek(mapping -> mapping.setLstPartitionId(appendArray(mapping.getLstPartitionId(), partitionId)))
            .collect(Collectors.toSet());
        mappingUserPartitionRepository.saveAll(lstMapping);
    }

    private Long[] appendArray(Long[] source, Long member) {
        Set<Long> lstMemberId = new HashSet<>(Arrays.asList(source));
        lstMemberId.add(member);
        return lstMemberId.toArray(new Long[0]);
    }

    @Override
    public List<CommonIdNameDTO> getAdminAmPartition(Long userId) {
        return partitionRepository.findAdminAmPartition(userId).stream().map(item -> new CommonIdNameDTO(item.getId(), item.getName()))
            .collect(Collectors.toList());
    }

    @Override
    public List<CommonIdNameDTO> getListPartition(Long userId) {
        return partitionRepository.findListPartition(userId).stream().map(item -> new CommonIdNameDTO(item.getId(), item.getName()))
            .collect(Collectors.toList());
    }

    @Override
    public List<CrmDataPartition> findAll() {
        return partitionRepository.findAll();
    }
}
