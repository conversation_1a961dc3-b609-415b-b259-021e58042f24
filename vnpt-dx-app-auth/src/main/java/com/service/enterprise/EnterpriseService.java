package com.service.enterprise;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.model.dto.enterprise.IEnterprisePopupDTO;
import com.model.entity.enterprise.Enterprise;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryTypeEnum;

/**
 * <AUTHOR> HuyNV
 * @version : 1.0 21/05/2021
 */
public interface EnterpriseService {

    /**
     * L<PERSON>u thông tin từ user sang enterprise
     */
    void sync(User sme, ActionHistoryTypeEnum actionHistoryTypeEnum);

    /**
     * Cập nhật mã số thuế
     *
     * @param oldTaxCode Mã số thuế trước khi thay đổi
     * @param newTaxCode Mã số thuế sau cần được cập nhật
     */
    void updateTin(String oldTaxCode, String newTaxCode);

    /**
     * Ad<PERSON> lấy danh sách khách hàng
     */
    Page<IEnterprisePopupDTO> getListEnterprise(String keyword, Boolean searchName, Boolean searchEmail, Boolean searchPhone,
        Boolean searchIdentity, CustomerTypeEnum customerType, Pageable pageable);

    /**
     * Đồng bộ thông tin giữa user với enterprise tương ứng
     *
     * @param sme        Thông tin user
     * @param enterprise Thông tin enterprise tương ứng user
     */
    void syncEnterpriseAndUser(User sme, Enterprise enterprise);
}
