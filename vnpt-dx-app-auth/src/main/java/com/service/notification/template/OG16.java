package com.service.notification.template;

import java.util.HashMap;
import com.enums.ActionNotificationEnum;
import com.model.dto.actionNotification.EmailParamDTO;
import com.service.notification.impl.ActionNotificationTemplateBase;

public class OG16 extends ActionNotificationTemplateBase {
    public OG16(String receiverEmail, String receiverName, String linkActive) {
        //Type
        this.actionNotificationEnum = ActionNotificationEnum.OG16;

        // Email
        EmailParamDTO emailParamDTO = new EmailParamDTO();
        HashMap<String, String> lstParam = emailParamDTO.getLstParam();
        emailParamDTO.setEmail(receiverEmail);

        lstParam.put("$USER", receiverName);
        lstParam.put("$LINK_ACTIVE", linkActive);

        this.lstEmailParam.add(emailParamDTO);
    }

}
