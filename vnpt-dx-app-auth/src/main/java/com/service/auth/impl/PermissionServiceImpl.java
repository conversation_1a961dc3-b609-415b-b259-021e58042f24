package com.service.auth.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import com.common.util.AuthUtil;
import com.exception.MessageKeyConstant;
import com.google.common.base.Objects;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.dto.rolePermission.IGetPermissionByPortal;
import com.onedx.common.dto.rolePermission.PermissionByPortalResDTO;
import com.onedx.common.repository.rolePermission.RoleRepository;
import com.repository.PermissionRepository;
import com.service.auth.PermissionService;

/**
 *
 * <AUTHOR>
 * @since 14/06/2021
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    RoleRepository roleRepository;

    public static final String VIEW_ROLE_ADMIN = "XEM_DANH_SACH_VAI_TRO_ADMIN_1";
    public static final String VIEW_ROLE_DEV = "XEM_DANH_SACH_VAI_TRO_DEVELOPER_1";
    public static final String VIEW_ROLE_SME = "XEM_DANH_SACH_VAI_TRO_SME_1";

    @Override
    public List<PermissionByPortalResDTO> getPermissionGroupByPortalType(PortalType portalType) {
        if ((PortalType.ADMIN.equals(portalType) && !AuthUtil.checkPermissionByCode(VIEW_ROLE_ADMIN)) ||
            (PortalType.DEV.equals(portalType) && !AuthUtil.checkPermissionByCode(VIEW_ROLE_DEV)) ||
            (PortalType.SME.equals(portalType) && !AuthUtil.checkPermissionByCode(VIEW_ROLE_SME))) {
            throw new AccessDeniedException(MessageKeyConstant.ACCESS_DENIED);
        }
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        List<Long> ids = roleRepository.findAllRoleByUserId(userLogin.getId());
        // List<PermissionByPortalResDTO> lstPer = permissionRepository.findAllByPortal(portalType.name(),ids);
        List<IGetPermissionByPortal> lstPermission = permissionRepository.getPermissionByPortal(portalType.name(), ids);
        Set<PermissionByPortalResDTO> lstPer = lstPermission.stream().map(PermissionByPortalResDTO::new).collect(Collectors.toSet());
        List<PermissionByPortalResDTO> result = new ArrayList<>();
        for (PermissionByPortalResDTO per : lstPer) {
            List<PermissionByPortalResDTO> lstChild = new ArrayList<>();
            for (PermissionByPortalResDTO perChild : lstPer) {
                if (Objects.equal(per.getId(), perChild.getParentId())) {
                    lstChild.add(perChild);
                }
            }
            per.setSubPermissions(lstChild);
            if (per.getParentId() == null || per.getParentId() < 0) {
                result.add(per);
            }
        }

        return result;
    }

    @Override
    public Map<PortalType, List<PermissionByPortalResDTO>> getAllPermissionGroupByPortalType() {
        PortalType portalLogin = AuthUtil.getPortalOfUserRoles();
        if (!PortalType.ADMIN.equals(portalLogin)) {
            throw new AccessDeniedException(MessageKeyConstant.ACCESS_DENIED);
        }
        List<PermissionByPortalResDTO> lstPer = permissionRepository.findAllPermissionGroupPortal();
        Map<PortalType, List<PermissionByPortalResDTO>> result = new HashedMap<>();
        for (PortalType portalType : PortalType.values()) {
            result.put(portalType, new ArrayList<>());
        }
        for (PermissionByPortalResDTO per : lstPer) {
            List<PermissionByPortalResDTO> lstChild = new ArrayList<>();
            for (PermissionByPortalResDTO perChild : lstPer) {
                if (Objects.equal(per.getId(), perChild.getParentId())
                        && Objects.equal(per.getPortalName(), perChild.getPortalName())) {
                    lstChild.add(perChild);
                }
            }
            per.setSubPermissions(lstChild);
            if (per.getParentId() == null || per.getParentId() < 0) {
                result.get(PortalType.valueOf(per.getPortalName())).add(per);
            }
        }

        return result;
    }

}
