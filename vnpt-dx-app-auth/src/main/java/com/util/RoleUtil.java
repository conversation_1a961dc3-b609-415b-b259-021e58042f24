package com.util;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.security.core.GrantedAuthority;

/**
 * <AUTHOR> SonNH
 * @version    : 1.0
 * 30/1/2021
 */
public class RoleUtil {
    public static boolean checkRole(Collection<? extends GrantedAuthority> authorities, List<String> roles) {
        AtomicBoolean isRoleDev = new AtomicBoolean(false);
        authorities.forEach(i -> {
            if (roles.contains(i.getAuthority())) {
                isRoleDev.set(true);
            }
        });
        return isRoleDev.get();
    }
}
