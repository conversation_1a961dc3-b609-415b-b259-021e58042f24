package com.util;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.values.RoleConst;
import com.onedx.common.entity.security.Role;
import com.onedx.common.dto.enterprise.CustomerConvertDTO;
import com.onedx.common.dto.enterprise.CustomerInfoDHSXDTO;
import com.onedx.common.dto.enterprise.CustomerInfoReqDTO;
import com.onedx.common.dto.enterprise.PersonalInfoDHSXDTO;
import com.model.entity.enterprise.Enterprise;
import com.model.entity.security.User;
import com.onedx.common.repository.rolePermission.RoleRepository;
import com.onedx.common.utils.HttpRestUtil;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class EnterpriseUtil {

    @Autowired
    private HttpRestUtil httpUtil;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private RoleRepository roleRepository;

    // api lấy thông tin khách hàng trên ĐHSXKD
    @Value("${dhsxkd.url-api-customer-info}")
    private String dhsxkhApiCustomerInfo; // api lấy thông tin khách hàng trên ĐHSXKD
    @Value("${dhsxkd.url-api-personal-info}")
    private String dhsxkhApiPersonalInfo;
    @Value("${dhsxkd.header.token.key}")
    private String tokenKey;
    @Value("${dhsxkd.header.token.value}")
    private String tokenValue;
    @Value("${dhsxkd.header.acc.key}")
    private String accKey;
    @Value("${dhsxkd.header.acc.value}")
    private String accValue;
    @Value("${dhsxkd.header.pwd.key}")
    private String pwdKey;
    @Value("${dhsxkd.header.pwd.value}")
    private String pwdValue;
    @Value(("${dhsxkd.timeout}"))
    private int timeout;

    @Async(value = "asyncExecutorUpdateCustomerCode")
    public void updateCustomerCode(User user) {
        log.info("Start update CustomerCode");
        //Set thông tin đầu vào cho api gọi dhsxkd lấy thông tin khác hàng
        CustomerInfoReqDTO customerInfoReqDTO = new CustomerInfoReqDTO();
        customerInfoReqDTO.setProvinceId(user.getProvinceId());
        customerInfoReqDTO.setTaxCode(user.getCorporateTaxCode());
        customerInfoReqDTO.setRepPersonalCertNumber(user.getRepPersonalCertNumber());
        user.setCustomerCode(getCustomerCode(user.getCustomerType(), customerInfoReqDTO));
        log.info("End update CustomerCode");
    }

    /**
     * Cập nhật thông tin nguồn tạo khách hàng
     * Mặc định khi tạo mới là Đăng ký trên OneSme (10)
     * Nếu tồn tại mã số thuế trên dhsxkd là Đăng ký trên OneSME & có thông tin trên ĐHSXKD (11)
     */
    public String getCustomerCodeUserFromDHSXKD(Enterprise enterprise) {
        //Set thông tin đầu vào cho api gọi dhsxkd lấy thông tin khác hàng
        CustomerInfoReqDTO customerInfoReqDTO = new CustomerInfoReqDTO();
        customerInfoReqDTO.setProvinceId(enterprise.getProvinceId());
        customerInfoReqDTO.setTaxCode(enterprise.getTin());
        customerInfoReqDTO.setRepPersonalCertNumber(enterprise.getRepIdentityNo());
        return getCustomerCode(enterprise.getCustomerType(), customerInfoReqDTO);
    }

    private String getCustomerCode(String customerType, CustomerInfoReqDTO customerInfoReqDTO) {
        CustomerConvertDTO data = new CustomerConvertDTO();
        BeanUtils.copyProperties(customerInfoReqDTO, data);
        //Nếu là khách hàng cá nhân gọi api sme_tracuu_thongtin_khcn
        //Nếu là khách hàng doanh nghiệp gọi api sme_tracuu_thongtin_kh
        HttpHeaders headers = createEntityRequest();
        if (Objects.equals(CustomerTypeEnum.PERSONAL.getValue(), customerType)) {
            PersonalInfoDHSXDTO personalInfoDHSXDTO = httpUtil.callRest(dhsxkhApiPersonalInfo, HttpMethod.POST, headers, data,
                PersonalInfoDHSXDTO.class, timeout);
            if (Objects.nonNull(personalInfoDHSXDTO) && Objects.nonNull(personalInfoDHSXDTO.getErrorCode()) &&
                personalInfoDHSXDTO.getErrorCode().equals(0)
                && Objects.nonNull(personalInfoDHSXDTO.getMessage()) && !personalInfoDHSXDTO.getMessage().isEmpty()) {
                return personalInfoDHSXDTO.getMessage().get(personalInfoDHSXDTO.getMessage().size() - 1).getCustomerCode();
            }
        } else {
            CustomerInfoDHSXDTO customerInfoDHSXKDResDTO = httpUtil.callRest(dhsxkhApiCustomerInfo, HttpMethod.POST, headers, data,
                CustomerInfoDHSXDTO.class, timeout);
            if (Objects.nonNull(customerInfoDHSXKDResDTO) && customerInfoDHSXKDResDTO.getErrorCode().equals(0)
                && Objects.nonNull(customerInfoDHSXKDResDTO.getMessage()) && !customerInfoDHSXKDResDTO.getMessage().isEmpty()) {
                return customerInfoDHSXKDResDTO.getMessage().get(customerInfoDHSXKDResDTO.getMessage().size() - 1).getCustomerCode();
            }
        }
        return null;
    }

    public User createUserFromEnterprise(Enterprise enterprise) {
        String passRandom = RandomStringUtils.random(10, true, true);
        User user = new User();
        BeanUtils.copyProperties(enterprise,user);
        user.setParentId(-1L);
        user.setPassword(passwordEncoder.encode(passRandom));
        user.setRepPersonalCertNumber(enterprise.getRepIdentityNo());
        user.setCorporateTaxCode(enterprise.getTin());
        user.setActivationKey(RandomStringUtils.random(20, true, true));
        Set<Role> roles = new HashSet<>();
        roleRepository.findById(RoleConst.ROLE_SME).ifPresent(roles::add);
        user.setRoles(roles);
        user.setUserCode(generateUserCode());
        user.setModifiedAt(LocalDateTime.now());
        if (Objects.equals(user.getPhoneNumber(), "")) {
            user.setPhoneNumber(null);
        }
        return user;
    }

    private String generateUserCode() {
        long now = new Date().getTime();
        String time = Long.toString(now);
        int size = time.length();
        return String.format("%s-%s-%s-%s",
            time.substring(size - 13, size - 9),
            time.substring(size - 9, size - 5),
            time.substring(size - 5, size - 2),
            time.substring(size - 2, size));
    }

    private HttpHeaders createEntityRequest() {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set(tokenKey, tokenValue);
        headers.set(accKey, accValue);
        headers.set(pwdKey, pwdValue);
        return headers;
    }
}
