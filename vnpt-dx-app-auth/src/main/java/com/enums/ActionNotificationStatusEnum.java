package com.enums;

import java.util.HashMap;
import java.util.Map;

public enum ActionNotificationStatusEnum {
	ON(1), OFF(0);

	public final int action;

	ActionNotificationStatusEnum(int action) {
		this.action = action;
	}

	private static final Map<Integer, ActionNotificationStatusEnum> map = new HashMap<>();

	static {
		for (ActionNotificationStatusEnum action : ActionNotificationStatusEnum.values()) {
			map.put(action.action, action);
		}
	}

	public static ActionNotificationStatusEnum valueOf(int action) {
		return map.get(action);
	}
}
