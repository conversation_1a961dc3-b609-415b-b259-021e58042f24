package com.onedx.common.entity.notification;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Date;

/**
 * Bảng quản lý hành động thông báo
 */
/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 18/3/2021
 */
@Entity
@Table(name = "action_notification")
@Data
public class ActionNotification implements Serializable {

	/**
	 * Mã định danh
	 */
	@Id
	@Column(name = "id", nullable = false)
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Schema(description = "Mã hành động", example = "2")
	private Long id;

	@Column(name = "action_code")
	@Schema(description = "Mã hành động", example = "TK-11")
	private String actionCode;

	/**
	 * Tên hành động thông báo
	 */
	@Column(name = "name")
	@Schema(description = "Tên hành động thông báo", example = "Tạo tài khoản Admin")
	private String name;

	/**
	 * Trạng thái gửi Email (1: Cho phép gửi Email 0: Không cho phép gửi Email)
	 */
	@Column(name = "is_send_email")
	@Schema(description = "Trạng thái gửi Email", example = "ON|OFF")
	private Integer isSendEmail;

	/**
	 * Trạng thái gửi SMS (1: Cho phép gửi SMS 0: Không cho phép gửi SMS)
	 */
	@Column(name = "is_send_sms")
	@Schema(description = "Trạng thái gửi SMS", example = "ON|OFF")
	private Integer isSendSms;

	/**
	 * Trạng thái gửi thông báo (1: Cho phép gửi thông báo 0: Không cho phép gửi thông báo)
	 */
	@Column(name = "is_notification")
	@Schema(description = "Trạng thái gửi thông báo", example = "ON|OFF")
	private Integer isNotification;

	@Column(name = "allow_change_email")
	@Schema(description = "Trạng thái cho phép gửi Email", example = "A")
	private String allowChangeEmail;

	@Column(name = "allow_change_sms")
	@Schema(description = "Trạng thái cho phép gửi SMS", example = "A")
	private String allowChangeSMS;

	@Column(name = "allow_change_notification")
	@Schema(description = "Trạng thái cho phép gửi thông báo", example = "A")
	private String allowChangeNotif;

	/**
	 * Trạng thái gửi thông báo (1: Cho phép gửi thông báo 0: Không cho phép gửi thông báo)
	 */
	@Column(name = "receiver")
	@Schema(description = "Tên nhóm người nhận được thông báo/ SMS/ Email", example = "Giáo dục")
	private String receiver;

	/**
	 * Mã action cha
	 */
	@Column(name = "parent_id", nullable = false)
	@Schema(description = "Mã hành động cha", example = "1")
	private Long parentId;

	/**
	 * Người tạo bản ghi
	 */
	@Column(name = "created_by")
	@Schema(description = "Id người tạo bản ghi", example = "1")
	private String createdBy;

	/**
	 * Ngày tạo bản ghi
	 */
	@Column(name = "created_at")
	@Schema(description = "Ngày tạo bản ghi", example = "01/01/2021")
	private Date createdAt;

	/**
	 * Người sửa bản ghi cuối cùng
	 */
	@Column(name = "modified_by")
	@Schema(description = "Người sửa bản ghi cuối cùngi", example = "1")
	private String modifiedBy;

	/**
	 * Ngày sửa bản ghi cuối cùng
	 */
	@Column(name = "modified_at")
	@Schema(description = "Ngày sửa bản ghi cuối cùng", example = "01/01/2021")
	private Date modifiedAt;

    /**
     * Sắp xếp thứ tự
     */
    @Column(name = "priority_order")
    @Schema(description = "Sắp xếp thứ tự", example = "1001")
    private Integer priorityOrder;

	/**
	 * Trạng thái gửi Telegram (1: Cho phép gửi Telegram 0: Không cho phép gửi Telegram)
	 */
	@Column(name = "is_send_telegram")
	@Schema(description = "Trạng thái gửi Telegram", example = "ON|OFF")
	private Integer isSendTelegram;

	@Column(name = "allow_change_telegram")
	@Schema(description = "Trạng thái cho phép gửi telegram", example = "A")
	private String allowChangeTelegram;

}
