package com.onedx.common.dto.integration.backend.subscription;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * * Integration Sub Update DTO
 *
 * <AUTHOR>
 * @since 6/24/2021 2:19 PM
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntegrationSubSwapPricingDTO {

    @Schema(description = SwaggerConstant.Subscription.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    private Long quantity;

    private List<CouponSubReqDTO> couponList = new ArrayList<>();

    private List<CouponSubReqDTO> couponPricings = new ArrayList<>();

    private List<AddonSubReqDTO> addonsList = new ArrayList<>();

}
