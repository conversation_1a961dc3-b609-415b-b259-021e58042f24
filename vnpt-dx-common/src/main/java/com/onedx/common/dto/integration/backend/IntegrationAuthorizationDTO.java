
package com.onedx.common.dto.integration.backend;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 * 6/1/2021
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegrationAuthorizationDTO {

    @Schema(description = "token one SME")
    String token;

    @Schema(description = "refresh token")
    String refresh_token;

    @Schema(description = "thời gian hết hạn")
    Integer expired_in;

    @Schema(description = "email của user")
    String email;

    @Schema(description = "scope")
    String scope;
}