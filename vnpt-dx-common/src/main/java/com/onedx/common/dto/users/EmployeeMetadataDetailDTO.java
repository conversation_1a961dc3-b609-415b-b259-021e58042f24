package com.onedx.common.dto.users;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeMetadataDetailDTO {
    String position;
    @Schema(description = "Thời gian bắt đầu làm việc", example = "22/08/2023")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy", timezone = DateUtil.TIME_ZONE)
    private Date startWorkDate;
    @Schema(description = "Thời gian kết thúc làm việc", example = "22/08/2024")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy", timezone = DateUtil.TIME_ZONE)
    private Date endWorkDate;
    @Schema(description = "Ghi chú")
    private String note;
}
