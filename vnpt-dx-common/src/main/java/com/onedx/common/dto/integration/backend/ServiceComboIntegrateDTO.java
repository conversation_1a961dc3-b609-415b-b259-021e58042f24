package com.onedx.common.dto.integration.backend;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 * 7/6/2021
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServiceComboIntegrateDTO {

    Long pricingId;

    String pricingName;

    String serviceType;

    String serviceName;

    Long freeQuantity;

    Long quantity;

    Long usedQuantity;

    String unitName;
}
