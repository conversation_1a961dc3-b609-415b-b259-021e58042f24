package com.onedx.common.constants.enums.customFields;

public enum EntityTypeEnum {
    SERVICE(0),
    COMBO(1),
    PRICING(2),
    COMBO_PLAN(3),
    ADDON(4),
    SUBSCRIPTION(5),
    CUSTOMER(6),
    CUSTOMER_CONTACT(7),
    CUSTOM_FEE(8),
    PACKAGE(9);

    private final Integer value;

    EntityTypeEnum(Integer value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static EntityTypeEnum valueOf(Integer value) {
        if (value == null) {
            return null;
        }
        switch (value) {
            case 0:
                return SERVICE;
            case 1:
                return COMBO;
            case 2:
                return PRICING;
            case 3:
                return COMBO_PLAN;
            case 4:
                return ADDON;
            case 5:
                return SUBSCRIPTION;
            case 6:
                return CUSTOMER;
            case 7:
                return CUSTOMER_CONTACT;
            default:
                return null;
        }
    }

    public static Integer getValueFromName(String name) {
        switch (name) {
            case "SERVICE":
                return 0;
            case "COMBO":
                return 1;
            case "PRICING":
                return 2;
            case "COMBO_PLAN":
                return 3;
            case "ADDON":
                return 4;
            case "SUBSCRIPTION":
                return 5;
            case "CUSTOMER":
                return 6;
            case "CUSTOMER_CONTACT":
                return 7;
            default:
                return null;
        }
    }
}
