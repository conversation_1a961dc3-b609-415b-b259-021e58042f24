package com.onedx.common.constants.values;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CharacterConstant {

    public static final String DOT = ".";
    public static final String UNDERLINED = "_";
    public static final String SPACE = " ";
    public static final String COMMA = ",";
    public static final String SINGLE_QUOTE = "'";
    public static final String BLANK = "";
    public static final String COLON = ":";
    public static final String SEMICOLON = ";";
    public static final String HYPHEN = "-";
    public static final String HYPHEN_EXTRA = " - ";
    public static final String NO_SELECT = "Không chọn";
    public static final String PERCENT = "%";
    public static final String BACK_SLASH = "\\";
    public static final String SLASH = "/";
    public static final String VERTICAL_BAR = "|";
    public static final String QUESTION_MARK = "?";
    public static final String AND = "&";
    public static final String LEFT_SQUARE_BRACKET = "[";
    public static final String RIGHT_SQUARE_BRACKET = "]";
    public static final String VALUE = "4000-12-12";
    public static final String NEW_LINE = "\\n";
    public static final String BR = "<br>";
    public static final String DUAL_QUOTE = "\"";
    public static final String BEARER_PREFIX = "Bearer ";
    public static final String DEFAULT_VALUE = "-1";
    public static final String BATCH_PREFIX = "batch-";
    public static final String CLIENT_ID = "DX-CLIENT-ID";
    public static final String CLIENT_TOKEN = "DX-CLIENT-TOKEN";
    public static final String X_API_KEY = "X-API-KEY";
    public static final String ONESME = "onesme";
    public static final String MASK_5 = "*****"; // Mask có độ dài 5 kí tự để che giấu thông tin cần bảo mật
}
