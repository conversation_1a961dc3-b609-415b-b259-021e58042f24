package com.onedx.common.constants.sql;

/**
 * <AUTHOR> 09/07/2021
 *
 */
public final class SQLApi {

    public static final String SEARCH_LIST_API_BY_ROLE_ID =
            "SELECT " +
            "    distinct a.api_code AS apiCode " +
            "FROM " +
            "          {h-schema}\"role\" r " +
            "JOIN      {h-schema}roles_permissions rp ON " +
            "     r.id = rp.role_id " +
            "JOIN ( " +
            "     SELECT " +
            "          p2.* " +
            "     FROM " +
            "               {h-schema}\"permission\" p " +
            "     JOIN      {h-schema}\"permission\" p2 ON " +
            "          p.id = p2.parent_id) AS p ON " +
            "     p.id = rp.permission_id " +
            "join {h-schema}permission_portal on permission_portal.permission_id = p.id \n" +
            "JOIN      {h-schema}api_permission ap ON " +
            "     permission_portal.id = ap.permission_portal_id \n" +
            "         and COALESCE(ap.map_permission_portal::::integer, 0) = 1 and COALESCE(ap.delete_flag::::integer, 1) = 1 \n" +
            "JOIN      {h-schema}apis a ON " +
            "     a.id = ap.api_id " +
            "WHERE " +
            "     r.deleted_flag = 1 " +
            "     AND r.status = 1 " +
            "     AND r.id IN (:roleIds)";

    public static final String DELETE_HISTORY_KAFKA =
        "UPDATE {h-schema}kafka_history "
            + "SET deleted_flag = 0 "
            + "WHERE id = :id ";

    public static final String GET_LIST_CUSTOMER_AUTO_MIGRATE =
        "SELECT h.* "
            + "FROM {h-schema}history_call_api_migrate h "
            + "WHERE h.api_url = :key "
            + "   AND h.api_status = :status";

}
