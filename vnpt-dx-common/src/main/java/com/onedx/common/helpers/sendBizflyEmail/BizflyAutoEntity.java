package com.onedx.common.helpers.sendBizflyEmail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class BizflyAutoEntity {
    private String appKey;
    private String token;
    private String projectToken;
    private String uuid;
    private String emails = "";
    private String khname = "";
    private String code = "";

    @JsonProperty("app_key")
    public String getAppKey() {
        return appKey;
    }

    @JsonProperty("project_token")
    public String getProjectToken() {
        return projectToken;
    }
}
