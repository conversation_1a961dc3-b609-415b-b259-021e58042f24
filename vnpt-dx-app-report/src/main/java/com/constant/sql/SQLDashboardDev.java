package com.constant.sql;

public final class SQLDashboardDev {
    
    public static final String GET_PREVIEW_CUSTOMER_CTE = 
        "with pricing_service as ( \n" +
            "    select \n" +
            "        mPricing.id as pricing_id, \n" +
            "        mPricing.pricing_name, \n" +
            "        mService.service_name, \n" +
            "        mService.user_id as developer_id,\n" +
            "        mService.service_owner, \n" +
            "        case \n" +
            "            when mPricing.cycle_type = 0 then concat (mPricing.payment_cycle, ' ngày') \n" +
            "            when mPricing.cycle_type = 1 then concat (mPricing.payment_cycle, ' tuần') \n" +
            "            when mPricing.cycle_type = 2 then concat (mPricing.payment_cycle, ' tháng') \n" +
            "            when mPricing.cycle_type = 3 then concat (mPricing.payment_cycle, ' năm') \n" +
            "            else '' \n" +
            "        end as payment_cycle \n" +
            "    from {h-schema}pricing mPricing \n" +
            "        left join {h-schema}services mService on mService.id = mPricing.service_id \n" +
            "    where \n" +
            "        mPricing.deleted_flag = 1 and \n" +
            "        mService.deleted_flag = 1 and \n" +
            "        mService.approve = 1 \n" +
            "), pricing_multi_plan_info as ( \n" +
            "    select \n" +
            "        mPricingPlan.id, \n" +
            "        case \n" +
            "            when mPricingPlan.payment_cycle = -1 then '' --Gói 1 lần \n" +
            "            when mPricingPlan.circle_type = 0 then concat (mPricingPlan.payment_cycle, ' ngày') \n" +
            "            when mPricingPlan.circle_type = 1 then concat (mPricingPlan.payment_cycle, ' tuần') \n" +
            "            when mPricingPlan.circle_type = 2 then concat (mPricingPlan.payment_cycle, ' tháng') \n" +
            "            when mPricingPlan.circle_type = 3 then concat (mPricingPlan.payment_cycle, ' năm') \n" +
            "            else '' \n" +
            "        end as payment_cycle \n" +
            "   from {h-schema}pricing_multi_plan mPricingPlan \n" +
            "   where mPricingPlan.deleted_flag = 1 \n" +
            "), combo_plan_combo as ( \n" +
            "    select \n" +
            "        mComboPlan.combo_name as combo_plan_name, \n" +
            "        mComboPlan.id as combo_plan_id, \n" +
            "        mCombo.combo_name, \n" +
            "        mCombo.user_id as developer_id,\n" +
            "        mCombo.id as combo_id,\n" +
            "        mCombo.combo_owner, \n" +
            "        case \n" +
            "            when mComboPlan.payment_cycle = -1 then '' --Gói 1 lần \n" +
            "            when mComboPlan.cycle_type = 0 then concat (mComboPlan.payment_cycle, ' ngày') \n" +
            "            when mComboPlan.cycle_type = 1 then concat (mComboPlan.payment_cycle, ' tuần') \n" +
            "            when mComboPlan.cycle_type = 2 then concat (mComboPlan.payment_cycle, ' tháng') \n" +
            "            when mComboPlan.cycle_type = 3 then concat (mComboPlan.payment_cycle, ' năm') \n" +
            "            else '' \n" +
            "        end as payment_cycle \n" +
            "   from {h-schema}combo_plan mComboPlan \n" +
            "     left join {h-schema}combo mCombo on mCombo.id = mComboPlan.combo_id \n" +
            "  where \n" +
            "    mComboPlan.deleted_flag = 1 and \n" +
            "    mCombo.deleted_flag = 1 and \n" +
            "    mCombo.approve = 1 \n" +
            "), user_province as ( \n" +
            "    select \n" +
            "        mUser.id as user_id, \n" +
            "        mUser.email, \n" +
            "        case \n" +
            "            when mUser.customer_type = 'CN' then concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
            "            else mUser.name \n" +
            "        end as name, \n" +
            "        case \n" +
            "            when mUser.customer_type = 'CN' then mUser.rep_personal_cert_number \n" +
            "            else mUser.tin \n" +
            "        end as identity_num, \n" +
            "        mProvince.name as province_name, \n" +
            "        mUser.created_at \n" +
            "    from {h-schema}users mUser \n" +
            "        left join {h-schema}province mProvince on mProvince.id = mUser.province_id \n" +
            "), result_customer as ( \n" +
            "    select \n" +
            "        distinct mSub.id, \n" +
            "        mUser.province_name as provinceName, \n" +
            "        mUser.email as email, \n" +
            "        mUser.name AS enterpriseName, \n" +
            "        mUser.identity_num as identityNum, \n" +
            "        coalesce(mService.service_name, mCombo.combo_name) as servicename, \n" +
            "        coalesce(mService.pricing_name, mCombo.combo_plan_name) as pricingname, \n" +
            "        case \n" +
            "            when mCombo.combo_id is not null then 'COMBO' \n" +
            "            when coalesce(mService.service_owner, mCombo.combo_owner) = any(array[0, 1]) then 'ON' \n" +
            "            else 'OS' \n" +
            "        end as serviceType, \n" +
            "        case \n" +
            "            when mSub.is_one_time = 0 then '' \n" +
            "            when mSub.pricing_multi_plan_id is not null then mPricingPlan.payment_cycle \n" +
            "            else coalesce(mService.payment_cycle, mCombo.payment_cycle) \n" +
            "        end as paymentCycle, \n" +
            "        case \n" +
            "            when mSub.is_one_time = 0 then '' \n" +
            "            when mSub.number_of_cycles is null then 'Không giới hạn' \n" +
            "            when mSub.number_of_cycles < 1 then 'Không giới hạn' \n" +
            "            else cast(mSub.number_of_cycles as varchar) \n" +
            "        end as numberOfCycle, \n" +
            "        to_char(mSub.created_at, 'dd/MM/yyyy') as registeredAt, \n" +
            "        to_char(mSub.started_at, 'dd/MM/yyyy') as startedAt, \n" +
            "        case \n" +
            "            when mSub.next_payment_time is null then '' \n" +
            "            else to_char(mSub.next_payment_time, 'dd/MM/yyyy') \n" +
            "        end as nextPaymentTime, \n" +
            "        to_char(mUser.created_at, 'dd/MM/yyyy') as createdAt, \n" +
            "        case \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = -1 \n" +
            "                then 'Không xác định' \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = 0 \n" +
            "                then 'Đang chờ' \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = 1 \n" +
            "                then 'Dùng thử' \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = 2 \n" +
            "                then 'Hoạt động' \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = 3 \n" +
            "                then 'Đã hủy' \n" +
            "            when (mSub.combo_plan_id is not null or mService.service_owner = 0 or mService.service_owner = 1) and mSub.status = 4 \n" +
            "                then 'Kết thúc' \n" +
            "            when mSmeProgress.id is null then 'Đặt hàng thành công' \n" +
            "            when mSmeProgress.order = 1 then 'Tiếp nhận đơn hàng' \n" +
            "            when mSmeProgress.order = 2 then 'Đang triển khai' \n" +
            "            when mSmeProgress.order = 3 then 'Hoàn thành' \n" +
            "            when mSmeProgress.order = 4 then 'Hủy đơn hàng' \n" +
            "            else 'Không xác định' \n" +
            "        end as subStatus \n" +
            "    from {h-schema}subscriptions mSub \n" +
            "        left join pricing_service mService on mService.pricing_id = mSub.pricing_id \n" +
            "        left join combo_plan_combo mCombo on mCombo.combo_plan_id = mSub.combo_plan_id \n" +
            "        left join user_province mUser on mUser.user_id = mSub.user_id \n" +
            "        left join pricing_multi_plan_info mPricingPlan on mPricingPlan.id = mSub.pricing_multi_plan_id \n" +
            "        left join {h-schema}order_service_receive mOrderReceive on mOrderReceive.subscription_id = mSub.id \n" +
            "        left join {h-schema}order_service_status mOrderStatus on mOrderStatus.id = cast(mOrderReceive.order_status as bigint) \n" +
            "        left join {h-schema}sme_progress mSmeProgress on mSmeProgress.id = mOrderStatus.sme_progress_id \n" +
            "    where \n" +
            "        --Điều kiện subscription\n" +
            "        mSub.deleted_flag = 1 and mSub.confirm_status = 1 and \n" +
            "        --Điều kiện lọc\n" +
            "        ( :currentDevId = -1 or :currentDevId = coalesce(mService.developer_id, mCombo.developer_id)) and \n" +
            "        ( \n" +
            "            cast('1970-01-01' as date) = cast(:startDate as date) or \n" +
            "            cast('3000-01-01' as date) = cast(:endDate as date) or \n" +
            "            (cast(mSub.created_at as date) between cast(:startDate as date) and cast(:endDate as date)) \n" +
            "        ) \n" +
            "    order by mSub.id desc \n" +
            ") \n";
    
    public static final String GET_PREVIEW_CUSTOMER_SELECT =
        GET_PREVIEW_CUSTOMER_CTE + 
            "select * from result_customer";

    public static final String GET_PREVIEW_CUSTOMER_COUNT=
        GET_PREVIEW_CUSTOMER_CTE +
            "select count(1) from result_customer";
    
    public static final String GET_EXPORT_CUSTOMER_SELECT =
        GET_PREVIEW_CUSTOMER_CTE +
            "select \n" +
            "    cast(row_number() over() as varchar), \n" +
            "    provinceName, \n" +
            "    email, \n" +
            "    enterpriseName, \n" +
            "    identityNum, \n" +
            "    servicename, \n" +
            "    pricingname, \n" +
            "    serviceType, \n" +
            "    paymentCycle, \n" +
            "    numberOfCycle, \n" +
            "    concat(' ', registeredAt), \n" +
            "    concat(' ', startedAt),\n" +
            "    concat(' ', nextPaymentTime), \n" +
            "    concat(' ', createdAt), \n" +
            "    subStatus \n" +
            "from result_customer";
    
    public static final String GET_OVERVIEW_CUSTOMER_BY_INTERVAL =
        "with pricing_service as ( \n" +
            "    select \n" +
            "        mPricing.id as pricing_id, \n" +
            "        mService.user_id as developer_id \n" +
            "    from {h-schema}pricing mPricing \n" +
            "        left join {h-schema}services mService on mService.id = mPricing.service_id \n" +
            "    where \n" +
            "        mPricing.deleted_flag = 1 and \n" +
            "        mService.deleted_flag = 1 and \n" +
            "        mService.approve = 1  \n" +
            "), combo_plan_combo as ( \n" +
            "    select \n" +
            "        mComboPlan.id as combo_plan_id, \n" +
            "        mCombo.user_id as developer_id \n" +
            "   from {h-schema}combo_plan mComboPlan \n" +
            "     left join {h-schema}combo mCombo on mCombo.id = mComboPlan.combo_id \n" +
            "  where \n" +
            "    mComboPlan.deleted_flag = 1 and \n" +
            "    mCombo.deleted_flag = 1 and \n" +
            "    mCombo.approve = 1 \n" +
            "), user_cte as ( \n" +
            "    select id as userId \n" +
            "    from {h-schema}users \n" +
            "), customer_result as ( \n" +
            "    select \n" +
            "        mSub.user_id as userId, \n" +
            "        cast(mSub.created_at as date) as createdAt \n" +
            "    from {h-schema}subscriptions mSub \n" +
            "        left join pricing_service mService on mService.pricing_id = mSub.pricing_id \n" +
            "        left join combo_plan_combo mCombo on mCombo.combo_plan_id = mSub.combo_plan_id \n" +
            "        left join user_cte mUser on mUser.userId = mSub.user_id \n" +
            "        left join {h-schema}order_service_receive mOrderReceive on mOrderReceive.subscription_id = mSub.id \n" +
            "        left join {h-schema}order_service_status mOrderStatus on mOrderStatus.id = cast(mOrderReceive.order_status as bigint) \n" +
            "        left join {h-schema}sme_progress mSmeProgress on mSmeProgress.id = mOrderStatus.sme_progress_id \n" +
            "    where \n" +
            "        --Điều kiện subscription \n" +
            "        mSub.deleted_flag = 1 and mSub.confirm_status = 1 and \n" +
            "        --Điều kiện user \n" +
            "        mUser.userId is not null and \n" +
            "        --Điều kiện lọc theo DEV đang đăng nhập \n" +
            "        ( :developerId = -1 or :developerId = coalesce(mService.developer_id, mCombo.developer_id)) \n" +
            ") \n" +
            "select \n" +
            "    count(distinct userId) as total, \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            --Từ Ngày đầu của 1 tháng trước đến ngày cuối của 1 tháng trước \n" +
            "            createdAt >= date_trunc('month', (now() - interval '1' month))::::date and \n" +
            "            createdAt <= (date_trunc('month', now()) - interval '1' day)::::date \n" +
            "    ) as oneMonthAgo, \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            --Từ Ngày đầu của 2 tháng trước đến ngày cuối của 2 của tháng trước \n" +
            "            createdAt >= date_trunc('month', (now() - interval '2' month))::::date and \n" +
            "            createdAt <= (date_trunc('month', now() - interval '1' month) - interval '1' day)::::date \n" +
            "    ) as twoMonthAgo, \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            --Từ Ngày đầu của 3 tháng trước đến ngày cuối của 3 tháng trước \n" +
            "            createdAt >= date_trunc('month', (now() - interval '3' month))::::date and \n" +
            "            createdAt <= (date_trunc('month', now() - interval '2' month) - interval '1' day)::::date \n" +
            "    ) as threeMonthAgo, \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            --Từ Ngày đầu của 12 tháng trước đến ngày cuối của 12 tháng trước \n" +
            "            createdAt >= date_trunc('month', (now() - interval '12' month))::::date and \n" +
            "            createdAt <= (date_trunc('month', now() - interval '11' month) - interval '1' day)::::date \n" +
            "    ) as twelveMonthAgo, \n" +
            "    --Tháng này: Từ đầu tháng đến ngày hiện tại \n" +
            "     count(distinct userId) filter ( \n" +
            "        where \n" +
            "            createdAt >= date_trunc('month', now())::::date and \n" +
            "            createdAt <= now()::::date \n" +
            "    ) as currentMonth, \n" +
            "    --Cùng kỳ 1 tháng trước \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            createdAt >= date_trunc('month', (now() - interval '1' month))::::date and \n" +
            "            createdAt <= date_trunc('day', (now() - interval '1' month))::::date \n" +
            "    ) as samePeriodOneMonthAgo, \n" +
            "    --Cùng kỳ 2 tháng trước \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            createdAt >= date_trunc('month', (now() - interval '2' month))::::date and \n" +
            "            createdAt <= date_trunc('day', (now() - interval '2' month))::::date \n" +
            "    ) as samePeriodTwoMonthAgo, \n" +
            "    --Cùng kỳ 3 tháng trước \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            createdAt >= date_trunc('month', (now() - interval '3' month))::::date and \n" +
            "            createdAt <= date_trunc('day', (now() - interval '3' month))::::date \n" +
            "    ) as samePeriodThreeMonthAgo, \n" +
            "    --Cùng kỳ 12 tháng trước \n" +
            "    count(distinct userId) filter ( \n" +
            "        where \n" +
            "            createdAt >= date_trunc('month', (now() - interval '12' month))::::date and \n" +
            "            createdAt <= date_trunc('day', (now() - interval '12' month))::::date \n" +
            "    ) as samePeriodTwelveMonthAgo \n" +
            "from customer_result";

    public static final String GET_OVERVIEW_PRICING_REVENUE =
        "with billing_calculator as\n" +
            "    -- tính tổng doanh thu của từng billing\n" +
            "    ( \n" +
            "        select \n" +
            "            billings.subscriptions_id as subscription_id,\n" +
            "            round(sum(bill_item.amount_pre_tax)) as amount_pretax, \n" +
            "            case \n" +
            "                when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd'::::text) \n" +
            "                when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM'::::text) \n" +
            "                else to_char(billings.payment_date, 'yyyy'::::text) \n" +
            "            end as labelTime \n" +
            "        from \n" +
            "            {h-schema}bill_item \n" +
            "            join {h-schema}billings on bill_item.billing_id = billings.id \n" +
            "        where \n" +
            "            billings.status = 2 and\n" +
            "            billings.payment_date is not null and\n" +
            "            cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date) \n" +
            "        group by \n" +
            "            bill_item.billing_id, labelTime, billings.subscriptions_id\n" +
            "    ),\n" +
            "    --Thong tin chung cua pricing\n" +
            "    pricing_info as \n" +
            "    (\n" +
            "        select\n" +
            "            subscriptions.id as subscription_id, \n" +
            "            coalesce(services.user_id, combo.user_id) as developer_id,\n" +
            "            case\n" +
            "                when subscriptions.pricing_id is not null then concat(view_latest_pricing.pricing_latest_id, '0000')::::bigint\n" +
            "                else concat(view_latest_combo_plan.combo_plan_latest_id, '0001')::::bigint\n" +
            "            end as pricing_latest_unique_id, \n" +
            "            case \n" +
            "                when combo.id is not null then 2\n" +
            "                when coalesce(services.service_owner, combo.combo_owner) = any (array[0, 1]) then 0\n" +
            "                else 1\n" +
            "            end as os_on,\n" +
            "            case\n" +
            "                when view_latest_combo.combo_latest_id is null then concat(services.id, '0000')::::bigint\n" +
            "                else concat(view_latest_combo.combo_latest_id, '0001')::::bigint\n" +
            "            end as service_latest_unique_id,\n" +
            "            coalesce(services.service_name, view_latest_combo.combo_latest_name) as service_name,\n" +
            "            coalesce(view_latest_pricing.pricing_latest_name, view_latest_combo_plan.combo_plan_latest_name) as pricing_name,\n" +
            "            users.province_id\n" +
            "        from \n" +
            "            {h-schema}subscriptions\n" +
            "            left join {h-schema}pricing on pricing.id = subscriptions.pricing_id\n" +
            "            left join {h-schema}services on services.id = pricing.service_id\n" +
            "            left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id\n" +
            "            left join {h-schema}combo on combo.id = combo_plan.combo_id\n" +
            "            left join {h-schema}users on users.id = subscriptions.user_id\n" +
            "            left join {h-schema}view_latest_pricing on pricing.pricing_draft_id = view_latest_pricing.pricing_draft_id\n" +
            "            left join {h-schema}view_latest_combo_plan on combo_plan.combo_plan_draft_id = view_latest_combo_plan.combo_plan_draft_id\n" +
            "            left join {h-schema}view_latest_combo on combo.combo_draft_id = view_latest_combo.combo_draft_id\n" +
            "       where subscriptions.group_code is null \n" +
            "    ),\n" +
            "    filtered_pricing_amount as \n" +
            "    -- lấy doanh thu của pricing theo từng ngày/tháng/năm theo bộ lọc \n" +
            "    ( \n" +
            "        select \n" +
            "            pricing_info.pricing_latest_unique_id as pricingLatestUniqueId, \n" +
            "            coalesce(sum(billing_calculator.amount_pretax), 0) as amountPreTax, \n" +
            "            pricing_info.service_name as serviceName, \n" +
            "            pricing_info.pricing_name as pricingName, \n" +
            "            billing_calculator.labelTime \n" +
            "        from \n" +
            "            pricing_info \n" +
            "            join billing_calculator on pricing_info.subscription_id = billing_calculator.subscription_id \n" +
            "        where \n" +
            "            :developerId = pricing_info.developer_id and \n" +
            "            (-1 in (:lstProvinceId) or pricing_info.province_id in (:lstProvinceId)) and \n" +
            "            ( -1 = :serviceType or pricing_info.os_on = :serviceType) \n" +
            "            and (-1 in (:lstServiceProductUniqueId) or pricing_info.service_latest_unique_id in (:lstServiceProductUniqueId))\n" +
            "        group by \n" +
            "            pricingLatestUniqueId, pricingName, labelTime, serviceName \n" +
            "    ), \n" +
            "    -- tính tổng doanh thu của các gói cước trong khoảng tgian lọc + sort theo doanh thu lớn nhất\n" +
            "    total_pricing_amount as \n" +
            "    ( \n" +
            "        select \n" +
            "            pricingLatestUniqueId, \n" +
            "            pricingName,\n" +
            "            serviceName,\n" +
            "            coalesce(sum(amountPreTax), 0) as totalAmountPreTax \n" +
            "        from \n" +
            "            filtered_pricing_amount \n" +
            "        group by \n" +
            "            pricingLatestUniqueId, pricingName, serviceName \n" +
            "        order by \n" +
            "            totalAmountPreTax desc \n" +
            "        limit :top \n" +
            "    ), \n" +
            "    -- Lấy dữ liệu gói cước theo từng ngày/tháng/năm, bao gồm tgian ko có dữ liệu trong DB\n" +
            "    pricing_multiple_time as ( \n" +
            "        select distinct \n" +
            "            * \n" +
            "        from \n" +
            "            total_pricing_amount, {h-schema}func_get_interval_time(cast(:startTime as date), cast(:endTime as date), cast(:reportCycle as smallint)) \n" +
            "    ) \n" +
            "    -- tính tăng trưởng + lũy kế của từng gói cước trong khoảng tgian lọc\n" +
            "    select distinct \n" +
            "        pricing_multiple_time.pricingLatestUniqueId, \n" +
            "        pricing_multiple_time.label_time as labelTime, \n" +
            "        pricing_multiple_time.serviceName, \n" +
            "        pricing_multiple_time.pricingName, \n" +
            "        case \n" +
            "            when :chartType = 1 then coalesce(filtered_pricing_amount.amountPreTax, 0) \n" +
            "            else sum(coalesce(filtered_pricing_amount.amountPreTax, 0)) \n" +
            "                over (partition by pricing_multiple_time.pricingLatestUniqueId order by pricing_multiple_time.label_time rows between unbounded preceding and current row) \n" +
            "        end as amountPreTax \n" +
            "    from \n" +
            "        pricing_multiple_time \n" +
            "        left join filtered_pricing_amount on \n" +
            "            pricing_multiple_time.pricingLatestUniqueId = filtered_pricing_amount.pricingLatestUniqueId \n" +
            "            and pricing_multiple_time.label_time = filtered_pricing_amount.labelTime\n" +
            "    order by \n" +
            "        labelTime, \n" +
            "        pricingLatestUniqueId \n";



    public static final String SUB_TABLE_PRICING_REVENUE = "with multi_plan_unit_amount as (\n" +
            "        select pmp.id as id,\n" +
            "               pmp.pricing_plan,\n" +
            "               case\n" +
            "                   when pmp.pricing_plan in (2, 3, 4) then coalesce(ppd.price, pmp.price)\n" +
            "                   else pmp.price\n" +
            "               end as price\n" +
            "        from {h-schema}pricing_multi_plan pmp\n" +
            "         left join {h-schema}pricing_plan_detail ppd\n" +
            "                   on pmp.id = ppd.pricing_multi_plan_id\n" +
            "                      and ppd.subscription_setup_fee_id is null and ppd.unit_from = 1),\n" +
            "     pricing_tax as (\n" +
            "       select 1 as type,\n" +
            "           pt.pricing_id as id,\n" +
            "           sum(pt.percent) as percent\n" +
            "       from {h-schema}pricing_tax pt\n" +
            "       join {h-schema}tax t on pt.tax_id = t.id\n" +
            "       where pt.has_tax = 1\n" +
            "           and t.deleted_flag = 1\n" +
            "       group by pt.pricing_id\n" +
            "       UNION\n" +
            "       select 2 as type,\n" +
            "           ct.id_combo_plan as id,\n" +
            "           sum(ct.percent) as percent\n" +
            "       from {h-schema}combo_tax ct\n" +
            "       join {h-schema}tax t on ct.tax_id = t.id\n" +
            "       where ct.has_tax = 1\n" +
            "           and t.deleted_flag = 1\n" +
            "       group by ct.id_combo_plan),\n" +
            "     billing_calculator as\n" +
            "    -- tính tổng doanh thu của từng billing\n" +
            "    ( \n" +
            "        select \n" +
            "            billings.subscriptions_id as subscription_id,\n" +
            "            round(sum(COALESCE(bill_item.amount, 0::::double precision))) as unit_amount,\n" +
            "            round(sum(bill_item.amount_pre_tax)) as amount_pretax, \n" +
            "            round(sum(bill_item.amount_after_tax)) AS amount_aftertax,\n" +
            "            round(sum(bill_item.amount - bill_item.amount_pre_tax)) AS amount_discount,\n" +
            "            case \n" +
            "                when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd'::::text) \n" +
            "                when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM'::::text) \n" +
            "                else to_char(billings.payment_date, 'yyyy'::::text) \n" +
            "            end as labelTime,\n" +
            "            billings.payment_date,\n" +
            "            billings.billing_code\n" +
            "        from \n" +
            "            {h-schema}bill_item \n" +
            "            join {h-schema}billings on bill_item.billing_id = billings.id \n" +
            "        where \n" +
            "            billings.status = 2\n" +
            "            and billings.payment_date is not null\n" +
            "            and cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date) \n" +
            "        group by \n" +
            "            bill_item.billing_id, billing_code, labelTime, billings.subscriptions_id, billings.payment_date\n" +
            "    order by billings.payment_date desc),\n" +
            "    pricing_info as \n" +
            "    (\n" +
            "        select\n" +
            "            subscriptions.id as subscription_id, \n" +
            "            subscriptions.created_at,\n" +
            "            subscriptions.quantity,\n" +
            "            coalesce(multi_plan.price,\n" +
            "                case when latest_pricing.pricing_plan in (2, 3, 4) then unit_limited.price else latest_pricing.price end,\n" +
            "                combo_plan.amount) as price,\n" +
            "            coalesce(pricing_tax.percent, combo_tax.percent) as tax_percent,\n" +
            "            CASE\n" +
            "                WHEN subscriptions.portal_type = 1 THEN 'Admin'::::text\n" +
            "                WHEN subscriptions.portal_type = 2 THEN 'Dev'::::text\n" +
            "                WHEN subscriptions.portal_type = 3 THEN 'OneSME'::::text\n" +
            "                ELSE NULL::::text\n" +
            "            END AS portal_type,\n" +
            "            coalesce(services.user_id, combo.user_id) as developer_id,\n" +
            "            case\n" +
            "               when subscriptions.pricing_id is not null\n" +
            "                   then concat(view_latest_pricing.pricing_latest_id, '0000')::::bigint\n" +
            "               else concat(view_latest_combo_plan.combo_plan_latest_id, '0001')::::bigint\n" +
            "               end as pricing_latest_unique_id,\n" +
            "            case\n" +
            "                when view_latest_combo.combo_latest_id is null then concat(services.id, '0000')::::bigint\n" +
            "                else concat(view_latest_combo.combo_latest_id, '0001')::::bigint\n" +
            "            end as service_latest_unique_id,\n" +
            "            case\n" +
            "               when combo.id is not null then 2\n" +
            "               when coalesce(services.service_owner, combo.combo_owner) = any (array [0, 1]) then 0\n" +
            "               else 1\n" +
            "               end as os_on,\n" +
            "            case \n" +
            "                when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name) \n" +
            "                else users.name\n" +
            "            end as sme_name, \n" +
            "            users.province_id,\n" +
            "            users.email,\n" +
            "            case \n" +
            "                when users.customer_type = 'CN' then users.rep_personal_cert_number\n" +
            "                else users.tin\n" +
            "            end as identity_no,\n" +
            "            users.customer_type,\n" +
            "            province.name as province_name,\n" +
            "            coalesce(services.service_name, view_latest_combo.combo_latest_name) as service_name,\n" +
            "            coalesce(view_latest_pricing.pricing_latest_name, view_latest_combo_plan.combo_plan_latest_name) as pricing_name\n" +
            "        from \n" +
            "            {h-schema}subscriptions\n" +
            "            left join {h-schema}pricing on pricing.id = subscriptions.pricing_id\n" +
            "            left join {h-schema}view_latest_pricing\n" +
            "                 on pricing.pricing_draft_id = view_latest_pricing.pricing_draft_id\n" +
            "            left join {h-schema}pricing latest_pricing\n" +
            "                 on latest_pricing.id = view_latest_pricing.pricing_latest_id\n" +
            "            left join multi_plan_unit_amount multi_plan\n" +
            "                 on multi_plan.id = subscriptions.pricing_multi_plan_id\n" +
            "            left join {h-schema}services on services.id = pricing.service_id\n" +
            "            left join {h-schema}unit_limited\n" +
            "                 on unit_limited.pricing_id = view_latest_pricing.pricing_latest_id and unit_from = 1\n" +
            "            left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id\n" +
            "            left join {h-schema}combo on combo.id = combo_plan.combo_id\n" +
            "            left join {h-schema}view_latest_combo_plan\n" +
            "                 on combo_plan.combo_plan_draft_id = view_latest_combo_plan.combo_plan_draft_id\n" +
            "            left join {h-schema}view_latest_combo on combo.combo_draft_id = view_latest_combo.combo_draft_id\n" +
            "            left join {h-schema}combo_plan combo_plan_latest\n" +
            "                 on combo_plan_latest.id = view_latest_combo_plan.combo_plan_latest_id\n" +
            "            left join pricing_tax on pricing_tax.id = latest_pricing.id and pricing_tax.type = 1\n" +
            "            left join pricing_tax combo_tax on combo_tax.id = combo_plan_latest.id and combo_tax.type = 2\n" +
            "            left join {h-schema}users on users.id = subscriptions.user_id\n" +
            "            left join {h-schema}province on province.id = users.province_id\n" +
            "        where \n" +
            "            (-1 = :developerId or :developerId = coalesce(services.user_id, combo.user_id)) \n" +
            "                 and subscriptions.group_code is null\n" +
            "            and (-1 in (:lstProvinceId) or users.province_id in (:lstProvinceId)) \n" +
            "            and ('ALL' in (:lstCustomerType) or users.customer_type in (:lstCustomerType)) \n" +
            "    )\n";

    public static final String GET_PREVIEW_PRICING_REVENUE =
            SUB_TABLE_PRICING_REVENUE +
            "select \n" +
            "    pricing_info.pricing_name as pricingName,\n" +
            "    pricing_info.service_name as serviceName,\n" +
            "    case\n" +
            "        when pricing_info.os_on = 2 then 'Combo'\n" +
            "        when pricing_info.os_on = 1 then 'OS'\n" +
            "        when pricing_info.os_on = 0 then 'ON'\n" +
            "        else 'OS'\n" +
            "        end as subType,\n" +
            "    pricing_info.province_name as provinceName,\n" +
            "    pricing_info.email as email,\n" +
            "    pricing_info.sme_name as smeName,\n" +
            "    pricing_info.identity_no as identityNo,\n" +
            "    billing_calculator.billing_code as billingCode,\n" +
            "    billing_calculator.amount_pretax as amountPreTax,\n" +
            "    billing_calculator.amount_aftertax as amountAfterTax,\n" +
            "    billing_calculator.amount_discount as amountDiscount,\n" +
            "    pricing_info.created_at as createdAt,\n" +
            "    pricing_info.portal_type as createdSource\n" +
            "from \n" +
            "    pricing_info \n" +
            "    join billing_calculator on billing_calculator.subscription_id = pricing_info.subscription_id \n" +
            "where \n" +
            "    (-1 in (:lstPricingLatestUniqueId) or pricing_info.pricing_latest_unique_id in (:lstPricingLatestUniqueId)) and \n" +
            "    (-1 in (:lstServiceProductUniqueId) or pricing_info.service_latest_unique_id in (:lstServiceProductUniqueId)) and\n" +
            "    (-1 = :serviceType or :serviceType = pricing_info.os_on) and \n" +
            "    ( \n" +
            "        (:chartType = 1 and (:labelTime = '' or billing_calculator.labelTime = :labelTime)) or \n" +
            "        (:chartType = 2 and (:labelTime = '' or billing_calculator.labelTime <= :labelTime)) \n" +
            "    )\n" +
            "order by \n" +
            "    createdAt desc";

    public static final String GET_EXPORT_PRICING_REVENUE =
            SUB_TABLE_PRICING_REVENUE.replace("::::", "::") +
            "select \n" +
            "    cast(row_number() over(order by pricing_info.created_at desc) as varchar),\n" +
            "    pricing_info.pricing_name as pricingName,\n" +
            "    pricing_info.service_name as serviceName,\n" +
            "    case\n" +
            "       when pricing_info.os_on = 2 then 'Combo'\n" +
            "       when pricing_info.os_on = 1 then 'OS'\n" +
            "       when pricing_info.os_on = 0 then 'ON'\n" +
            "       else 'OS'\n" +
            "       end as subType,\n" +
            "    pricing_info.province_name as provinceName,\n" +
            "    pricing_info.email as email,\n" +
            "    pricing_info.sme_name as smeName,\n" +
            "    pricing_info.identity_no as identityNo,\n" +
            "    billing_calculator.billing_code as billingCode,\n" +
//            "    pricing_info.quantity as quantity,\n" +
            "    cast(billing_calculator.amount_pretax as varchar) as amountPreTax,\n" +
            "    cast(billing_calculator.amount_aftertax as varchar) as amountAfterTax,\n" +
            "    cast(billing_calculator.amount_discount as varchar) as amountDiscount,\n" +
            "    to_char(pricing_info.created_at, ' dd/MM/yyyy') as createdAt,\n" +
            "    pricing_info.portal_type as createdSource\n" +
            "from \n" +
            "    pricing_info \n" +
            "    join billing_calculator on billing_calculator.subscription_id = pricing_info.subscription_id \n" +
            "where \n" +
            "    (-1 in (:lstPricingLatestUniqueId) or pricing_info.pricing_latest_unique_id in (:lstPricingLatestUniqueId)) and \n" +
            "    (-1 in (:lstServiceProductUniqueId) or pricing_info.service_latest_unique_id in (:lstServiceProductUniqueId)) and\n" +
            "    (-1 = :serviceType or :serviceType = pricing_info.os_on) and \n" +
            "    ( \n" +
            "        (:chartType = 1 and (:labelTime = '' or billing_calculator.labelTime = :labelTime)) or \n" +
            "        (:chartType = 2 and (:labelTime = '' or billing_calculator.labelTime <= :labelTime)) \n" +
            "    ) " ;

    public static final String GET_REPORT_PRICING_REVENUE =
            "with multi_plan_unit_amount as (\n" +
                    "        select pmp.id as id,\n" +
                    "               pmp.pricing_plan,\n" +
                    "               case\n" +
                    "                   when pmp.pricing_plan in (2, 3, 4) then coalesce(ppd.price, pmp.price)\n" +
                    "                   else pmp.price\n" +
                    "               end as price\n" +
                    "        from {h-schema}pricing_multi_plan pmp\n" +
                    "         left join {h-schema}pricing_plan_detail ppd\n" +
                    "                   on pmp.id = ppd.pricing_multi_plan_id\n" +
                    "                      and ppd.subscription_setup_fee_id is null and ppd.unit_from = 1),\n" +
                    "     pricing_tax as (\n" +
                    "       select 1 as type,\n" +
                    "           pt.pricing_id as id,\n" +
                    "           sum(pt.percent) as percent\n" +
                    "       from {h-schema}pricing_tax pt\n" +
                    "       join {h-schema}tax t on pt.tax_id = t.id\n" +
                    "       where pt.has_tax = 1\n" +
                    "           and t.deleted_flag = 1\n" +
                    "       group by pt.pricing_id\n" +
                    "       UNION\n" +
                    "       select 2 as type,\n" +
                    "           ct.id_combo_plan as id,\n" +
                    "           sum(ct.percent) as percent\n" +
                    "       from {h-schema}combo_tax ct\n" +
                    "       join {h-schema}tax t on ct.tax_id = t.id\n" +
                    "       where ct.has_tax = 1\n" +
                    "           and t.deleted_flag = 1\n" +
                    "       group by ct.id_combo_plan),\n" +
                    "     billing_calculator as\n" +
                    "    -- tính tổng doanh thu của từng billing\n" +
                    "    ( \n" +
                    "        select \n" +
                    "            billings.subscriptions_id as subscription_id,\n" +
                    "            round(sum(COALESCE(bill_item.amount, 0::::double precision))) as unit_amount,\n" +
                    "            round(sum(bill_item.amount_pre_tax)) as amount_pretax, \n" +
                    "            round(sum(bill_item.amount_after_tax)) AS amount_aftertax,\n" +
                    "            round(sum(bill_item.amount - bill_item.amount_pre_tax)) AS amount_discount,\n" +
                    "            case \n" +
                    "                when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd'::::text) \n" +
                    "                when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM'::::text) \n" +
                    "                else to_char(billings.payment_date, 'yyyy'::::text) \n" +
                    "            end as labelTime,\n" +
                    "            billings.payment_date,\n" +
                    "            billings.billing_code\n" +
                    "        from \n" +
                    "            {h-schema}bill_item \n" +
                    "            join {h-schema}billings on bill_item.billing_id = billings.id \n" +
                    "        where \n" +
                    "            billings.status = 2\n" +
                    "            and billings.payment_date is not null\n" +
                    "            and cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date) \n" +
                    "        group by \n" +
                    "            bill_item.billing_id, billing_code, labelTime, billings.subscriptions_id, billings.payment_date\n" +
                    "    order by billings.payment_date desc),\n" +
                    "    pricing_info as \n" +
                    "    (\n" +
                    "        select\n" +
                    "            subscriptions.id as subscription_id, \n" +
                    "            subscriptions.created_at,\n" +
                    "            subscriptions.quantity,\n" +
                    "            coalesce(multi_plan.price,\n" +
                    "                case when latest_pricing.pricing_plan in (2, 3, 4) then unit_limited.price else latest_pricing.price end,\n" +
                    "                combo_plan.amount) as price,\n" +
                    "            coalesce(pricing_tax.percent, combo_tax.percent) as tax_percent,\n" +
                    "            CASE\n" +
                    "                WHEN subscriptions.portal_type = 1 THEN 'Admin'::::text\n" +
                    "                WHEN subscriptions.portal_type = 2 THEN 'Dev'::::text\n" +
                    "                WHEN subscriptions.portal_type = 3 THEN 'OneSME'::::text\n" +
                    "                ELSE NULL::::text\n" +
                    "            END AS portal_type,\n" +
                    "            coalesce(services.user_id, combo.user_id) as developer_id,\n" +
                    "            case\n" +
                    "               when subscriptions.pricing_id is not null\n" +
                    "                   then concat(view_latest_pricing.pricing_latest_id, '0000')::::bigint\n" +
                    "               else concat(view_latest_combo_plan.combo_plan_latest_id, '0001')::::bigint\n" +
                    "               end as pricing_latest_unique_id,\n" +
                    "            case\n" +
                    "                when view_latest_combo.combo_latest_id is null then concat(services.id, '0000')::::bigint\n" +
                    "                else concat(view_latest_combo.combo_latest_id, '0001')::::bigint\n" +
                    "            end as service_latest_unique_id,\n" +
                    "            case\n" +
                    "               when combo.id is not null then 2\n" +
                    "               when coalesce(services.service_owner, combo.combo_owner) = any (array [0, 1]) then 0\n" +
                    "               else 1\n" +
                    "               end as os_on,\n" +
                    "            case \n" +
                    "                when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name) \n" +
                    "                else users.name\n" +
                    "            end as sme_name, \n" +
                    "            users.province_id,\n" +
                    "            users.email,\n" +
                    "            case \n" +
                    "                when users.customer_type = 'CN' then users.rep_personal_cert_number\n" +
                    "                else users.tin\n" +
                    "            end as identity_no,\n" +
                    "            users.customer_type,\n" +
                    "            province.name as province_name,\n" +
                    "            coalesce(services.service_name, view_latest_combo.combo_latest_name) as service_name,\n" +
                    "            coalesce(\n" +
                    "                case when pmp.id is not null and pmp.payment_cycle != -1 then concat_ws(' - ', view_latest_pricing.pricing_latest_name,\n" +
                    "                   concat(pmp.payment_cycle, ' ',\n" +
                    "                       case pmp.circle_type\n" +
                    "                            when 0 then 'ngày'\n" +
                    "                            when 1 then 'tuần'\n" +
                    "                            when 2 then 'tháng'\n" +
                    "                            when 3 then 'năm'\n" +
                    "                       else 'ngày'\n" +
                    "                       end))\n" +
                    "                else view_latest_pricing.pricing_latest_name end,\n" +
                    "                view_latest_combo_plan.combo_plan_latest_name) as pricing_name\n" +
                    "        from \n" +
                    "            {h-schema}subscriptions\n" +
                    "            left join {h-schema}pricing on pricing.id = subscriptions.pricing_id\n" +
                    "            left join {h-schema}view_latest_pricing\n" +
                    "                 on pricing.pricing_draft_id = view_latest_pricing.pricing_draft_id\n" +
                    "            left join {h-schema}pricing latest_pricing\n" +
                    "                 on latest_pricing.id = view_latest_pricing.pricing_latest_id\n" +
                    "            left join {h-schema}pricing_multi_plan pmp on pmp.id = subscriptions.pricing_multi_plan_id\n" +
                    "            left join multi_plan_unit_amount multi_plan\n" +
                    "                 on multi_plan.id = subscriptions.pricing_multi_plan_id\n" +
                    "            left join {h-schema}services on services.id = pricing.service_id\n" +
                    "            left join {h-schema}unit_limited\n" +
                    "                 on unit_limited.pricing_id = view_latest_pricing.pricing_latest_id and unit_from = 1\n" +
                    "            left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id\n" +
                    "            left join {h-schema}combo on combo.id = combo_plan.combo_id\n" +
                    "            left join {h-schema}view_latest_combo_plan\n" +
                    "                 on combo_plan.combo_plan_draft_id = view_latest_combo_plan.combo_plan_draft_id\n" +
                    "            left join {h-schema}view_latest_combo on combo.combo_draft_id = view_latest_combo.combo_draft_id\n" +
                    "            left join {h-schema}combo_plan combo_plan_latest\n" +
                    "                 on combo_plan_latest.id = view_latest_combo_plan.combo_plan_latest_id\n" +
                    "            left join pricing_tax on pricing_tax.id = latest_pricing.id and pricing_tax.type = 1\n" +
                    "            left join pricing_tax combo_tax on combo_tax.id = combo_plan_latest.id and combo_tax.type = 2\n" +
                    "            left join {h-schema}users on users.id = subscriptions.user_id\n" +
                    "            left join {h-schema}province on province.id = users.province_id\n" +
                    "        where \n" +
                    "            (-1 = :developerId or :developerId = coalesce(services.user_id, combo.user_id)) \n" +
                    "                 and subscriptions.group_code is null\n" +
                    "            and (-1 in (:lstProvinceId) or users.province_id in (:lstProvinceId)) \n" +
                    "            and ('ALL' in (:lstCustomerType) or users.customer_type in (:lstCustomerType)) \n" +
                    "    ),\n" +
                    "   report_data as (select billing_calculator.labelTime as createdAt,\n" +
                    "       max(billing_calculator.payment_date) as paymentDate,\n" +
                    "       pricing_info.pricing_name as pricingName,\n" +
                    "       pricing_info.service_name as serviceName,\n" +
                    "       case\n" +
                    "           when pricing_info.os_on = 2 then 'Combo'\n" +
                    "           when pricing_info.os_on = 1 then 'OS'\n" +
                    "           when pricing_info.os_on = 0 then 'ON'\n" +
                    "           else 'OS'\n" +
                    "           end as subType,\n" +
                    "       coalesce(sum(pricing_info.quantity), 0) as quantity,\n" +
                    "       min(coalesce(pricing_info.price, 0)) as price,\n" +
                    "       pricing_info.tax_percent as taxPercent,\n" +
                    "       sum(billing_calculator.amount_pretax) as amountPreTax,\n" +
                    "       sum(billing_calculator.amount_aftertax) as amountAfterTax,\n" +
                    "       sum(billing_calculator.amount_discount) as amountDiscount,\n" +
                    "       count(distinct pricing_info.subscription_id) as subscriptionCount,\n" +
                    "       pricing_info.portal_type as createdSource\n" +
                    "   from pricing_info\n" +
                    "         join billing_calculator on billing_calculator.subscription_id = pricing_info.subscription_id\n" +
                    "   where \n" +
                    "    (-1 in (:lstPricingLatestUniqueId) or pricing_info.pricing_latest_unique_id in (:lstPricingLatestUniqueId)) and \n" +
                    "    (-1 in (:lstServiceProductUniqueId) or pricing_info.service_latest_unique_id in (:lstServiceProductUniqueId)) and\n" +
                    "    (-1 = :serviceType or :serviceType = pricing_info.os_on) and \n" +
                    "    (:chartType = 1 and (:labelTime = '' or billing_calculator.labelTime = :labelTime))\n" +
                    "       or (:chartType = 2 and (:labelTime = '' or billing_calculator.labelTime <= :labelTime))\n" +
                    "   group by billing_calculator.labelTime, pricing_info.pricing_name, pricing_info.service_name, pricing_info.os_on,\n" +
                    "         pricing_info.portal_type, pricing_info.tax_percent),\n" +
                    "   ranking_data as (\n" +
                    "       select row_number() over (partition by rd.createdAt order by rd.amountPreTax desc) as ranking,\n" +
                    "              rd.*\n" +
                    "       from report_data rd)\n" +
                    "select * from ranking_data\n" +
                    "where ranking <= :top\n" +
                    "order by paymentDate desc" ;

    public static final String GET_COUNT_PREVIEW_PRICING_REVENUE =
        "select count(1) from ( " + GET_PREVIEW_PRICING_REVENUE + ") as query ";

    public static final String GET_OVERVIEW_REVENUE_BY_MONTH =
        "--CTE chứa tổng tiền theo từng bill_id\n" +
            "with billing_calculator as ( \n" +
            "    select mBillItem.billing_id, \n" +
            "        sum(mBillItem.amount_pre_tax) as amount_pre_tax \n" +
            "    from {h-schema}bill_item mBillItem \n" +
            "        left join {h-schema}billings mBill on mBill.id = mBillItem.billing_id \n" +
            "    group by mBillItem.billing_id \n" +
            "), \n" +
            "--CTE chứa tổng tiền và payment_date của bill_id và thuộc DEV đang đăng nhập\n" +
            "billing_result as ( \n" +
            "    select \n" +
            "        mBillCalculator.amount_pre_tax, \n" +
            "        cast(mBill.payment_date as date) payment_date \n" +
            "    from {h-schema}billings mBill \n" +
            "        left join billing_calculator mBillCalculator on mBillCalculator.billing_id = mBill.id \n" +
            "        left join {h-schema}view_dashboard_revenue_by_month mDashboardRevenue on mDashboardRevenue.subscription_id = mBill.subscriptions_id \n" +
            "    where \n" +
            "        mBill.status = 2 and mBill.payment_date is not null and \n" +
            "        (mDashboardRevenue.developer_id = :developerId) \n" +
            ") \n" +
            "select \n" +
            "    round(coalesce(sum(mBillResult.amount_pre_tax), 0)) as total, \n" +
            "    round(coalesce(sum(mBillResult.amount_pre_tax) filter ( \n" +
            "        where mBillResult.payment_date between \n" +
            "            cast(date_trunc('month', now()) as date) and \n" +
            "            cast(now() as date) \n" +
            "    ), 0)) as currentMonth, \n" +
            "    round(coalesce(sum(mBillResult.amount_pre_tax) filter ( \n" +
            "        where mBillResult.payment_date between \n" +
            "            cast(date_trunc('month', (now() - interval '1' month)) as date) and \n" +
            "            cast(date_trunc('day', (now() - interval '1' month)) as date) \n" +
            "    ), 0)) as oneMonthAgo, \n" +
            "    round(coalesce(sum(mBillResult.amount_pre_tax) filter ( \n" +
            "        where mBillResult.payment_date between \n" +
            "            cast(date_trunc('month', (now() - interval '2' month)) as date) and \n" +
            "            cast(date_trunc('day', (now() - interval '2' month)) as date) \n" +
            "    ), 0)) as twoMonthAgo, \n" +
            "    round(coalesce(sum(mBillResult.amount_pre_tax) filter ( \n" +
            "        where mBillResult.payment_date between \n" +
            "            cast(date_trunc('month', (now() - interval '3' month)) as date) and \n" +
            "            cast(date_trunc('day', (now() - interval '3' month)) as date) \n" +
            "    ), 0)) as threeMonthAgo \n" +
            "from billing_result mBillResult";
    
    private static final String GET_PREVIEW_REVENUE_BY_MONTH_CTE = 
        "with billing_calculator as ( \n" +
            "    select mBillItem.billing_id, \n" +
            "        round(sum(mBillItem.amount_pre_tax)) as amount_pretax, \n" +
            "        round(sum(mBillItem.amount_after_tax)) as amount_aftertax, \n" +
            "        round(sum(mBillItem.amount - mBillItem.amount_pre_tax)) as amount_discount \n" +
            "    from {h-schema}bill_item mBillItem \n" +
            "        left join {h-schema}billings mBill on mBill.id = mBillItem.billing_id \n" +
            "    group by mBillItem.billing_id \n" +
            "), pricing_service as ( \n" +
            "    select \n" +
            "        mPricing.id as pricing_id, \n" +
            "        mPricing.pricing_name, \n" +
            "        mService.service_name \n" +
            "    from {h-schema}pricing mPricing \n" +
            "        left join {h-schema}services mService on mService.id = mPricing.service_id \n" +
            "    where \n" +
            "        mPricing.deleted_flag = 1 and \n" +
            "        mService.deleted_flag = 1 and \n" +
            "        mService.approve = 1  \n" +
            "), combo_plan_combo as ( \n" +
            "    select \n" +
            "        combo_plan.combo_name as combo_plan_name, \n" +
            "        combo_plan.id as combo_plan_id, \n" +
            "        combo.combo_name \n" +
            "   from {h-schema}combo_plan \n" +
            "     left join {h-schema}combo on combo.id = combo_plan.combo_id \n" +
            "  where \n" +
            "    combo_plan.deleted_flag = 1 and \n" +
            "    combo.deleted_flag = 1 and \n" +
            "    combo.approve = 1 \n" +
            "), user_province as ( \n" +
            "    select \n" +
            "        mUser.id as user_id, \n" +
            "        mUser.email, \n" +
            "        case \n" +
            "            when mUser.customer_type = 'CN' then concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
            "            else mUser.name \n" +
            "        end as name, \n" +
            "        mUser.customer_type, mUser.rep_personal_cert_number, \n" +
            "        mUser.tin, \n" +
            "        mUser.province_id, \n" +
            "        mProvince.name as province_name \n" +
            "    from {h-schema}users mUser \n" +
            "        left join {h-schema}province mProvince on mProvince.id = mUser.province_id \n" +
            "), revenue_result as (\n" +
            "    select \n" +
            "        mUser.province_name as provinceName, \n" +
            "        mUser.email as email, \n" +
            "        mUser.name as smeName, \n" +
            "        case \n" +
            "            when mUser.customer_type = 'CN' then mUser.rep_personal_cert_number \n" +
            "            else mUser.tin \n" +
            "        end as identityNo, \n" +
            "        coalesce(mService.service_name, mCombo.combo_name) as serviceName, \n" +
            "        coalesce(mService.pricing_name, mCombo.combo_plan_name) as pricingName, \n" +
            "        case \n" +
            "            when mDashboardRevenue.service_lastest_unique_id % 10000 = 1 then 'COMBO' \n" +
            "            when mDashboardRevenue.os_on = 0 then 'ON' \n" +
            "            else 'OS' \n" + 
            "        end as subType,\n" +
            "        mBillCalculator.amount_pretax as amountPreTax, \n" +
            "        mBillCalculator.amount_aftertax as amountAfterTax, \n" +
            "        mBillCalculator.amount_discount as amountDiscount, \n" +
            "        to_char(mDashboardRevenue.created_at, 'dd/MM/yyyy') as createdAt, \n" +
            "        mDashboardRevenue.created_source as createdSource \n" +
            "    from {h-schema}billings mBill \n" +
            "        left join billing_calculator mBillCalculator on mBillCalculator.billing_id = mBill.id \n" +
            "        left join {h-schema}view_dashboard_revenue_by_month mDashboardRevenue on mDashboardRevenue.subscription_id = mBill.subscriptions_id \n" +
            "        left join pricing_service mService on mService.pricing_id = mDashboardRevenue.pricing_id \n" +
            "        left join combo_plan_combo mCombo on mCombo.combo_plan_id = mDashboardRevenue.combo_plan_id \n" +
            "        left join user_province mUser on mUser.user_id = mDashboardRevenue.user_id \n" +
            "    where \n" +
            "        mBill.status = 2 and mBill.payment_date is not null and \n" +
            "        --Các điều kiện filter\n" +
            "        (mDashboardRevenue.developer_id = :developerId) and \n" +
            "        (-1 in (:lstProvinceId) or mUser.province_id in (:lstProvinceId)) and \n" +
            "        ( \n" +
            "            -1 = :serviceType or \n" +
            "            --Lọc theo loại dịch vụ là combo\n" +
            "            (2 = :serviceType and mDashboardRevenue.service_lastest_unique_id % 10000 = 1) or \n" +
            "            mDashboardRevenue.os_on = :serviceType \n" +
            "        ) and \n" +
            "        (-1 in (:lstServiceProductUniqueId) or mDashboardRevenue.service_lastest_unique_id in (:lstServiceProductUniqueId)) and \n" +
            "        ('ALL' in (:lstCustomerType) or mUser.customer_type in (:lstCustomerType)) and \n" +
            "        (cast(mBill.payment_date as date) between cast(:startTime as date) and cast(:endTime as date)) \n" +
            "    order by mBill.payment_date desc \n" +
            ") \n";
    
    public static final String GET_PREVIEW_REVENUE_BY_MONTH_SELECT =
        GET_PREVIEW_REVENUE_BY_MONTH_CTE + 
            "select * from revenue_result";
    
    public static final String GET_EXPORT_REVENUE_BY_MONTH_SELECT =
        GET_PREVIEW_REVENUE_BY_MONTH_CTE +
            "select \n" +
            "   cast(row_number() over() as varchar), \n" +
            "   provinceName, \n" +
            "   email, \n" +
            "   smeName, \n" +
            "   identityNo, \n" +
            "   serviceName, \n" +
            "   pricingName, \n" +
            "   subType,\n" +
            "   cast(amountPreTax as varchar), \n" +
            "   cast(amountAfterTax as varchar), \n" +
            "   cast(amountDiscount as varchar), \n" +
            "   concat(' ', createdAt), \n" +
            "   createdSource \n" + 
            "from revenue_result";
    
    public static final String GET_PREVIEW_REVENUE_BY_MONTH_COUNT =
        GET_PREVIEW_REVENUE_BY_MONTH_CTE +
            "select count(1) from revenue_result";

    public static final String GET_OVERVIEW_REVENUE =
        "with mview_billing as ( \n" +
            "    select \n" +
            "        billings.subscriptions_id as subscription_id, \n" +
            "        sum(bill_item.amount_pre_tax) as amount_pretax, \n" +
            "        case \n " +
            "            when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd') \n" +
            "            when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM') \n" +
            "            else to_char(billings.payment_date, 'yyyy') \n" +
            "        end as payment_date \n" +
            "    from {h-schema}billings \n" +
            "        left join {h-schema}bill_item on bill_item.billing_id = billings.id \n" +
            "    where \n" +
            "        billings.status = 2 and billings.payment_date is not null and \n" +
            "        (cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date)) \n" +
            "    group by billings.subscriptions_id, billings.payment_date \n" +
            "), \n" +
            "raw_data as ( \n" +
            "        select \n" +
            "            sum(mview_billing.amount_pretax) as amount_pretax, \n" +
            "            mview_billing.payment_date as label_time \n" +
            "        from {h-schema}mview_dashboard_dev \n" +
            "            join mview_billing on mview_dashboard_dev.subscription_id = mview_billing.subscription_id \n" +
            "        where \n" +
            "            mview_dashboard_dev.developer_id = (:userId) and \n" +
            "            (-1 in (:lstServiceProductUniqueId) or mview_dashboard_dev.service_lastest_unique_id in (:lstServiceProductUniqueId)) and \n" +
            "            (-1 = :serviceType or mview_dashboard_dev.os_on = :serviceType or (2 = :serviceType and mview_dashboard_dev.combo_plan_id is not null)) and \n" +
            "            ('ALL' in (:lstCustomerType) or mview_dashboard_dev.customer_type in (:lstCustomerType)) and \n" +
            "            (-1 in (:lstProvinceId) or mview_dashboard_dev.province_id in (:lstProvinceId)) \n" +
            "        group by label_time \n" +
            "        order by label_time asc \n" +
            "     )\n" +
            "\n" +
            "select \n" +
            "    time_interval.label_time as labelTime,\n" +
            "    case \n" +
            "        when :chartType = 2 then COALESCE(round((sum(raw_data.amount_pretax) over (order by time_interval.label_time rows between unbounded preceding and current row))), 0) \n" +
            "        else COALESCE(round(raw_data.amount_pretax), 0) \n" +
            "    end as revenue \n" +
            "from {h-schema}func_get_interval_time(cast(:startTime as date), cast(:endTime as date), cast(:reportCycle as int2)) as time_interval \n" +
            "    left join raw_data on raw_data.label_time = time_interval.label_time \n" +
            "order by time_interval.label_time asc ";

    public static final String GET_PREVIEW_REVENUE_DEV =
            "with mview_billing as ( \n" +
            "    select \n" +
            "        billings.subscriptions_id as subscription_id, \n" +
            "        billings.payment_date as datePayment, \n" +
            "        round(sum(bill_item.amount_pre_tax)) as amount_pretax, \n" +
            "        round(sum(bill_item.amount_pre_tax)) as amount_aftertax, \n" +
            "        round(sum(bill_item.amount - bill_item.amount_pre_tax)) as amount_discount, \n" +
            "        case \n " +
            "            when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd') \n" +
            "            when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM') \n" +
            "            else to_char(billings.payment_date, 'yyyy') \n" +
            "        end as payment_date \n" +
            "    from {h-schema}billings \n" +
            "        left join {h-schema}bill_item on bill_item.billing_id = billings.id \n" +
            "    where \n" +
            "        billings.status = 2 and billings.payment_date is not null and \n" +
            "        (cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date)) \n" +
            "    group by billings.subscriptions_id, billings.payment_date \n" +
            "), \n" +
            "mview_service as ( \n" +
            "    select \n" +
            "        id, service_name, user_id, concat(services.id, '0000')::::bigint as lastest_service_unique_id, \n" +
            "        case \n" +
            "            when services.service_owner = ANY(ARRAY[0,1]) then 0 \n" +
            "            else 1 \n" +
            "        end as os_on \n" +
            "    from {h-schema}services \n" +
            "    where deleted_flag = 1 and approve = 1 \n" +
            "), \n" +
            "mview_combo as ( \n" +
            "    select \n" +
            "        id, combo_name, user_id, combo_draft_id, \n" +
            "        case \n" +
            "            when combo.combo_owner = ANY(ARRAY[0,1]) then 0 \n" +
            "            else 1 \n" +
            "        end as os_on \n" +
            "    from {h-schema}combo \n" +
            "    where deleted_flag = 1 and approve = 1 \n" +
            "), \n" +
            "lastest_combo as ( \n" +
            "    select \n" +
            "        combo.combo_draft_id, \n" +
            "        concat(max(combo.id), '0001')::::bigint as lastest_combo_unique_id \n" +
            "    from {h-schema}combo \n" +
            "    group by combo.combo_draft_id \n" +
            "), \n" +
            "raw_data as ( \n" +
            "    select \n" +
            "        province.name as provinceName,\n" +
            "        users.email as email, \n" +
            "        case \n" +
            "            when users.customer_type = 'CN' then concat(users.last_name,' ', users.first_name) \n" +
            "            else users.name \n" +
            "        end as smeName, \n" +
            "        case \n" +
            "            when users.customer_type = 'CN' then users.rep_personal_cert_number \n" +
            "            else users.tin \n" +
            "        end as identityNo, \n" +
            "        COALESCE(mview_service.service_name, mview_combo.combo_name) as serviceName, \n" +
            "        COALESCE(pricing.pricing_name, combo_plan.combo_name) as pricingName, \n" +
            "        case \n" +
            "            when subscriptions.combo_plan_id is not null then 'COMBO' \n" +
            "            when COALESCE(mview_service.os_on,mview_combo.os_on) = 0 then 'ON' \n" +
            "            else 'OS' \n" +
            "        end as subType, \n" +
            "        mview_billing.amount_pretax as amountPreTax, \n" +
            "        mview_billing.amount_aftertax as amountAfterTax, \n" +
            "        mview_billing.amount_discount as amountDiscount, \n" +
            "        cast(subscriptions.created_at as date) as createdAt, \n" +
            "        case \n" +
            "            when subscriptions.portal_type = 1 then 'Admin' \n" +
            "            when subscriptions.portal_type = 2 then 'Dev' \n" +
            "            when subscriptions.portal_type = 3 then 'OneSME' \n" +
            "            else null \n" +
            "        end as createdSource, \n" +
            "        mview_billing.payment_date as labelTime \n" +
            "    from mview_billing \n" +
            "        left join {h-schema}subscriptions on mview_billing.subscription_id = subscriptions.id \n" +
            "        left join {h-schema}users on subscriptions.user_id = users.id \n" +
            "        left join {h-schema}pricing on pricing.id = subscriptions.pricing_id \n" +
            "        left join mview_service on mview_service.id = pricing.service_id \n" +
            "        left join {h-schema}combo_plan on combo_plan.id = subscriptions.combo_plan_id \n" +
            "        left join mview_combo on mview_combo.id = combo_plan.combo_id \n" +
            "        left join lastest_combo on lastest_combo.combo_draft_id = mview_combo.combo_draft_id \n" +
            "        left join {h-schema}province ON province.id = users.province_id \n" +
            "    where \n" +
            "        (subscriptions.deleted_flag = 1 and subscriptions.confirm_status = 1) and \n" +
            "        (COALESCE(mview_service.user_id, mview_combo.user_id) = :userId) and \n" +
            "        (-1 in (:lstServiceProductUniqueId) or COALESCE(mview_service.lastest_service_unique_id,lastest_combo.lastest_combo_unique_id) in (:lstServiceProductUniqueId)) and \n" +
            "        (-1 = :serviceType or COALESCE(mview_service.os_on,mview_combo.os_on) = :serviceType or (2 = :serviceType and subscriptions.combo_plan_id is not null)) and \n" +
            "        (-1 in (:lstProvinceId) or users.province_id in (:lstProvinceId)) and \n" +
            "        ('ALL' in (:lstCustomerType) or users.customer_type in (:lstCustomerType)) \n" +
            "    order by mview_billing.datePayment desc \n" +
            "    ) \n" +
            "select * from raw_data \n" +
            "where \n" +
            "    (:chartType = 1 and (:labelTime = '' or raw_data.labelTime = :labelTime)) or \n" +
            "    (:chartType = 2 and (:labelTime = '' or raw_data.labelTime <= :labelTime)) \n";

    public static final String GET_EXPORT_REVENUE_DEV =
        "with mview_billing as ( \n" +
            "    select \n" +
            "        billings.subscriptions_id as subscription_id, \n" +
            "        billings.payment_date as datePayment, \n" +
            "        round(sum(bill_item.amount_pre_tax)) as amount_pretax, \n" +
            "        round(sum(bill_item.amount_pre_tax)) as amount_aftertax, \n" +
            "        round(sum(bill_item.amount - bill_item.amount_pre_tax)) as amount_discount, \n" +
            "        case \n " +
            "            when :reportCycle = 0 then to_char(billings.payment_date, 'yyyy/MM/dd') \n" +
            "            when :reportCycle = 1 then to_char(billings.payment_date, 'yyyy/MM') \n" +
            "            else to_char(billings.payment_date, 'yyyy') \n" +
            "        end as payment_date \n" +
            "    from {h-schema}billings \n" +
            "        left join {h-schema}bill_item on bill_item.billing_id = billings.id \n" +
            "    where \n" +
            "        billings.status = 2 and billings.payment_date is not null and \n" +
            "        (cast(billings.payment_date as date) between cast(:startTime as date) and cast(:endTime as date)) \n" +
            "    group by billings.subscriptions_id, billings.payment_date \n" +
            "), \n" +
            "raw_data as ( \n" +
            "    select \n" +
            "        province.name as provinceName,\n" +
            "        users.email as email, \n" +
            "        case \n" +
            "            when users.customer_type = 'CN' then concat(users.last_name,' ', users.first_name) \n" +
            "            else users.name \n" +
            "        end as smeName, \n" +
            "        case \n" +
            "            when users.customer_type = 'CN' then users.rep_personal_cert_number \n" +
            "            else users.tin \n" +
            "        end as identityNo, \n" +
            "        COALESCE(mview_service.service_name, mview_combo.combo_name) as serviceName, \n" +
            "        COALESCE(mview_service.pricing_name, mview_combo.comboplan_name) as pricingName, \n" +
            "        case \n" +
            "            when mview_dashboard_dev.combo_plan_id is not null then 'COMBO' \n" +
            "            when mview_dashboard_dev.os_on = 0 then 'ON' \n" +
            "            else 'OS' \n" +
            "        end as subType, \n" +
            "        cast(mview_billing.amount_pretax as varchar) as amountPreTax, \n" +
            "        cast(mview_billing.amount_aftertax as varchar) as amountAfterTax, \n" +
            "        cast(mview_billing.amount_discount as varchar) as amountDiscount, \n" +
            "        to_char(mview_dashboard_dev.created_at, ' dd/MM/yyyy') as createdAt, \n" +
            "        case \n" +
            "            when subscriptions.portal_type = 1 then 'Admin' \n" +
            "            when subscriptions.portal_type = 2 then 'Dev' \n" +
            "            when subscriptions.portal_type = 3 then 'OneSME' \n" +
            "            else null \n" +
            "        end as createdSource, \n" +
            "        mview_billing.datePayment, \n" +
            "        mview_billing.payment_date as labelTime \n" +
            "    from mview_billing \n" +
            "        left join {h-schema}mview_dashboard_dev on mview_billing.subscription_id = mview_dashboard_dev.subscription_id \n" +
            "        left join {h-schema}subscriptions on mview_billing.subscription_id = subscriptions.id \n" +
            "        left join {h-schema}users on mview_dashboard_dev.user_id = users.id \n" +
            "        left join {h-schema}mview_service on mview_service.pricing_id = mview_dashboard_dev.pricing_id \n" +
            "        left join {h-schema}mview_combo on mview_combo.comboplan_id = mview_dashboard_dev.combo_plan_id \n" +
            "        left join {h-schema}province ON province.id = users.province_id \n" +
            "    where \n" +
            "        (mview_dashboard_dev.developer_id = :userId) and \n" +
            "        (-1 in (:lstServiceProductUniqueId) or mview_dashboard_dev.service_lastest_unique_id in (:lstServiceProductUniqueId)) and \n" +
            "        (-1 = :serviceType or mview_dashboard_dev.os_on = :serviceType or (2 = :serviceType and mview_dashboard_dev.combo_plan_id is not null)) and \n" +
            "        (-1 in (:lstProvinceId) or mview_dashboard_dev.province_id in (:lstProvinceId)) and \n" +
            "        ('ALL' in (:lstCustomerType) or mview_dashboard_dev.customer_type in (:lstCustomerType)) \n" +
            "    ) \n" +
            "select \n" +
            "    cast(row_number() over(order by raw_data.datePayment desc) as varchar) as order, \n" +
            "    raw_data.provinceName,\n" +
            "    raw_data.email, \n" +
            "    raw_data.smeName, \n" +
            "    raw_data.identityNo, \n" +
            "    raw_data.serviceName, \n" +
            "    raw_data.pricingName, \n" +
            "    raw_data.subType, \n" +
            "    raw_data.amountPreTax, \n" +
            "    raw_data.amountAfterTax, \n" +
            "    raw_data.amountDiscount, \n" +
            "    raw_data.createdAt, \n" +
            "    raw_data.createdSource \n" +
            "from raw_data \n" +
            "where \n" +
            "    (:chartType = 1 and (:labelTime = '' or raw_data.labelTime = :labelTime)) or \n" +
            "    (:chartType = 2 and (:labelTime = '' or raw_data.labelTime <= :labelTime))" ;
    
    public static final String GET_COUNT_PREVIEW_REVENUE_DEV =
        "select count(1) from (" + GET_PREVIEW_REVENUE_DEV + ") as data_query";
}