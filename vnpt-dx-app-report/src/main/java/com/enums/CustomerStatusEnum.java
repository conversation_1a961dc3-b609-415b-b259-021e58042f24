package com.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR> NghiaPT
 * @version : 1.0 5/30/2022
 */
@Getter
public enum CustomerStatusEnum {
    IN_TRIAL(1L), ACTIVE(2L), ALL(-1L);

    CustomerStatusEnum(Long value) {
        this.value = value;
    }

    private final Long value;

    private static final Map<Long, CustomerStatusEnum> map = new HashMap<>();

    static {
        for (CustomerStatusEnum customerStatus : CustomerStatusEnum.values()) {
            map.put(customerStatus.value, customerStatus);
        }
    }

    public static CustomerStatusEnum getEnumOf(Long value) {
        return map.get(value);
    }
}
