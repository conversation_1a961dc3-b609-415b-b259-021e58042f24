package com.service.dashboard;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import com.common.dto.ICommonIdAndName;
import com.dto.dashboard.IComboboxProvinceDetailDTO;

public interface RevenueAdminComboboxService {

    Slice<IComboboxProvinceDetailDTO> getComboboxLstProvinceDetail(String name, Pageable pageable);

    // Lấy ra tên SPDV (ko có Combo)
    Slice<ICommonIdAndName> getComboboxServiceName(String name, Pageable pageable);

    // Lấy ra tên SPDV + Combo
   Slice<ICommonIdAndName> getComboboxServiceAndComboName(String name, Pageable pageable);


}
