DELETE FROM "vnpt_dev"."excel_field_mapping" WHERE type = 2 AND (db_field = 'customer_type' OR db_field = 'name');
INSERT INTO "vnpt_dev"."excel_field_mapping" ("id", "type", "db_field", "excel_fields") VALUES ((SELECT MAX(id)+1 FROM "vnpt_dev"."excel_field_mapping"), 2, 'name', '["Tên khách hàng (*)","Tên khách hàng"]');INSERT INTO "vnpt_dev"."excel_field_mapping" ("id", "type", "db_field", "excel_fields") VALUES ((SELECT MAX(id)+1 FROM "vnpt_dev"."excel_field_mapping"), 2, 'customer_type', '["Đối tượng khách hàng (*)","Đ<PERSON><PERSON> tượng khách hàng"]');

DROP VIEW IF EXISTS view_enterprise_classification;
CREATE VIEW view_enterprise_classification AS
 WITH last_login AS (
         SELECT DISTINCT ON (mhistorylogin.user_id) mhistorylogin.user_id,
            mhistorylogin.created_at
           FROM vnpt_dev.history_login mhistorylogin
          ORDER BY mhistorylogin.user_id, mhistorylogin.created_at DESC
        ), user_subs AS (
         SELECT msubscriptions.user_id,
            count(msubscriptions.id) FILTER (WHERE msubscriptions.status = 1 OR msubscriptions.status = 2) AS num_active_or_trial,
            count(msubscriptions.id) FILTER (WHERE msubscriptions.status = 3 OR msubscriptions.status = 4) AS num_cancel_or_finish
           FROM vnpt_dev.subscriptions msubscriptions
          WHERE msubscriptions.deleted_flag = 1 AND msubscriptions.confirm_status = 1
          GROUP BY msubscriptions.user_id
        ), user_classification AS (
         SELECT DISTINCT ON (musers.tin) musers.id AS user_id,
            musers.tin,
            musers.status,
            musers.created_at,
            musers.customer_type,
            vusersubs.num_active_or_trial,
            vusersubs.num_cancel_or_finish,
            vlastlogin.created_at::date AS last_login,
                CASE
                    WHEN vusersubs.num_active_or_trial > 0 THEN 0
                    WHEN vusersubs.num_active_or_trial = 0 AND vusersubs.num_cancel_or_finish > 0 AND vlastlogin.created_at::date IS NOT NULL AND vlastlogin.created_at::date < (now() - '90 days'::interval)::date THEN 1
                    ELSE 2
                END AS type
           FROM vnpt_dev.users musers
             LEFT JOIN user_subs vusersubs ON musers.id = vusersubs.user_id
             LEFT JOIN last_login vlastlogin ON musers.id = vlastlogin.user_id
          WHERE musers.deleted_flag = 1
          ORDER BY musers.tin, (COALESCE(musers.created_at, '1970-01-01 00:00:00'::timestamp without time zone)) DESC
        )
 SELECT menterprise.id AS enterprise_id,
    muserclassify.user_id,
    COALESCE(muserclassify.num_active_or_trial, 0::bigint) + COALESCE(muserclassify.num_cancel_or_finish, 0::bigint) AS num_subs,
    muserclassify.type,
    muserclassify.status,
    muserclassify.created_at,
    muserclassify.customer_type
   FROM user_classification muserclassify
     LEFT JOIN vnpt_dev.enterprise menterprise ON muserclassify.tin::text = menterprise.tin::text
  WHERE muserclassify.tin IS NOT NULL AND menterprise.id IS NOT NULL