-- back-up table variant và variant_draft --
-- table variant
drop table if exists "vnpt_dev"."variant_bk";
CREATE TABLE vnpt_dev."variant_bk" (LIKE vnpt_dev.variant INCLUDING ALL);
INSERT INTO vnpt_dev."variant_bk" (SELECT * FROM vnpt_dev."variant");
-- table variant_draft
drop table if exists "vnpt_dev"."variant_draft_bk";
CREATE TABLE vnpt_dev."variant_draft_bk" (LIKE vnpt_dev.variant_draft INCLUDING ALL);
INSERT INTO vnpt_dev."variant_draft_bk" (SELECT * FROM vnpt_dev."variant_draft");

-- restore --
-- drop table if exists vnpt_dev.variant;
-- alter table vnpt_dev.variant_bk rename to variant;
-- drop table if exists vnpt_dev.variant_draft;
-- alter table vnpt_dev.variant_draft_bk rename to variant_draft;
-- alter sequence vnpt_dev.variant_id_seq rename to variant_id_seq_tmp;
-- alter sequence vnpt_dev.variant_draft_id_seq rename to variant_id_seq;
-- alter sequence vnpt_dev.variant_id_seq_tmp rename to variant_draft_id_seq;

-- copy new draft id to variant --
alter table vnpt_dev.variant_draft drop column if exists "variant_draft_id";
alter table "vnpt_dev"."variant_draft"
    add column if not exists "variant_draft_id" int8;
update vnpt_dev.variant_draft set variant_draft_id = variant_id where variant_id is not null;

-- thêm các column để đảm bảo run thành công
-- alter table "vnpt_dev"."variant"
-- add column if not exists "variant_draft_id" int8;
-- comment on column "vnpt_dev"."variant"."variant_draft_id" is '[NEW] Id bảng variant_draft (đổi logic thành 1 variant draft - n variant)';
--
-- alter table "vnpt_dev"."variant"
-- add column if not exists "approved" int8;
-- comment on column "vnpt_dev"."variant"."approved" is '[NEW] Trạng thái phê duyệt (0 - chưa duyệt, 1 - đã duyệt, 2 - chờ duyệt, 3 - từ chối)';

-- rename table -> swap data của bảng variant <-> variant_draft --
alter table vnpt_dev.variant rename to variant_tmp;
alter table vnpt_dev.variant_draft rename to variant;
alter table vnpt_dev.variant_tmp rename to variant_draft;

-- rename sequence of table --
alter sequence vnpt_dev.variant_id_seq rename to variant_id_seq_tmp;
alter sequence vnpt_dev.variant_draft_id_seq rename to variant_id_seq;
alter sequence vnpt_dev.variant_id_seq_tmp rename to variant_draft_id_seq;

-- cập nhật lại thông tin biến thể trong thuê bao --
alter table "vnpt_dev"."subscriptions"
    add column if not exists "variant_id" int8;
comment on column "vnpt_dev"."subscriptions"."variant_id" is 'Id phiên bản biến thể đã mua (bảng variant)';
comment on column "vnpt_dev"."subscriptions"."variant_draft_id" is 'Id draft biến thể đã mua theo logic cũ 1 variant - n draft nay là id của variant tương ứng (cột này để backup không dùng để query, các query sử dụng cột variant_id)';
-- set lại thông tin biến thể đã mua vào cột variant_id --
update vnpt_dev.subscriptions
set variant_id = variant_draft_id
where variant_draft_id is not null;

-- drop column not use from old table --
alter table vnpt_dev.variant drop column if exists "variant_id";
alter table vnpt_dev.variant_draft drop column if exists "variant_draft_id";
-- alter table vnpt_dev.subscriptions drop column if exists "variant_draft_id";