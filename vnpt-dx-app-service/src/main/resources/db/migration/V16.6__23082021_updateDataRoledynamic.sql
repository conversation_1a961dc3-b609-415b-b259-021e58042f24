TRUNCATE TABLE vnpt_dev.apis RESTART IDENTITY;
INSERT INTO vnpt_dev.apis (id, api_path, api_code, "method")
VALUES (1, '/api/users-admin/{id}', 'ROLE_ADMIN_UPDATE_ACCOUNTS_ADMIN', 'PUT'),
       (2, '/api/users-admin/{id}/customers', 'ROLE_ADMIN_UPDATE_ACCOUNTS_SME', 'PUT'),
	(3, '/api/users-sme/smeemployees', 'ROLE_SME_CREATED_ACCOUNTS', 'POST'),
	(4, '/api/admin-portal/services', 'ROLE_ADMIN_GET_SERVICE', 'GET'),
	(5, '/api/dev-portal/services', 'ROLE_DEV_GET_SERVICE', 'GET'),   
	(6, '/api/portal/subscription', 'ROLE_GET_SUBSCRIPTIONS', 'GET'),
	(7, '/api/dev-portal/pricing/service/{serviceId}', 'ROLE_DEV_CREATED_PRICING', 'POST'),
	(8, '/api/dev-portal/services/{id}/displayed/{status}', 'ROLE_DEVELOPER_HIDDEN_SERVICE', 'PUT'),
       	(9, '/api/admin-portal/pricing/approve/{id}', 'ROLE_ADMIN_APPROVED_PRICING', 'PUT'),
	(10, '/api/users-admin', 'ROLE_ADMIN_ACCOUNTS', 'GET'), 
	(11, '/api/users-admin/{id}/status/{status}', 'ROLE_ADMIN_ACCOUNTS_STATUS', 'PUT'),
	(12, '/api/users-sme/import', 'ROLE_SME_IMPORT_ACCOUNTS_BY_FILE', 'POST'),
	(13, '/api/dev-portal/pricing/service/{serviceId}', 'ROLE_DEV_VIEW_LIST_PRICINGS', 'GET'),
	(14, '/api/admin-portal/pricing/service/{serviceId}', 'ROLE_ADMIN_VIEW_LIST_PRICINGS', 'GET'),
	(15, '/api/admin-portal/pricing/{id}/request-for-approval', 'ROLE_DEV_REQUEST_APPROVE_PRICING', 'PUT'),
	(16, '/api/portal/subscription/pricing', 'ROLE_DEV_CREATE_SUBSCRIPTION_PRICING', 'POST'),
       	(17, '/api/dev-portal/combos/{id}', 'ROLE_DEV_UPDATE_SUBSCRIPTION_COMBO', 'PUT'),
	(18, '/api/dev-portal/pricing/{pricingId}/service/{serviceId}', 'ROLE_DEV_UPDATE_PRICING', 'PUT'),
	(19, '/api/portal/subscription/update/{subscriptionId}/pricing', 'ROLE_ADMIN_DEV_SWAP_PRICING', 'PUT'),
       	(20, '/api/sme-portal/subscription/update/{id}/pricing', 'ROLE_SME_SWAP_PRICING', 'PUT');

INSERT INTO vnpt_dev.api_permission (api_id, permission_id)
VALUES (1 , 27),
       (2 , 30),
	(3, 33),
	(4, 56),
	(5, 56),
	(6, 131),
	(7, 64),
	(8 , 62),
       	(9 , 67),
	(10, 25),
	(11, 28),
	(12 , 38), 
	(13, 168), 
	(14, 168), 
	(15, 66), 
	(16 , 133),
 	(17, 135),
	(18, 65), 
	(19, 137), 
	(20, 137);

INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) 
VALUES (168, 'Xem danh sách kế hoạch định giá SPDV', 'XEM_DANH_SACH_GOI_DICH_VU_1', 63, 9993600);