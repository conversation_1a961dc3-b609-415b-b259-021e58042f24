DELETE FROM "vnpt_dev"."mail_template" WHERE code IN ('SCN-05');
DELETE FROM "vnpt_dev"."param_email" WHERE mail_template_code IN ('SCN-05');
DELETE FROM "vnpt_dev"."action_notification" WHERE action_code IN ('SCN-05');

--===MAIL-TEMPLATE--- SCN-05
INSERT INTO vnpt_dev.mail_template (code, name, status, content_html, content_html_default, content_text,
                                    content_text_default, parent_code, title, title_default, priority_order, email_type)
VALUES ('SCN-05', 'Đăng ký thuê bao thành công', 1, '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đăng ký thuê bao thành công</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
                <p style="margin: 0;">Doanh nghiệp của bạn đã đăng ký thành công dịch vụ </span><span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span></p>
				<p style="margin: 0;">Mã giao dịch: <span>$CODE_TRANSACTION</span></p>
				<p style="margin: 0;">Người đăng ký: <span>$NAME_REGISTER </span><span>$POSITION</span></p>
				<p style="margin: 0;">Ngày đăng ký: <span>$DATE_SUBSCRIPTION</span></p>
				<p style="margin: 0;">Ngày bắt đầu sử dụng: <span>$DATE_START</span></p>
				<p style="margin: 0;">Ngày kết thúc sử dụng: <span>$DATE_END</span></p>
				<p style="margin: 0;">Số tiền cần thanh toán: <span>$AMOUNT</span></p>
				<p style="margin: 0;">Trạng thái thanh toán: <span>$PAYMENT_STATUS</span></p>

                <p style="margin: 0;">Truy cập đường link dưới đây để sử dụng dịch vụ.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_USE_SUBS" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Sử dụng dịch vụ</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>
', '<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>


</head>
<body style="padding: 40px;margin: 0 auto;max-width: 600px;background-color: #F8F8F8;font-family: &quot;Montserrat&quot;, Helvetica, sans-serif;">
    <div class="container" style="background-color: #FFFFFF;">
        <div class="logo-container" style="height: 80px;display: flex;justify-content: center;align-items: center;box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);">
            $HEADER
        </div>
        <div class="content-container" style="padding: 40px;">
            <div class="title-container" style="text-align: center;padding: 40px 0 60px;">
                <img class="title-icon" src="$IMG_PATH/resources/upload/file/mail/images/icon_sb.png" alt="Thuê bao">
                <p class="main-title" style="margin: 0;line-height: 28px;font-size: 20px;font-weight: 700;color: #2C3D94;text-transform: uppercase;margin-top: 30px;">Đăng ký thuê bao thành công</p>
            </div>
            <div class="main-content" style="line-height: 22px;font-size: 14px;letter-spacing: .3px;">
                <p class="mb-m" style="margin: 0;margin-bottom: 20px;">Xin chào <span> $USER</span>,</p>
                <p style="margin: 0;">Doanh nghiệp của bạn đã đăng ký thành công dịch vụ </span><span>$NAME_SERVICE</span> - <span>$NAME_PRICING</span></p>
				<p style="margin: 0;">Mã giao dịch: <span>$CODE_TRANSACTION</span></p>
				<p style="margin: 0;">Người đăng ký: <span>$NAME_REGISTER </span><span>$POSITION</span></p>
				<p style="margin: 0;">Ngày đăng ký: <span>$DATE_SUBSCRIPTION</span></p>
				<p style="margin: 0;">Ngày bắt đầu sử dụng: <span>$DATE_START</span></p>
				<p style="margin: 0;">Ngày kết thúc sử dụng: <span>$DATE_END</span></p>
				<p style="margin: 0;">Số tiền cần thanh toán: <span>$AMOUNT</span></p>
				<p style="margin: 0;">Trạng thái thanh toán: <span>$PAYMENT_STATUS</span></p>

                <p style="margin: 0;">Truy cập đường link dưới đây để sử dụng dịch vụ.</p>
                <p class="btn" style="margin: 40px auto;text-align: center;">
                    <a href="$LINK_USE_SUBS" style="text-decoration: none;color: #ffffff;background: #2C3D94;padding: 12px 16px;border-radius: 12px;font-weight: 500;">Sử dụng dịch vụ</a>
                </p>
                <p class="mt-m" style="margin: 0;margin-top: 20px;">Trân trọng,</p>
                <p style="margin: 0;">Đội ngũ phát triển nền tảng oneSME</p>
            </div>
        </div>
        <div class="footer-container" style="padding: 40px;">
            $FOOTER
        </div>
    </div>
</body>
</html>
', NULL, NULL, 'SB', 'Đăng ký thuê bao thành công','Đăng ký thuê bao thành công', 23044, 1);

--=========param_email
INSERT INTO vnpt_dev.param_email (id_mail_template,param_name,remark,mail_template_code,param_default_value) VALUES
((select id from mail_template where code = 'SBN-05'),'$CODE_TRANSACTION','[Mã giao dịch]','SBN-05',NULL),
((select id from mail_template where code = 'SBN-05'),'$LINK_USE_SUBS','[Link sử dụng dịch vụ]','SBN-05',NULL),
((select id from mail_template where code = 'SBN-05'),'$PAYMENT_STATUS','[Trạng thái thanh toán của hóa đơn]','SBN-05',NULL),
((select id from mail_template where code = 'SBN-05'),'$NAME_SERVICE','[Tên dịch vụ]','SBN-05','VNPT Pharmacy'),
((select id from mail_template where code = 'SBN-05'),'$NAME_PRICING','[Tên gói dịch vụ]','SBN-05','Gói cơ bản 3 tháng'),
((select id from mail_template where code = 'SBN-05'),'$NAME_REGISTER','[Tên người đăng ký]','SBN-05','Nguyễn Ngọc Viên'),
((select id from mail_template where code = 'SBN-05'),'$POSITION','[Vai trò người đăng ký]','SBN-05','Nhân viên'),
((select id from mail_template where code = 'SBN-05'),'$DATE_SUBSCRIPTION','[Ngày đăng ký]','SBN-05','06/03/2021 10:27:51'),
((select id from mail_template where code = 'SBN-05'),'$DATE_START','[Ngày bắt đầu sử dụng thuê bao]','SBN-05','06/03/2021 10:27:51'),
((select id from mail_template where code = 'SBN-05'),'$DATE_END','[Ngày kết thúc sử dụng thuê bao]','SBN-05','06/03/2021 10:27:51'),
((select id from mail_template where code = 'SBN-05'),'$AMOUNT','[Số tiền]','SBN-05','300000'),
((select id from mail_template where code = 'SBN-05'),'$USER','[Người dùng]','SBN-05','Nguyễn Ngọc Viện'),
((select id from mail_template where code = 'SBN-05'),'$HOTLINE_TOANQUOC','[Hotline oneSME]','SBN-05','942827446'),
((select id from mail_template where code = 'SBN-05'),'$HOTLINE_TINH','[Hotline tỉnh]','SBN-05','');

--===action notification==
INSERT INTO vnpt_dev.action_notification (name,is_send_email,is_send_sms,is_notification,parent_id,created_by,created_at,modified_by,modified_at,receiver,action_code,allow_change_email,allow_change_sms,allow_change_notification,priority_order,is_send_telegram,allow_change_telegram) VALUES
('Khách hàng đăng kí thuê bao thành công với trạng thái thuê bao là Hoạt động ',1,1,1,94,'system',NULL,NULL,'2021-10-31 00:00:00','SME admin của khách hàng tạo thuê bao, SME tạo thuê bao','SCN-05','B','B','B',29102,NULL,NULL);
