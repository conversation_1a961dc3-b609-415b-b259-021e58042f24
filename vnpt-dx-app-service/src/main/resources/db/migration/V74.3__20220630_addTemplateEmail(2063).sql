INSERT INTO "vnpt_dev"."mail_template" ("id", "code", "name", "status", "content_html", "content_html_default", "content_text", "content_text_default", "parent_code", "title", "title_default", "priority_order", "email_type") VALUES ((SELECT MAX(id)+1 FROM vnpt_dev.mail_template), 'MIG-02', 'Thông báo xử lý xong file đồng bộ', 1, '<html><head>
					<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
					<link rel="preconnect" href="https://fonts.googleapis.com">
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
					
			<style id="ckeditor-style">
			 	:root{--ck-color-image-caption-background:hsl(0, 0%, 97%);--ck-color-image-caption-text:hsl(0, 0%, 20%);--ck-color-mention-background:hsla(341, 100%, 30%, 0.1);--ck-color-mention-text:hsl(341, 100%, 30%);--ck-color-table-caption-background:hsl(0, 0%, 97%);--ck-color-table-caption-text:hsl(0, 0%, 20%);--ck-highlight-marker-blue:hsl(201, 97%, 72%);--ck-highlight-marker-green:hsl(120, 93%, 68%);--ck-highlight-marker-pink:hsl(345, 96%, 73%);--ck-highlight-marker-yellow:hsl(60, 97%, 73%);--ck-highlight-pen-green:hsl(112, 100%, 27%);--ck-highlight-pen-red:hsl(0, 85%, 49%);--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing) / 2);--ck-todo-list-checkmark-size:16px}.ck-content .image.image_resized{max-width:100%;display:block;-webkit-box-sizing:border-box;box-sizing:border-box}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck-content .image>figcaption{display:table-caption;caption-side:bottom;word-break:break-word;color:var(--ck-color-image-caption-text);background-color:var(--ck-color-image-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .image{display:table;clear:both;text-align:center;margin:.9em auto;min-width:50px}.ck-content .image img{display:block;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;max-width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.ck-content .image-inline picture{display:-webkit-box;display:-ms-flexbox;display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;-ms-flex-negative:1;flex-shrink:1;max-width:100%}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-right:0;margin-left:auto}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-top:var(--ck-inline-image-style-spacing);margin-bottom:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck-content .marker-yellow{background-color:var(--ck-highlight-marker-yellow)}.ck-content .marker-green{background-color:var(--ck-highlight-marker-green)}.ck-content .marker-pink{background-color:var(--ck-highlight-marker-pink)}.ck-content .marker-blue{background-color:var(--ck-highlight-marker-blue)}.ck-content .pen-red{color:var(--ck-highlight-pen-red);background-color:transparent}.ck-content .pen-green{color:var(--ck-highlight-pen-green);background-color:transparent}.ck-content .text-tiny{font-size:.7em}.ck-content .text-small{font-size:.85em}.ck-content .text-big{font-size:1.4em}.ck-content .text-huge{font-size:1.8em}.ck-content hr{margin:15px 0;height:4px;background:#ddd;border:0}.ck-content pre{padding:1em;color:#353535;background:rgba(199,199,199,.3);border:1px solid #c4c4c4;border-radius:2px;text-align:left;direction:ltr;-moz-tab-size:4;-o-tab-size:4;tab-size:4;white-space:pre-wrap;font-style:normal;min-width:200px}.ck-content pre code{background:unset;padding:0;border-radius:0}.ck-content blockquote{overflow:hidden;padding-right:1.5em;padding-left:1.5em;margin-left:0;margin-right:0;font-style:italic;border-left:solid 5px #ccc}.ck-content[dir=rtl] blockquote{border-left:0;border-right:solid 5px #ccc}.ck-content code{background-color:rgba(199,199,199,.3);padding:.15em;border-radius:2px}.ck-content .table>figcaption{display:table-caption;caption-side:top;word-break:break-word;text-align:center;color:var(--ck-color-table-caption-text);background-color:var(--ck-color-table-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .table{margin:.9em auto;display:table}.ck-content .table table{border-collapse:collapse;border-spacing:0;width:100%;height:100%;border:1px double #b2b2b2}.ck-content .table table td,.ck-content .table table th{min-width:2em;padding:.4em;border:1px solid #bfbfbf}.ck-content .table table th{font-weight:700;background:#000}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content .page-break{position:relative;clear:both;padding:5px 0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ck-content .page-break::after{content:"";position:absolute;border-bottom:2px dashed #c4c4c4;width:100%}.ck-content .page-break__label{position:relative;z-index:1;padding:.3em .6em;display:block;text-transform:uppercase;border:1px solid #c4c4c4;border-radius:2px;font-family:Helvetica,Arial,Tahoma,Verdana,Sans-Serif;font-size:.75em;font-weight:700;color:#333;background:#fff;-webkit-box-shadow:2px 2px 1px rgba(0,0,0,.15);box-shadow:2px 2px 1px rgba(0,0,0,.15);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ck-content .media{clear:both;margin:.9em 0;display:block;min-width:15em}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;display:inline-block;position:relative;width:var(--ck-todo-list-checkmark-size);height:var(--ck-todo-list-checkmark-size);vertical-align:middle;border:0;left:-25px;margin-right:-15px;right:0;margin-left:0}.ck-content .todo-list .todo-list__label>input::before{display:block;position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;content:"";width:100%;height:100%;border:1px solid #333;border-radius:2px;-webkit-transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border;transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border}.ck-content .todo-list .todo-list__label>input::after{display:block;position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;pointer-events:none;content:"";left:calc(var(--ck-todo-list-checkmark-size)/ 3);top:calc(var(--ck-todo-list-checkmark-size)/ 5.3);width:calc(var(--ck-todo-list-checkmark-size)/ 5.3);height:calc(var(--ck-todo-list-checkmark-size)/ 2.6);border-style:solid;border-color:transparent;border-width:0 calc(var(--ck-todo-list-checkmark-size)/ 8) calc(var(--ck-todo-list-checkmark-size)/ 8) 0;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ck-content .todo-list .todo-list__label>input[checked]::before{background:#25ab33;border-color:#25ab33}.ck-content .todo-list .todo-list__label>input[checked]::after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content span[lang]{font-style:italic}.ck-content .mention{background:var(--ck-color-mention-background);color:var(--ck-color-mention-text)}@media print{.ck-content .page-break{padding:0}.ck-content .page-break::after{display:none}}.ck-content img{max-width:100%}
			</style>
			<style id="main-style">
				:root {
					--color-primary: #2c3d94;
					--ck-border-radius: 0.25rem;
					--border-radius-base: 0.25rem;
					--select-dropdown-height: 2rem;
				}
				.ck-content a {
					color: var(--color-primary);
					text-decoration: none;
				}
				.ck-content p,
				.ck-content a {
					font-size: 1rem;
				}
				.ck-content {
					box-sizing: border-box;
				}
				.ck-content *{
					box-sizing: border-box;
					height: unset!important;
				}


			</style>
			
				<title>Xem mẫu email</title></head>
				<body>
					<div class="main ck-content" style="padding: 40px;margin: 0 auto; width: 600px;background-color: #F8F8F8; font-family: ''Montserrat'', Helvetica, sans-serif;">
						<div class="email-container" style="background-color: #FFFFFF;">
							<div class="logo-container" style="padding: 20px 20px 5px 20px;">
								$HEADER
							</div>
							<div classname="logo-container-divider" style="border-bottom: 1px solid #cdcdcd;"></div>
							<div class="content-container" style="padding: 20px 40px 10px 40px;">
								<p style="margin-left:0;">Xin chào quản trị viên.</p><p style="margin-left:0;">Đã xử lý xong file đồng bộ dữ liệu. Bạn vui lòng kiểm tra trong danh sách đồng bộ.</p><p style="margin-left:0;">Trân trọng,</p><p style="margin-left:0;">Đội ngũ phát triển nền tảng oneSME</p>
							</div>
							<div class="footer-container" style="padding: 20px 40px 10px 40px;">
								$FOOTER
							</div>
						</div>
					</div>
				
				
				</body></html>', '<!DOCTYPE html><html><head>
					<meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
					<link rel="preconnect" href="https://fonts.googleapis.com">
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
					
			<style id="ckeditor-style">
			 	:root{--ck-color-image-caption-background:hsl(0, 0%, 97%);--ck-color-image-caption-text:hsl(0, 0%, 20%);--ck-color-mention-background:hsla(341, 100%, 30%, 0.1);--ck-color-mention-text:hsl(341, 100%, 30%);--ck-color-table-caption-background:hsl(0, 0%, 97%);--ck-color-table-caption-text:hsl(0, 0%, 20%);--ck-highlight-marker-blue:hsl(201, 97%, 72%);--ck-highlight-marker-green:hsl(120, 93%, 68%);--ck-highlight-marker-pink:hsl(345, 96%, 73%);--ck-highlight-marker-yellow:hsl(60, 97%, 73%);--ck-highlight-pen-green:hsl(112, 100%, 27%);--ck-highlight-pen-red:hsl(0, 85%, 49%);--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing) / 2);--ck-todo-list-checkmark-size:16px}.ck-content .image.image_resized{max-width:100%;display:block;-webkit-box-sizing:border-box;box-sizing:border-box}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck-content .image>figcaption{display:table-caption;caption-side:bottom;word-break:break-word;color:var(--ck-color-image-caption-text);background-color:var(--ck-color-image-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .image{display:table;clear:both;text-align:center;margin:.9em auto;min-width:50px}.ck-content .image img{display:block;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;max-width:100%;-webkit-box-align:start;-ms-flex-align:start;align-items:flex-start}.ck-content .image-inline picture{display:-webkit-box;display:-ms-flexbox;display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1;-ms-flex-negative:1;flex-shrink:1;max-width:100%}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-right:0;margin-left:auto}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-top:var(--ck-inline-image-style-spacing);margin-bottom:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck-content .marker-yellow{background-color:var(--ck-highlight-marker-yellow)}.ck-content .marker-green{background-color:var(--ck-highlight-marker-green)}.ck-content .marker-pink{background-color:var(--ck-highlight-marker-pink)}.ck-content .marker-blue{background-color:var(--ck-highlight-marker-blue)}.ck-content .pen-red{color:var(--ck-highlight-pen-red);background-color:transparent}.ck-content .pen-green{color:var(--ck-highlight-pen-green);background-color:transparent}.ck-content .text-tiny{font-size:.7em}.ck-content .text-small{font-size:.85em}.ck-content .text-big{font-size:1.4em}.ck-content .text-huge{font-size:1.8em}.ck-content hr{margin:15px 0;height:4px;background:#ddd;border:0}.ck-content pre{padding:1em;color:#353535;background:rgba(199,199,199,.3);border:1px solid #c4c4c4;border-radius:2px;text-align:left;direction:ltr;-moz-tab-size:4;-o-tab-size:4;tab-size:4;white-space:pre-wrap;font-style:normal;min-width:200px}.ck-content pre code{background:unset;padding:0;border-radius:0}.ck-content blockquote{overflow:hidden;padding-right:1.5em;padding-left:1.5em;margin-left:0;margin-right:0;font-style:italic;border-left:solid 5px #ccc}.ck-content[dir=rtl] blockquote{border-left:0;border-right:solid 5px #ccc}.ck-content code{background-color:rgba(199,199,199,.3);padding:.15em;border-radius:2px}.ck-content .table>figcaption{display:table-caption;caption-side:top;word-break:break-word;text-align:center;color:var(--ck-color-table-caption-text);background-color:var(--ck-color-table-caption-background);padding:.6em;font-size:.75em;outline-offset:-1px}.ck-content .table{margin:.9em auto;display:table}.ck-content .table table{border-collapse:collapse;border-spacing:0;width:100%;height:100%;border:1px double #b2b2b2}.ck-content .table table td,.ck-content .table table th{min-width:2em;padding:.4em;border:1px solid #bfbfbf}.ck-content .table table th{font-weight:700;background:#000}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-content .page-break{position:relative;clear:both;padding:5px 0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ck-content .page-break::after{content:"";position:absolute;border-bottom:2px dashed #c4c4c4;width:100%}.ck-content .page-break__label{position:relative;z-index:1;padding:.3em .6em;display:block;text-transform:uppercase;border:1px solid #c4c4c4;border-radius:2px;font-family:Helvetica,Arial,Tahoma,Verdana,Sans-Serif;font-size:.75em;font-weight:700;color:#333;background:#fff;-webkit-box-shadow:2px 2px 1px rgba(0,0,0,.15);box-shadow:2px 2px 1px rgba(0,0,0,.15);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ck-content .media{clear:both;margin:.9em 0;display:block;min-width:15em}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;display:inline-block;position:relative;width:var(--ck-todo-list-checkmark-size);height:var(--ck-todo-list-checkmark-size);vertical-align:middle;border:0;left:-25px;margin-right:-15px;right:0;margin-left:0}.ck-content .todo-list .todo-list__label>input::before{display:block;position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;content:"";width:100%;height:100%;border:1px solid #333;border-radius:2px;-webkit-transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border;transition:250ms ease-in-out box-shadow,250ms ease-in-out background,250ms ease-in-out border}.ck-content .todo-list .todo-list__label>input::after{display:block;position:absolute;-webkit-box-sizing:content-box;box-sizing:content-box;pointer-events:none;content:"";left:calc(var(--ck-todo-list-checkmark-size)/ 3);top:calc(var(--ck-todo-list-checkmark-size)/ 5.3);width:calc(var(--ck-todo-list-checkmark-size)/ 5.3);height:calc(var(--ck-todo-list-checkmark-size)/ 2.6);border-style:solid;border-color:transparent;border-width:0 calc(var(--ck-todo-list-checkmark-size)/ 8) calc(var(--ck-todo-list-checkmark-size)/ 8) 0;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ck-content .todo-list .todo-list__label>input[checked]::before{background:#25ab33;border-color:#25ab33}.ck-content .todo-list .todo-list__label>input[checked]::after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content span[lang]{font-style:italic}.ck-content .mention{background:var(--ck-color-mention-background);color:var(--ck-color-mention-text)}@media print{.ck-content .page-break{padding:0}.ck-content .page-break::after{display:none}}.ck-content img{max-width:100%}
			</style>
			<style id="main-style">
				:root {
					--color-primary: #2c3d94;
					--ck-border-radius: 0.25rem;
					--border-radius-base: 0.25rem;
					--select-dropdown-height: 2rem;
				}
				.ck-content a {
					color: var(--color-primary);
					text-decoration: none;
				}
				.ck-content p,
				.ck-content a {
					font-size: 1rem;
				}
				.ck-content {
					box-sizing: border-box;
				}
				.ck-content *{
					box-sizing: border-box;
					height: unset!important;
				}


			</style>
			
				<title>Xem mẫu email</title></head>
				<body>
					<div class="main ck-content" style="padding: 40px;margin: 0 auto; width: 600px;background-color: #F8F8F8; font-family: ''Montserrat'', Helvetica, sans-serif;">
						<div class="email-container" style="background-color: #FFFFFF;">
							<div class="logo-container" style="padding: 20px 20px 5px 20px;">
								$HEADER
							</div>
							<div classname="logo-container-divider" style="border-bottom: 1px solid #cdcdcd;"></div>
							<div class="content-container" style="padding: 20px 40px 10px 40px;">
								<p style="margin-left:0;">Xin chào quản trị viên.</p><p style="margin-left:0;">Đã xử lý xong file đồng bộ dữ liệu. Bạn vui lòng kiểm tra trong danh sách đồng bộ.</p><p style="margin-left:0;">Trân trọng,</p><p style="margin-left:0;">Đội ngũ phát triển nền tảng oneSME</p>
							</div>
							<div class="footer-container" style="padding: 20px 40px 10px 40px;">
								$FOOTER
							</div>
						</div>
					</div>
				
				
				</body></html>', NULL, NULL, 'MIG', 'Thông báo xử lý xong file đồng bộ', 'Thông báo xử lý xong file đồng bộ', 28002, NULL);
