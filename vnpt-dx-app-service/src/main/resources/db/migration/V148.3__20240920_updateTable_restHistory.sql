-- <PERSON><PERSON><PERSON> bảng dữ liệu lưu lịch sử gọi REST API
DROP TABLE IF EXISTS "vnpt_dev"."rest_history";
CREATE TABLE "vnpt_dev"."rest_history" (
  "id" bigserial,
  "partition_id" date NOT NULL DEFAULT CAST(now() AS DATE),
  "created_at" timestamp NOT NULL,
  "http_method" varchar(10) NOT NULL,
  "url" varchar(512) NOT NULL,
  "http_header" jsonb,
  "request" jsonb,
  "response" jsonb,
  "response_time" int8,
  "exception" text,
  PRIMARY KEY ("id", "partition_id")
) PARTITION BY RANGE (partition_id);

COMMENT ON COLUMN "vnpt_dev"."rest_history"."partition_id" IS 'ID sử dụng để partition bảng history';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."created_at" IS 'Thời gian gọi REST API';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."http_method" IS 'HTTP method';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."url" IS 'URL của bản tin yêu cầu';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."http_header" IS 'HTTP header của bản tin yêu cầu';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."request" IS 'HTTP body của bản tin yêu cầu';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."response" IS 'HTTP body của bản tin phản hồi';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."response_time" IS 'Thời gian phản hồi (milisecond)';
COMMENT ON COLUMN "vnpt_dev"."rest_history"."exception" IS 'Thông tin exception (nếu có)';
COMMENT ON TABLE "vnpt_dev"."rest_history" IS 'Bảng lưu lịch sử gọi REST API';

CREATE TABLE rest_history_2024_09 PARTITION OF rest_history FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

-- Xóa function cũ
DROP FUNCTION IF EXISTS vnpt_dev.func_create_partition_by_month(varchar);
-- Tạo function cho phép tạo partition của tháng sau cho một bảng bất kì
CREATE OR REPLACE FUNCTION vnpt_dev.func_create_partition_by_month(tbl_name varchar) RETURNS int2 AS $$
DECLARE
    partition_name TEXT;
    sql_query TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- Lấy thời gian là tháng sau
    EXECUTE 'SELECT cast(date_trunc(''month'', now() + INTERVAL ''1 month'') as date)' INTO start_date;
    end_date := cast(start_date + INTERVAL '1 month' as date);

    -- Tạo tên partition dựa trên tháng
    partition_name := tbl_name || '_' || to_char(start_date, 'YYYY_MM');

    -- Kiểm tra nếu partition đã tồn tại
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class
        WHERE relname = partition_name
    ) THEN
        -- Tạo partition mới nếu chưa tồn tại
        sql_query := format(
            'CREATE TABLE vnpt_dev.%I PARTITION OF vnpt_dev.%I FOR VALUES FROM (%L) TO (%L)',
            partition_name,
            tbl_name,
            start_date,
            end_date
        );
        RAISE NOTICE 'SQL query % ', sql_query;
        EXECUTE sql_query;
        RAISE NOTICE 'Partition % created.', partition_name;
        RETURN 1;
    ELSE
        RAISE NOTICE 'Partition % already exists.', partition_name;
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;