package com.service.coupon.impl;

import com.dto.coupons.AddonCouponResDTO;
import com.entity.coupons.Coupon;
import com.repository.coupons.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Service
public class CouponConditionService {
    private CouponEnterpriseRepository couponEnterpriseRepository;
    private CouponPricingRepository couponPricingRepository;
    private CouponAddonRepository couponAddonRepository;
    private CouponSupplierRepository couponSupplierRepository;
    private CouponRepository couponRepository;
    private final NumberFormat formatter = NumberFormat.getCurrencyInstance(Locale.GERMAN);

    @Autowired
    public void setCouponRepository(CouponRepository couponRepository) {
        this.couponRepository = couponRepository;
    }

    @Autowired
    public void setCouponSupplierRepository(CouponSupplierRepository couponSupplierRepository) {
        this.couponSupplierRepository = couponSupplierRepository;
    }

    @Autowired
    public void setCouponAddonRepository(CouponAddonRepository couponAddonRepository) {
        this.couponAddonRepository = couponAddonRepository;
    }

    @Autowired
    public void setCouponPricingRepository(CouponPricingRepository couponPricingRepository) {
        this.couponPricingRepository = couponPricingRepository;
    }

    @Autowired
    public void setCouponEnterpriseRepository(CouponEnterpriseRepository couponEnterpriseRepository) {
        this.couponEnterpriseRepository = couponEnterpriseRepository;
    }


    public  List<String> gen(Coupon c){
        List<String> conditions = new ArrayList<>();
        try{
            // điều kiện về số tiền nhỏ nhất, tối đa
            StringBuilder moneyCondition = new StringBuilder("Giảm ").append(getStringCurrency(c.getDiscountValue()));
            switch (c.getDiscountType()){
                case 0: moneyCondition.append("%"); break;
                case 1: moneyCondition.append("VNĐ"); break;
            }
            if(c.getMaximumPromotion() !=null && c.getMinimumAmount() > 0L){
                moneyCondition.append(" cho đơn hàng từ ").append(getStringCurrency(c.getMinimumAmount()));
            }
            if(c.getDiscountAmount() !=null && c.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0){
                moneyCondition.append(". Tối đa ").append(getStringCurrency(c.getDiscountAmount()));
            }
            conditions.add(moneyCondition.toString());

            // điều kiện tổng số lần áp dụng
            if(c.getMaximumPromotion() !=null &&  c.getMaximumPromotion() > 0){
                String countCondition = "Số lần khách hàng sử dụng tối đa " + c.getMaximumPromotion();
                conditions.add(countCondition);
            }
            // điều kiện áp dụng cho doanh nghiệp
            if(c.getEnterpriseType() !=null && c.getEnterpriseType() == 1){
                StringBuilder condition = new StringBuilder("Áp dụng cho sản phẩm của ");
                couponEnterpriseRepository.getCouponEnterprise(c.getId()).forEach(ce->{
                    condition.append(ce.getName()).append(" ");
                });
                conditions.add(condition.toString());
            }
            // Áp dụng cho sản phẩm
            if(c.getPricingType() !=null && c.getPricingType() == 1){
                StringBuilder condition = new StringBuilder("Áp dụng cho sản phẩm ");
                couponPricingRepository.getCouponPricing(c.getId()).forEach(ce->{
                    condition.append(ce.getPricingName()).append(" ");
                });
                conditions.add(condition.toString());
            }
            //Áp dụng cho dịch vụ bổ xung
            if(c.getAddonsType() !=null && c.getAddonsType() == 1){
                StringBuilder condition = new StringBuilder("Áp dụng cho dịch vụ bổ xung ");
                couponAddonRepository.getCouponAddon(c.getId()).forEach(ce->{
                    condition.append(ce.getName()).append(" ");
                });
                conditions.add(condition.toString());
            }
            //Áp dụng cho nhà cung cấp dịch vụ
            if(c.getSupplierType()!=null && c.getSupplierType() == 1){
                StringBuilder condition = new StringBuilder("Áp dụng cho nhà cung cấp dịch vụ ");
                couponSupplierRepository.getCouponSupplier(c.getId()).forEach(ce->{
                    condition.append(ce.getName()).append(" ");
                });
                conditions.add(condition.toString());
            }

            // Chiết khấu cho nhà cung cấp
            if(c.getDiscountSupplierType() !=null){
                switch ( c.getDiscountSupplierType()){
                    case 0: conditions.add("Chiếu khấu theo phí đăng ký"); break;
                    case 1: conditions.add("Chiết khấu theo phí hoa hồng"); break;
                }
            }

            // Số lần sử dụng
            if(c.getTimesUsedType() !=null){
                switch (c.getTimesUsedType()){
                    case 0: conditions.add("Số lần sử dụng: Vĩnh viễn"); break;
                    case 1: conditions.add("Số lần sử dụng: Giới hạn " + c.getLimitedQuantity() + " lần"); break;
                    case 2: conditions.add("Số lần sử dụng: Một lần"); break;
                }
            }

        } catch (Exception e){
            e.printStackTrace();
        }
        return conditions;
    }
    public  List<String> gen(AddonCouponResDTO c){
        Coupon coupon = couponRepository.findFirstById(c.getCouponId());
        return gen(coupon);
    }

    public  List<String> gen(Long couponId){
        Coupon coupon = couponRepository.findFirstById(couponId);
        return gen(coupon);
    }

    public String getStringCurrency(BigDecimal money){
        money = money.setScale(0, RoundingMode.HALF_EVEN);
        return formatter.format(money).replace("¤","").replace(",00","");
    }

    public String getStringCurrency(Long money){
        return formatter.format(money).replace("¤","").replace(",00","");
    }
}
