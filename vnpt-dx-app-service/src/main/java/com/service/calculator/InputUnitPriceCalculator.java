package com.service.calculator;

import java.math.BigDecimal;

/**
 * T<PERSON>h đơn giá gói đơn vị khi người dùng sửa giá gói
 * <AUTHOR>
 *
 */
public class InputUnitPriceCalculator extends UnitPriceCalculator implements InputPriceCalculator {

	public InputUnitPriceCalculator(BigDecimal freeQuantity, BigDecimal registerQuantity, BigDecimal price) {
		super(freeQuantity, registerQuantity, price);
	}

}
