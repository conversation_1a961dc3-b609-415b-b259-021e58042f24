package com.service.preorder;

import com.repository.collect_info_preorder.CollectInfoPreOrder;

import java.text.ParseException;

public interface CollectInfoPreOrderService {
    void save(CollectInfoPreOrder collectInfoPreOrder);

    void init(Long userId, String preOrderId);

    CollectInfoPreOrder findByPreOrderId(String preOrderId);

    void updateSigned(String preOrderId);

    void updateUserInfo(CollectInfoPreOrder collectInfoPreOrder) throws ParseException;
}
