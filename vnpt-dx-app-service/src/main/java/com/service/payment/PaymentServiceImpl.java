package com.service.payment;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.common.CommonRestTemplate;
import com.constant.EntitiesConstant;
import com.constant.OrderConstant;
import com.constant.PaymentConstant;
import com.constant.SubscriptionConstant;
import com.constant.SubscriptionConstant.MailParams;
import com.constant.enums.StatusCallAPIEnum;
import com.constant.enums.common.KeyGenType;
import com.constant.enums.credit_note.CreditNoteDetailStatusEnum;
import com.constant.enums.payment.PaymentQRStatusEnum;
import com.constant.enums.payment.PaymentStatusEnum;
import com.constant.enums.pricing.ChangePricingPaymentTimeEnum;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.subscription.ActionTypeEnum;
import com.constant.enums.subscription.OrderStatusEnum;
import com.constant.enums.subscription.StatusOrderEnum;
import com.constant.enums.subscription.SubsTypeEnum;
import com.constant.enums.transactionLog.ActivityCodeEnum;
import com.constant.enums.transactionLog.TransactionCodeEnum;
import com.constant.enums.transactionLog.TransactionLogStatusEnum;
import com.dto.actionNotification.ActionNotificationParamDTO;
import com.dto.contracts.ResponseEcontractDTO;
import com.dto.contracts.ResponseEcontractSSODTO;
import com.dto.coupons.mailParam.MailSendParamDTO;
import com.dto.creditNote.CreditNoteCalculateDTO;
import com.dto.payment.ClueDTO;
import com.dto.payment.PaymentInitResDTO;
import com.dto.payment.PaymentStatusReqDTO;
import com.dto.payment.PaymentStatusResDTO;
import com.dto.payment.QRConfig;
import com.dto.payment.QRNotifyPaymentReqDTO;
import com.dto.payment.QRNotifyPaymentResDTO;
import com.dto.payment.QRTransactionCheckReqDTO;
import com.dto.payment.QRTransactionCheckResDTO;
import com.dto.payment.TradingResultDTO;
import com.dto.payment.TransactionStatusReqDTO;
import com.dto.payment.TransactionStatusResDTO;
import com.dto.payment.TransactionStatusVnptResDTO;
import com.dto.quotation.ChangeStatusMetadataDTO;
import com.dto.services.ServicePricingInfo;
import com.dto.shoppingCart.register.SubscriptionRegisterInfo;
import com.dto.subscriptions.CustomerInformationResDTO;
import com.dto.subscriptions.SmartCAPopupDTO;
import com.dto.subscriptions.SubscriptionRegisterResDTO;
import com.dto.subscriptions.common.IServiceOwnerInfoDTO;
import com.dto.system.param.InfoSendMailDTO;
import com.dto.system.param.SubscriptionActiveMailParamDTO;
import com.dto.system.param.Wallet3rdPartyDTO;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.bills.BillItem;
import com.entity.bills.Bills;
import com.entity.combo.Combo;
import com.entity.combo.ComboPlan;
import com.entity.combo.SubscriptionComboAddon;
import com.entity.department.Department;
import com.entity.massoffer.BatchMasoffer;
import com.entity.orderServiceReceive.OrderServiceReceive;
import com.entity.payment.Clue;
import com.entity.payment.VNPTPayResponse;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.quotation.QuotationStatusEnum;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.BatchDHSXKD;
import com.entity.subscriptions.BatchKafka;
import com.entity.subscriptions.ChangeSubscription;
import com.entity.subscriptions.Subscription;
import com.entity.transaction_log.ActivityLog;
import com.entity.transaction_log.TransactionLog;
import com.enums.ActionChangeSubEnum;
import com.enums.ActionNotificationEnum;
import com.enums.VnptPayTransactionStatusEnum;
import com.exception.ErrorKey;
import com.exception.ErrorKey.VNPT_PAY;
import com.exception.Resources;
import com.mapper.VnptPayTransactionStatusMapper;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.emails.ParamEmailEnum;
import com.onedx.common.constants.enums.integration.PartnerCallAPI;
import com.onedx.common.constants.enums.integration.backend.AccessTradeStatusEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.integration.backend.MasOfferStatusEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.services.ServiceTypeEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeContinueEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionConfirmStatusEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.systemParams.SystemParamTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant.ContentType;
import com.onedx.common.dto.coupons.IGetPromotionDetailDTO;
import com.onedx.common.dto.integration.backend.IntegrationResponseDTO;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.exception.type.VNPTPayResponseException;
import com.onedx.common.repository.contact.ContactProvinceRepository;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.repository.subscriptions.SubscriptionHistoryRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.GsonMapperUtil;
import com.onedx.common.utils.HttpRestUtil;
import com.onedx.common.utils.ObjectUtil;
import com.repository.bills.BillCouponPrivateRepository;
import com.repository.bills.BillCouponTotalRepository;
import com.repository.bills.BillItemRepository;
import com.repository.bills.BillTaxRepository;
import com.repository.bills.BillsRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboRepository;
import com.repository.combo.SubscriptionComboAddonRepository;
import com.repository.coupons.CouponRepository;
import com.repository.credit_note.ChangeCreditNoteRepository;
import com.repository.credit_note.CreditNoteRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.massoffer.BatchMasofferRepository;
import com.repository.orderService.OrderServiceReceiveRepository;
import com.repository.payment.ClueRepository;
import com.repository.payment.VNPTPayResponseRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.BatchDHSXKDRepository;
import com.repository.subscriptions.BatchKafkaRepository;
import com.repository.subscriptions.ChangeSubscriptionRepository;
import com.repository.subscriptions.SubscriptionAddonsRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.transactionLog.TransactionLogRepository;
import com.repository.users.UserRepository;
import com.service.affiliate.AffiliateUserService;
import com.service.bills.BillsService;
import com.service.combo.ComboService;
import com.service.common.CommonService;
import com.service.contracts.EcontractService;
import com.service.couponSet.CouponItemService;
import com.service.creditNote.CreditNoteService;
import com.service.crm.automationRule.impl.AutomationRuleServiceImpl;
import com.service.email.EmailService;
import com.service.email.EmailTemplateService;
import com.service.integrate.IntegrationService;
import com.service.integrate.InventoryService;
import com.service.integrated.ExecutiveProducerService;
import com.service.notification.ActionNotificationService;
import com.service.notification.template.CP16;
import com.service.notification.template.KM16;
import com.service.notification.template.SCD05;
import com.service.openapis.v1.payments.PaymentV1Service;
import com.service.orderServiceReceive.OrderServiceReceiveService;
import com.service.payment.impl.PaymentService;
import com.service.pricing.PricingService;
import com.service.quotation.QuotationService;
import com.service.services.ServicesService;
import com.service.subscriptions.SubscriptionCalculateService;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.subscriptions.SubscriptionHelperService;
import com.service.subscriptions.SubscriptionHistoryService;
import com.service.subscriptions.SubscriptionNotificationService;
import com.service.subscriptions.SubscriptionOrderService;
import com.service.subscriptions.SubscriptionPaymentService;
import com.service.subscriptions.SubscriptionService;
import com.service.subscriptions.SubscriptionValidateService;
import com.service.system.param.SystemParamService;
import com.service.transactionLog.ActivityLogService;
import com.service.transactionLog.TransactionLogService;
import com.service.users.UserService;
import com.service.vnptPayResponseService.VnptPayResponseService;
import com.util.AuthUtil;
import com.util.NotifyUtil;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * * Payment Service Impl
 *
 * <AUTHOR>
 * @since 6/3/2021 5:16 PM
 */

@Service
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    public static final int OTHER = -1;
    public static final int ACTION_UPDATE = 1;
    public static final int ACTION_CHANGE_PLAN = 2;
    public static final int ACTION_RE_ACTIVE_OUT_OF_DATE = 3;
    public static final int ACTION_RE_ACTIVE_IN_PAYMENT = 4;
    public static final int ACTION_RENEW_SUBSCRIPTION = 5; // Gia hạn thuê bao
    public static final int SUBS_PRICING = 1;
    public static final int SUBS_COMBO = 2;
    public static final Integer SWAP = 1;
    final String[] vnptPayError = {"vnpt_pay_response"};
    final String[] subscriptionError = {"subscription"};
    public static final int ADMIN = 1;
    public static final int DEV = 2;
    public static final int SME = 3;
    public static final int UNSET = -1;

    @Value(value = "${web.host}")
    private String webHost;
    @Value("${transaction.secret.key}")
    private String secretKey;
    @Value("${transaction.merchant_service_id}")
    private String merchantServiceId;
    @Value("${transaction.token}")
    private String tokenVNPTPay;
    @Value("${transaction.url}")
    private String rootUrl;

    @Autowired
    private VNPTPayResponseRepository vnptPayResponseRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private ContactProvinceRepository contactProvinceRepository;
    @Autowired
    private ParamEmailRepository paramEmailRepository;
    @Autowired
    private SubscriptionService subscriptionService;
    @Autowired
    private SubscriptionValidateService subscriptionValidateService;
    @Autowired
    private SubscriptionDetailService subscriptionDetailService;
    @Autowired
    private HttpRestUtil httpUtil;
    @Autowired
    private SubscriptionPaymentService subscriptionPaymentService;
    @Autowired
    private SubscriptionCalculateService subscriptionCalculateService;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private ServicesService servicesService;
    @Autowired
    private TokenStore tokenStore;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AffiliateUserService affiliateUserService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ExecutiveProducerService executiveProducerService;
    @Autowired
    private OrderServiceReceiveRepository orderServiceReceiveRepository;
    @Autowired
    private OrderServiceReceiveService orderServiceReceiveService;
    @Autowired
    private ChangeSubscriptionRepository changeSubscriptionRepository;
    @Autowired
    private BillItemRepository billItemRepository;
    @Autowired
    private BillTaxRepository billTaxRepository;
    @Autowired
    private BillCouponTotalRepository billCouponTotalRepository;
    @Autowired
    private BillCouponPrivateRepository billCouponPrivateRepository;
    @Autowired
    private SubscriptionOrderService subscriptionOrderService;
    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;
    @Autowired
    private SubscriptionHelperService subscriptionHelperService;
    @Autowired
    private SubscriptionAddonsRepository subscriptionAddonsRepository;
    @Autowired
    private CreditNoteService creditNoteService;
    @Autowired
    private ActivityLogService activityLogService;
    @Autowired
    private QRConfig qrConfig;
    @Autowired
    private TransactionLogService transactionLogService;
    @Autowired
    private SubscriptionHistoryRepository subscriptionHistoryRepository;
    @Autowired
    private VnptPayTransactionStatusMapper vnptPayTransactionStatusMapper;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private CommonRestTemplate restTemplate;
    @Autowired
    private ClueRepository clueRepository;
    @Autowired
    private BillsService billsService;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private BatchKafkaRepository batchKafkaRepository;
    @Autowired
    private BatchDHSXKDRepository batchDhsxkdRepository;
    @Autowired
    private BatchMasofferRepository batchMasofferRepository;
    @Autowired
    private CouponItemService couponItemService;
    @Autowired
    private CreditNoteRepository creditNoteRepository;
    @Autowired
    private ChangeCreditNoteRepository changeCreditNoteRepository;
    @Autowired
    private SubscriptionHistoryService subscriptionHistoryService;
    @Autowired
    private SubscriptionComboAddonRepository subscriptionComboAddonRepository;
    @Autowired
    private QuotationService quotationService;
    @Autowired
    private ComboService comboService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private ActionNotificationService actionNotificationService;
    @Autowired
    private TransactionLogRepository transactionLogRepository;
    @Autowired
    private VnptPayResponseService vnptPayResponseService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private AutomationRuleServiceImpl automationRuleService;
    @Autowired
    private PaymentV1Service paymentV1Service;

    @Value("${transaction.token}")
    private String token;

    @Override
    @Transactional
    public QRNotifyPaymentResDTO pushNotificationReceiverQR(QRNotifyPaymentReqDTO reqDTO) {
        log.info("pushNotificationReceiverQR: start reqDTO = '{}'", GsonMapperUtil.toJson(reqDTO));
        /* **************************************************************************************************************************************
         * Kiểm tra dữ liệu đầu vào
         ***************************************************************************************************************************************/
        // Các lỗi khác
        String merchantOrderId = reqDTO.getBillNumber();
        if (!vnptPayResponseService.isPaymentProcessing(merchantOrderId)) {
            log.error("pushNotificationReceiverQRQR: isPaymentProcessing({}) = false", merchantOrderId);
            return getPaymentStatusQR(PaymentQRStatusEnum.ERROR_OTHER.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                    PaymentStatusEnum.ERROR_OTHER.getValue());
        }
        VNPTPayResponse responseStatus = vnptPayResponseRepository.findByMerchantOrderId(merchantOrderId).orElse(null);
        // Giao dịch không tồn tại
        if (Objects.isNull(responseStatus)) {
            log.error("pushNotificationReceiverQR: VNPTPayResponse.merchantOrderId({}) not found", merchantOrderId);
            return getPaymentStatusQR(PaymentQRStatusEnum.ERROR_RECEIVED.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                    PaymentQRStatusEnum.ERROR_RECEIVED.getValue());
        }
        // Giao dịch đã thanh toán(đã confirm)
        if (Objects.equals(responseStatus.getTransactionStatus(), VnptPayTransactionStatusEnum.SUCCESS.getValue())) {
            log.error("pushNotificationReceiverQR: transactionStatus is not PAID");
            return getPaymentStatusQR(PaymentQRStatusEnum.ERROR_RECEIVED.getMessageKey(),
                    ErrorKey.MERCHANT_ORDER_ID, PaymentQRStatusEnum.ERROR_RECEIVED.getValue());
        }
        // Chữ ký không đúng
        if (validateChecksum(reqDTO, responseStatus)) {
            log.error("pushNotificationReceiverQR: checksum is not valid");
            return getPaymentStatusQR(PaymentQRStatusEnum.ERROR_CHECKSUM.getMessageKey(),
                    ErrorKey.CHECKSUM, PaymentQRStatusEnum.ERROR_CHECKSUM.getValue());
        }
        // Hệ thống merchant bảo trì hoặc timeout
//        if (false) {
//            log.error("pushNotificationReceiverQR: system timeout");
//            return getPaymentStatusQR(PaymentQRStatusEnum.ERROR_TIME_OUT.getMessageKey(),
//                   "", PaymentQRStatusEnum.ERROR_TIME_OUT.getValue());
//        }
        /* **************************************************************************************************************************************
         * cập nhật dữ liệu
         ***************************************************************************************************************************************/
        // Trong trường hợp thanh toán cho giỏ hàng
        if (responseStatus.getSubscriptionId() == null && responseStatus.getLstSubId() != null) {
            log.info("pushNotificationReceiver: handlePaymentShoppingCart start");
            return handlePaymentShoppingCartQR(reqDTO, responseStatus);
        }
        // Trong trường hợp thanh toán cho thuê bao đơn lẻ
        handleBeforeNotificationReceiver(responseStatus, reqDTO.getResponseCode(), PaymentMethodEnum.VNPTPAY_QR, reqDTO, reqDTO.getDescription());
        // save VNPTPayResponse
        responseStatus.setResponseCode(reqDTO.getResponseCode());
        responseStatus.setMerchantServiceId(reqDTO.getMerchantClientId());
        responseStatus.setDescription(reqDTO.getDescription());
        responseStatus.setAmount(new BigDecimal(reqDTO.getAmount()));
        responseStatus.setVnptpayTransactionId(reqDTO.getQrTxnId());
        responseStatus.setPaymentMethod(PaymentMethodEnum.VNPTPAY_QR.name());
        responseStatus.setAdditionalInfo(reqDTO.getAdditionalInfo());
        responseStatus.setOrderStatus(OrderStatusEnum.PROCESSED.value);
        responseStatus.setSecureCode(reqDTO.getChecksum());
        responseStatus.setPayDate(LocalDateTime.parse(reqDTO.getPayDate(), DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS)));
        responseStatus.setBuyNow(0);
        CustomUserDetails userDetails = AuthUtil.getCurrentUser();
        if (userDetails != null && userDetails.getBearerToken() != null) {
            responseStatus.setAccessToken(userDetails.getBearerToken());
        }
        VNPTPayResponse saved = vnptPayResponseRepository.save(responseStatus);
        log.info("pushNotificationReceiverQR: Saved VNPTPayResponse {}", GsonMapperUtil.toJson(saved));

        return getPaymentStatusQR(PaymentStatusEnum.SUCCESS.getMessageKey(), "", PaymentStatusEnum.SUCCESS.getValue());
    }

    @Override
    @Transactional
    public PaymentStatusResDTO pushNotificationReceiver(PaymentStatusReqDTO reqDTO) {
        log.info("pushNotificationReceiver: start reqDTO = '{}'", GsonMapperUtil.toJson(reqDTO));
        /* **************************************************************************************************************************************
         * Kiểm tra dữ liệu đầu vào
         ***************************************************************************************************************************************/
        // callback partner v1
        paymentV1Service.callBackPayment(reqDTO);

        if (!vnptPayResponseService.isPaymentProcessing(reqDTO.getMerchantOrderId())) {
            log.error("pushNotificationReceiver: isPaymentProcessing({}) = false", reqDTO.getMerchantOrderId());
            return getPaymentStatus(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                PaymentStatusEnum.ERROR_OTHER.getValue(), reqDTO.getMerchantServiceId(), reqDTO.getMerchantOrderId());
        }
        // Kiểm tra giao dịch tồn tại
        Optional<VNPTPayResponse> responseOptional = vnptPayResponseRepository.findByMerchantOrderId(reqDTO.getMerchantOrderId());
        if (!responseOptional.isPresent()) {
            log.error("pushNotificationReceiver: VNPTPayResponse.merchantOrderId({}) not found", reqDTO.getMerchantOrderId());
            return getPaymentStatus(PaymentStatusEnum.ERROR_NOT_FOUND.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                PaymentStatusEnum.ERROR_NOT_FOUND.getValue(), reqDTO.getMerchantServiceId(), reqDTO.getMerchantOrderId());
        }
        VNPTPayResponse responseStatus = responseOptional.get();
        // Kiểm tra mã bảo mật
        if (validateSecureCode(reqDTO, responseStatus.getSubscriptionId())) {
            log.error("pushNotificationReceiver: secured code is not valid");
            throw new VNPTPayResponseException(getPaymentStatus(PaymentStatusEnum.ERROR_SECURE_CODE.getMessageKey(),
                ErrorKey.SECURE_CODE, PaymentStatusEnum.ERROR_SECURE_CODE.getValue(), reqDTO.getMerchantServiceId(),
                reqDTO.getMerchantOrderId()));
        }
        // Kiểm tra số tiền
        if (Objects.isNull(responseStatus.getAmount()) || reqDTO.getAmount().compareTo(responseStatus.getAmount()) != 0) {
            log.error("pushNotificationReceiver: Invalid amount value");
            throw new VNPTPayResponseException(getPaymentStatus(PaymentStatusEnum.ERROR_AMOUNT.getMessageKey(),
                ErrorKey.SECURE_CODE, PaymentStatusEnum.ERROR_AMOUNT.getValue(), reqDTO.getMerchantServiceId(),
                reqDTO.getMerchantOrderId()));
        }
        // Kiểm tra trạng thái transaction đã nhận trước đó
        if (Objects.equals(responseStatus.getTransactionStatus(), VnptPayTransactionStatusEnum.SUCCESS.getValue())) {
            log.error("pushNotificationReceiver: transactionStatus is not PAID");
            throw new VNPTPayResponseException(getPaymentStatus(PaymentStatusEnum.ERROR_RECEIVED.getMessageKey(),
                ErrorKey.SECURE_CODE, PaymentStatusEnum.ERROR_RECEIVED.getValue(), reqDTO.getMerchantServiceId(),
                reqDTO.getMerchantOrderId()));
        }
        /* **************************************************************************************************************************************
         * Cập nhật dữ liệu
         ***************************************************************************************************************************************/
        // Trong trường hợp thanh toán cho giỏ hàng
        if (responseStatus.getSubscriptionId() == null && responseStatus.getLstSubId() != null) {
            log.info("pushNotificationReceiver: handlePaymentShoppingCart start");
            return handlePaymentShoppingCart(reqDTO, responseStatus);
        }
        // Trong trường hợp thanh toán cho thuê bao đơn lẻ
        PaymentStatusResDTO paymentRes = getPaymentStatus(PaymentStatusEnum.SUCCESS.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                PaymentStatusEnum.SUCCESS.getValue(), responseStatus.getMerchantServiceId(), responseStatus.getMerchantOrderId());
        handleBeforeNotificationReceiver(responseStatus, reqDTO.getResponseCode(), PaymentMethodEnum.VNPTPAY, reqDTO, paymentRes.getDescription());
        // save VNPTPayResponse
        BeanUtils.copyProperties(reqDTO, responseStatus);
        responseStatus.setPayDate(LocalDateTime.parse(reqDTO.getPayDate(), DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS)));
        responseStatus.setPayAction(reqDTO.getPayAction());
        responseStatus.setResponseCode(reqDTO.getResponseCode());
        responseStatus.setMerchantServiceId(reqDTO.getMerchantServiceId());
        responseStatus.setVnptpayTransactionId(reqDTO.getVnptpayTransactionId());
        responseStatus.setPaymentMethod(reqDTO.getPaymentMethod());
        responseStatus.setAdditionalInfo(reqDTO.getAdditionalInfo());
        CustomUserDetails userDetails = AuthUtil.getCurrentUser();
        if (userDetails != null && userDetails.getBearerToken() != null) {
            responseStatus.setAccessToken(userDetails.getBearerToken());
        }
        VNPTPayResponse saved = vnptPayResponseRepository.save(responseStatus);
        log.info("pushNotificationReceiver: Saved VNPTPayResponse {}", GsonMapperUtil.toJson(saved));

        return paymentRes;
    }

    private void handleBeforeNotificationReceiver(
            VNPTPayResponse responseStatus, String responseCode, PaymentMethodEnum paymentMethod, Object paymentReq,
            String description) {
        Bills billsVnptRes = billsRepository
                .findById(responseStatus.getBillingId())
                .orElseThrow(() -> new VNPTPayResponseException(paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR) ?
                        getPaymentStatusQR(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), "", PaymentStatusEnum.ERROR_OTHER.getValue()) :
                        getPaymentStatus(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), ErrorKey.SECURE_CODE,
                                PaymentStatusEnum.ERROR_OTHER.getValue(), responseStatus.getMerchantServiceId(), responseStatus.getMerchantOrderId())));
        Integer actionType = billsVnptRes.getActionType() != null ? billsVnptRes.getActionType() : OTHER;
        boolean isUpdateSubscription = Objects.equals(actionType, ACTION_UPDATE);
        boolean isChangePlanSubscription = Objects.equals(actionType, ACTION_CHANGE_PLAN);
        boolean isReActiveSub = Objects.equals(actionType, ACTION_RE_ACTIVE_OUT_OF_DATE) || Objects.equals(actionType, ACTION_RE_ACTIVE_IN_PAYMENT);
        // Thông tin thuê bao
        Subscription subscription = subscriptionRepository
                .findById(responseStatus.getSubscriptionId())
                .orElseThrow(() -> new VNPTPayResponseException(paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR) ?
                        getPaymentStatusQR(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), "", PaymentStatusEnum.ERROR_OTHER.getValue()) :
                        getPaymentStatus(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), ErrorKey.SECURE_CODE,
                                PaymentStatusEnum.ERROR_OTHER.getValue(), responseStatus.getMerchantServiceId(), responseStatus.getMerchantOrderId())));
        // Thông tin portal thanh toán
        int portalType = responseStatus.getPortalType() != null ? responseStatus.getPortalType() : PortalType.SME.getType();
        // Thông tin về gói được thanh toán
        Pricing pricing = null;
        ComboPlan comboPlan = null;
        Integer pricingType = PricingTypeEnum.PREPAY.value;
        Integer cycleTypePricing = null;
        List<SubscriptionComboAddon> subscriptionComboAddonsOld = new ArrayList<>();
        if (Objects.nonNull(subscription.getPricingId())) {
            pricing = subscriptionService.checkExistPricing(billsVnptRes.getPricingId());
            pricingType = pricing.getPricingType();
            cycleTypePricing = pricing.getCycleType();
        } else if (Objects.nonNull(subscription.getComboPlanId())) {
            comboPlan = subscriptionService.validateComboPlan(billsVnptRes.getComboPlanId());
            pricingType = comboPlan.getComboPlanType();
            subscriptionComboAddonsOld = subscriptionComboAddonRepository.findBySubscriptionId(subscription.getId());
        }
        Long paymentCyclePmp = null;
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            paymentCyclePmp = pricingMultiPlanRepository.getPaymentCycle(subscription.getPricingMultiPlanId());
        }
        Integer paymentCycle = null;
        if (paymentCyclePmp != null && pricing != null) {
            paymentCycle = Math.toIntExact(paymentCyclePmp);
        }
        // Thông tin về dịch vụ
        boolean checkServiceON = Boolean.TRUE;
        ServiceEntity serviceEntity = subscription.getServiceId() != null ? serviceRepository.findById(subscription.getServiceId()).orElse(null) : null;
        if (Objects.nonNull(serviceEntity) && (serviceEntity.getOnOsTypeEnum() == OnOsTypeEnum.OS)) {
            checkServiceON = Boolean.FALSE;
        }
        // Token của VNPTPay trên cache
        String token = commonService.getVNPTPayToken(KeyGenType.TOKEN_PAYMENT);
        // Thông tin transaction log
        TransactionLog transactionLogFinal = new TransactionLog();
        Optional<TransactionLog> transactionLog = transactionLogRepository.findByVnptPayResponseId(responseStatus.getId());
        if (!transactionLog.isPresent()) {
            // TH repay tạo ra vnpt_pay_response mới thì update lại transaction log với vnpt_pay_response_id mới này
            Optional<TransactionLog> transactionLogCurrent = transactionLogRepository.findFirstBySubscriptionIdOrderByIdDesc(subscription.getId());
            if (transactionLogCurrent.isPresent()) {
                transactionLogCurrent.get().setVnptPayResponseId(responseStatus.getId());
                transactionLogRepository.save(transactionLogCurrent.get());
                transactionLogFinal = transactionLogCurrent.get();
            } else {
                log.error("pushNotificationReceiver: transactionLog for subscriptionId {} is not found", subscription.getId());
                throw new VNPTPayResponseException(paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR) ?
                        getPaymentStatusQR(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), "", PaymentStatusEnum.ERROR_OTHER.getValue()) :
                        getPaymentStatus(PaymentStatusEnum.ERROR_OTHER.getMessageKey(), ErrorKey.SECURE_CODE,
                                PaymentStatusEnum.ERROR_OTHER.getValue(), responseStatus.getMerchantServiceId(), responseStatus.getMerchantOrderId()));
            }
        } else {
            transactionLogFinal = transactionLog.get();
        }
        CommonActivityLogInfoDTO activityLogInfoDTO = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, transactionLogFinal);

        /* **************************************************************************************************************************************
         * Thanh toán thành công
         ***************************************************************************************************************************************/
        Subscription oldSubscription = new Subscription();
        Subscription newSubscription;
        boolean changePlanEndOfCylcePayNow = false;
        BillStatusEnum billStatusEnum = BillStatusEnum.FAILURE;
        if (StringUtils.equals(responseCode, PaymentStatusEnum.SUCCESS.getValue())) {
            log.info("pushNotificationReceiver: responseCode SUCCESS, isReactiveSub {}, subscriptionStatus {}",
                    isReActiveSub, subscription.getStatus());
            if (isReActiveSub) {
                Subscription subscriptionReactive = reactiveSub(subscription, cycleTypePricing, responseStatus, token, pricing, portalType,
                        actionType, paymentCycle);
                subscriptionRepository.save(subscriptionReactive);
                // lưu lịch sử thay đổi thuê bao
                String hisContent = SubscriptionHistoryConstant.SUB_REACTIVE;
                subscriptionHistoryService.addSubscriptionHistoryNoAuth(subscription.getId(), hisContent, ContentType.SUB_REACTIVE, token);
            } else {
                subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
                // Đồng bộ trạng thái confirm_status giữa thuê bao chính và thuê bao được tặng
                subscriptionService.confirmGiveAwaySubscription(subscription.getId());

                List<Bills> billsList = billsRepository.findBySubscriptionsId(subscription.getId());

                // nếu sub trạng thái ko gia hạn thì k thay đổi trạng thái
                if (!Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.NOT_EXTEND.value)) {
                    if (!checkBillOutOfDate(billsList) && subscription.getStartedAt().compareTo(new Date()) <= 0) {
                        if (!isUpdateSubscription && !isChangePlanSubscription && executiveProducerService.checkCallApiOrderServiceDHSXKD(subscription)) {
                            // Combo OS tạo mới TT qua pay giữ nguyên Đang chờ
                            subscription.setStatus(SubscriptionStatusEnum.FUTURE.value);
                        } else {
                            subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                        }
                    }
                }

                subscription = subscriptionService.autoActivateSub(subscription, billsList);
                subscription.setPaymentMethod(paymentMethod.value);
                Subscription saveInfo = subscriptionRepository.save(subscription);
                log.info("pushNotificationReceiver: save subscription '{}'", saveInfo.toLogString());
                // Lưu lịch sử thay đổi trạng thái thuê bao
                subscriptionHistoryService.saveStatusHistory(saveInfo);
                billStatusEnum = BillStatusEnum.PAID;
                //xoa token khoi redis
                commonService.deleteTokenVNPT(KeyGenType.TOKEN_PAYMENT.getKey(), token);
                String finalToken = StringUtil.replaceToken(token);

                BeanUtils.copyProperties(subscription, oldSubscription);
                //Nếu thanh toán trả trước
                if (Objects.equals(PricingTypeEnum.PREPAY.value, pricingType)) {
                    // Neu action type la Update
                    if (isUpdateSubscription) {
                        log.info("pushNotificationReceiver: ACTION_UPDATE");
                        if (Objects.nonNull(saveInfo.getPricingId())) {
                            // Xóa thông tin coupon/addon cũ
                            subscriptionAddonsRepository.deleteAllBySubscriptionId(subscription.getId());
                            subscriptionService.updatePricingByBatch(saveInfo.getId(), ActionChangeSubEnum.NOW.value,
                                    activityLogInfoDTO);
                        } else if (Objects.nonNull(saveInfo.getComboPlanId())) {
                            newSubscription = subscriptionService.updateComboByBatch(saveInfo, ActionChangeSubEnum.NOW.value,
                                    activityLogInfoDTO);
                            // Lưu lịch sử addon
                            List<SubscriptionComboAddon> subscriptionComboAddonsNew = subscriptionComboAddonRepository
                                    .findBySubscriptionId(newSubscription.getId());
                            comboService.saveSubComboHistoryChangeAddon(newSubscription.getId(), subscriptionComboAddonsOld,
                                    subscriptionComboAddonsNew);
                        }
                        // Lưu thông tin dùng để gọi Kafka sang backend SPDV bằng batch
                        BatchKafka batchKafka = new BatchKafka(null, saveInfo.getId(), 1, finalToken,
                                DeletedFlag.NOT_YET_DELETED.getValue(), transactionLogFinal.getId());
                        batchKafkaRepository.save(batchKafka);
                        // Lưu thông tin gọi sang DHSXKD bằng batch
                        BatchDHSXKD batchDHSXKD = new BatchDHSXKD(null, saveInfo.getId(), responseStatus.getBillingId(),
                                responseStatus.getMerchantOrderId(), ActionTypeEnum.UPDATE_SUBSCRIPTION.getValue(), DeletedFlag.NOT_YET_DELETED.getValue());
                        batchDhsxkdRepository.save(batchDHSXKD);
                    } else if (isChangePlanSubscription) {
                        log.info("pushNotificationReceiver: ACTION_CHANGE_PLAN");
                        if (Objects.nonNull(saveInfo.getPricingId())) {
                            subscriptionService.swapPricingByBatch(saveInfo, ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                            Pricing pricingOld = pricingRepository.findByIdAndDeletedFlag(oldSubscription.getPricingId(),
                                    DeletedFlag.NOT_YET_DELETED.getValue())
                                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID,
                                    String.valueOf(oldSubscription.getPricingId())));
                            // Lưu lịch sử đổi gói
                            String histContent = String.format(SubscriptionHistoryConstant.CHANGE_PRICING, pricingOld.getPricingName(),
                                    pricing.getPricingName());
                            addSubscriptionHistoryBatch(subscription.getId(), histContent, billsVnptRes.getId(),
                                    SubscriptionHistoryConstant.ContentType.CHANGE_PRICING, responseStatus);
                            // Case đổi gói cuối chu kỳ, thanh toán ngay
                            if (Objects.nonNull(pricingOld.getChangePricingPaymentTime()) &&
                                    pricingOld.getChangePricingPaymentTime().equals(ChangePricingPaymentTimeEnum.NOW.getValue()) &&
                                    Objects.nonNull(pricingOld.getChangePricingDate()) &&
                                    pricingOld.getChangePricingDate().equals(ChangePricingPaymentTimeEnum.END_OF_PERIOD.getValue())) {
                                // Hóa đơn đổi gói cũ chuyển thành hủy
                                changePlanEndOfCylcePayNow = true;
                                Bills bill = billsRepository.getSubscriptionsIdAndCurrentCycle(subscription.getId(), subscription.getCurrentCycle());
                                if (Objects.nonNull(bill)) {
                                    List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository.getAllChangeCreditNoteByBillingId(bill.getId());
                                    if (!CollectionUtils.isEmpty(listCreditNote)) {
                                        bill.setStatus(BillStatusEnum.DELETED.value);
                                    }
                                    billsRepository.save(bill);
                                    creditNoteRepository.deleteCreditNoteByBillingId(bill.getId());
                                }
                            }
                        } else if (Objects.nonNull(saveInfo.getComboPlanId())) {
                            subscriptionService.swapComboPlanByBatch(saveInfo, ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                            ComboPlan comboPlanOld = comboPlanRepository.findByIdAndDeletedFlag(oldSubscription.getComboPlanId(),
                                            DeletedFlag.NOT_YET_DELETED.getValue())
                                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.COMBO_PLAN, ErrorKey.ID,
                                            String.valueOf(oldSubscription.getComboPlanId())));
                            // Lưu lịch sử đổi gói
                            String histContent = String.format(SubscriptionHistoryConstant.CHANGE_PRICING, comboPlanOld.getComboName(),
                                    (comboPlan == null ? "null" : comboPlan.getComboName()));
                            subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent, billsVnptRes.getId(),
                                    SubscriptionHistoryConstant.ContentType.CHANGE_PRICING);
                        }
                        // Lưu thông tin send kafka
                        // Khởi tạo bằng giá trị xóa, khi nào batchDHSXKD xử lý xong dữ liệu mới sẽ update lại để chạy kafka sau
                        // ko gọi kafka nếu là OS
                        if (checkServiceON) {
                            log.info("======== action type = ACTION_CHANGE_PLAN : calling batchKafkaRepository.save() for ON sub ===========");
                            batchKafkaRepository.save(
                                    new BatchKafka(null, saveInfo.getId(), 2, finalToken,
                                            !BooleanUtils.isTrue(activityLogInfoDTO.getIsCalledDHSX()) ?
                                                    DeletedFlag.DELETED.getValue() : DeletedFlag.NOT_YET_DELETED.getValue(),
                                            transactionLogFinal.getId())
                            );
                        }
                        if (!BooleanUtils.isTrue(activityLogInfoDTO.getIsCalledDHSX())) {
                            //Lưu thông tin batch_dhsxkd
                            batchDhsxkdRepository.save(
                                    new BatchDHSXKD(null, saveInfo.getId(), responseStatus.getBillingId(), responseStatus.getMerchantOrderId(),
                                            ActionTypeEnum.CHANGE_PLAN.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));
                        }
                    } else if (!Objects.equals(transactionLogFinal.getTransactionCode(), TransactionCodeEnum.RENEW.value)) {
                        log.info("pushNotificationReceiver: NEW");
                        // Tạo mới thuê bao
                        // Trong trường hợp tạo mới thuê bao kích hoạt hợp đồng bằng SmartCA, không gửi Kafka ngay khi thanh toán thành công
                        boolean enableSmartCA = subscriptionDetailService.checkEnableSmartCA(saveInfo.getId());
                        if (!enableSmartCA) {
                            //Lưu thông tin send kafka
                            batchKafkaRepository.save(
                                    new BatchKafka(null, saveInfo.getId(), 0, finalToken, DeletedFlag.NOT_YET_DELETED.getValue(),
                                            transactionLogFinal.getId())
                            );
                        }
                        //Lưu thông tin batch_dhsxkd
                        batchDhsxkdRepository.save(
                                new BatchDHSXKD(null, saveInfo.getId(), responseStatus.getBillingId(), responseStatus.getMerchantOrderId(),
                                        ActionTypeEnum.CREATE_SUBSCRIPTION.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));

                        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
                            saveMasofferWaitingSent(saveInfo.getId(), MasOfferStatusEnum.PENDING);
                        }
                        // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
                        inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
                        // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
                        if (subscription.getQuotationId() != null) {
                            quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER,
                                PortalType.UNSET, new ChangeStatusMetadataDTO(subscription.getCode(), subscription.getCreatedBy()));
                        }
                        subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);
                        subscriptionRepository.save(subscription);
                    } else if (Objects.equals(transactionLogFinal.getTransactionCode(), TransactionCodeEnum.RENEW.value)) {
                        log.info("pushNotificationReceiver: RENEW");
                        // Thanh toán bills gia hạn
                        if (Objects.nonNull(saveInfo.getPricingId())) {
                            // chuyen thong tin bang change_sub sang subscription
                            Subscription finalSubscription1 = renewSubscriptionByBatch(saveInfo.getPricing(), saveInfo.getId(),
                                    saveInfo,
                                    ActionChangeSubEnum.NOW.value, activityLogInfoDTO, billsVnptRes);
                            if (checkServiceON) {
                                integrationService.transactionOneSME(token, finalSubscription1, saveInfo.getPricing(),
                                        IntegrationActionTypeEnum.RENEW_SUBSCRIPTION, null, null, null, false,
                                        activityLogInfoDTO);
                            }
                        }
                        Long userIdCreateBill = getUserIdCreateBill(billsVnptRes.getCreatedBy());
                        subscriptionHistoryService.addSubscriptionHistory(saveInfo.getId(), SubscriptionHistoryConstant.RENEWAL_SUBSCRIPTION,
                                SubscriptionHistoryConstant.ContentType.RENEWAL_SUBSCRIPTION, userIdCreateBill);

                        if (checkServiceON) {
                            batchKafkaRepository.save(
                                    new BatchKafka(null, saveInfo.getId(), ActionTypeEnum.RENEW_SUBSCRIPTION.getValue(), finalToken, DeletedFlag.NOT_YET_DELETED.getValue(),
                                            transactionLogFinal.getId())
                            );
                        }

                        batchDhsxkdRepository.save(
                                new BatchDHSXKD(null, saveInfo.getId(), responseStatus.getBillingId(), responseStatus.getMerchantOrderId(),
                                        ActionTypeEnum.RENEW_SUBSCRIPTION.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));

                        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
                            saveMasofferWaitingSent(saveInfo.getId(), MasOfferStatusEnum.PENDING);
                        }

                    }
                }
            }
        }

        log.info("pushNotificationReceiver: billStatusEnum {}", billStatusEnum);

        // Thực hiện lưu thông tin hóa đơn nếu thanh toán thành công
        if (Objects.nonNull(responseStatus.getBillingId()) && !isReActiveSub) {
            Optional<Bills> billsOptional = billsRepository.findById(responseStatus.getBillingId());
            if (billsOptional.isPresent()) {
                Bills bills = billsOptional.get();
                bills.setStatus(billStatusEnum.value);
                if (StringUtils.equals(responseCode, PaymentStatusEnum.SUCCESS.getValue())) {
                    bills.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
                }
                //Ngày thanh toán sẽ được set khi thanh toán thành công
                if (billStatusEnum == BillStatusEnum.PAID) {
                    bills.setPaymentDate(new Date());
                    //update trạng thái credit note thành đã hoàn trả khi thanh toán thành công
                    List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository.getAllChangeCreditNoteByBillingId(bills.getId());
                    Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteList = listCreditNote.stream()
                            .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue));
                    creditNoteService.createCreditNote(subscription.getId(), bills, creditNoteList, CreditNoteDetailStatusEnum.ADJUSTED, null,
                            isChangePlanSubscription);
                    if (changePlanEndOfCylcePayNow) {
                        List<CreditNoteCalculateDTO> usedCreditNote = createUsedCreditNote(listCreditNote);
                        creditNoteService.createNewCreditNote(billsVnptRes, usedCreditNote, subscription.getCreatedBy(), true);
                    }
                    changeCreditNoteRepository.updateChangeCreditNoteStatusByBillingId(bills.getId(), YesNoEnum.YES.value);
                    if (Objects.nonNull(bills.getAffiliateLinkCode()) && actionType == -1) {
                        affiliateUserService.commissionCalculate(bills, null, subscription.getPricingId(), subscription.getComboPlanId());
                    }
                }
                boolean sendBillMail = false;
                if (bills.getSendMail() == null) {
                    bills.setSendMail(1);
                    sendBillMail = true;
                }
                billsRepository.save(bills);
                if (Objects.equals(bills.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value) &&
                        Objects.equals(bills.getStatus(), BillStatusEnum.PAID.value)) {
                    creditNoteRepository.updateCreditNoteByBillingApply(bills.getId(), CreditNoteDetailStatusEnum.REFUNDED.code);
                }
                // gui mail bill
                if (sendBillMail) {
                    billsService.sendBillMail(bills.getId());
                }
                couponItemService.used(subscription);

                log.info("pushNotificationReceiver: update bill '{}'", bills.toLogString());
            }
        }
        //TH thanh toán trả trước thành công, đăng ký mới
        try {
            log.info("pushNotificationReceiver: CHECK condition for calling sendNotifySubForSmeDevOrAdmin for subId = {}, responseCode = {}, isReActiveSub = {}, pricingType = {}, transactionLogFinal.getTransactionCode() = {}",
                subscription.getId(), responseCode, isReActiveSub, pricingType, transactionLogFinal.getTransactionCode());
            if (StringUtils.equals(responseCode, PaymentStatusEnum.SUCCESS.getValue())
                && ((!isReActiveSub && Objects.equals(PricingTypeEnum.PREPAY.value, pricingType) &&
                    !Objects.equals(transactionLogFinal.getTransactionCode(), TransactionCodeEnum.RENEW.value)) ||
                    paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR))
            ) {
                // Gui mail khi đky sub
                subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, pricing, comboPlan, MailParams.SME);
            }
        } catch (Exception e) {
            log.info("Exception before calling sendNotifySubForSmeDevOrAdmin {}", e.getMessage());
        }

        log.info("pushNotificationReceiver: end subscriptions {}", subscription.toLogString());

        ActivityLog activityLog = activityLogService.commonActivityLogData(ActivityCodeEnum.PAY_NOTIFY, activityLogInfoDTO);
        Object paymentRes = paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR) ?
                getPaymentStatusQR(PaymentStatusEnum.SUCCESS.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                        PaymentStatusEnum.SUCCESS.getValue()) :
                getPaymentStatus(PaymentStatusEnum.SUCCESS.getMessageKey(), ErrorKey.MERCHANT_ORDER_ID,
                        PaymentStatusEnum.SUCCESS.getValue(), responseStatus.getMerchantServiceId(), responseStatus.getMerchantOrderId());

        // Lưu thông tin transaction
        if (Objects.nonNull(activityLogInfoDTO.getActivityLog())) {
            activityLogInfoDTO.getActivityLog().setResponseAt(LocalDateTime.now());
        }
        activityLog.setResponseAt(LocalDateTime.now());
        activityLog.updateResponseReq(
                paymentReq, paymentRes, tokenVNPTPay,
                Objects.equals(PaymentStatusEnum.SUCCESS.getValue(), responseCode) ? TransactionLogStatusEnum.SUCCESS : TransactionLogStatusEnum.FAIL,
                responseCode,
                description,
                activityLogInfoDTO);

        activityLogService.saveActivityLogInTransaction(activityLogInfoDTO);

        // Update status transaction
        transactionLogService.updateTransactionLogCompleted(transactionLogFinal, activityLogInfoDTO);
    }

    private void addSubscriptionHistoryBatch(Long id, String histContent, Long id1, int changePricing, VNPTPayResponse vnptPayResponse) {
        subscriptionHistoryRepository.save(SubscriptionHistory.builder()
                .subscriptionId(id)
                .createdAt(LocalDateTime.now())
                .content(histContent)
                .contentType(changePricing)
                .billingId(id1)
                .createdBy(getCurrentUserId(vnptPayResponse.getAccessToken()))
                .build());
    }

    /**
     * Lấy id người tạo bill
     */
    private Long getUserIdCreateBill(String userId) {
        Long creatorId = null;
        try {
            creatorId = Long.valueOf(userId);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log.info("UserID create bill is not number {}", e.getMessage(), e);
        }
        return creatorId;
    }

    private PaymentStatusResDTO handlePaymentShoppingCart(PaymentStatusReqDTO reqDTO, VNPTPayResponse responseStatus) {
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList = new ArrayList<>();
        BigDecimal totalAmount = responseStatus.getAmount();
        VNPTPayResponse saved = getVnptPayResponse(reqDTO, responseStatus, subscriptionRegisterInfoList);
        // send email dang ky thanh cong
        sendEmailAndNotify(saved, subscriptionRegisterInfoList, totalAmount);
        return getPaymentStatus(
            PaymentStatusEnum.SUCCESS.getMessageKey(),
            ErrorKey.MERCHANT_ORDER_ID,
            PaymentStatusEnum.SUCCESS.getValue(),
            saved.getMerchantServiceId(),
            saved.getMerchantOrderId()
        );
    }

    private QRNotifyPaymentResDTO handlePaymentShoppingCartQR(QRNotifyPaymentReqDTO reqDTO, VNPTPayResponse responseStatus) {
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList = new ArrayList<>();
        BigDecimal totalAmount = responseStatus.getAmount();
        VNPTPayResponse saved = getVnptPayResponseQR(reqDTO, responseStatus, subscriptionRegisterInfoList);
        // send email dang ky thanh cong
        sendEmailAndNotify(saved, subscriptionRegisterInfoList, totalAmount);
        return getPaymentStatusQR(
                PaymentStatusEnum.SUCCESS.getMessageKey(),
                ErrorKey.MERCHANT_ORDER_ID,
                PaymentStatusEnum.SUCCESS.getValue()
        );
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public VNPTPayResponse getVnptPayResponse(PaymentStatusReqDTO reqDTO, VNPTPayResponse responseStatus,
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList) {
        responseStatus.setOrderStatus(OrderStatusEnum.PROCESSED.value);
        BeanUtils.copyProperties(reqDTO, responseStatus);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS);
        LocalDateTime dateTime = LocalDateTime.parse(reqDTO.getPayDate(), formatter);
        responseStatus.setPayDate(dateTime);
        responseStatus.setPayAction(reqDTO.getPayAction());
        responseStatus.setResponseCode(reqDTO.getResponseCode());
        responseStatus.setMerchantServiceId(reqDTO.getMerchantServiceId());
        responseStatus.setVnptpayTransactionId(reqDTO.getVnptpayTransactionId());
        responseStatus.setPaymentMethod(reqDTO.getPaymentMethod());
        responseStatus.setAdditionalInfo(reqDTO.getAdditionalInfo());
        CustomUserDetails userDetails = AuthUtil.getCurrentUser();
        if (userDetails != null && userDetails.getBearerToken() != null) {
            responseStatus.setAccessToken(userDetails.getBearerToken());
        }
        //
        String token = commonService.getVNPTPayToken(KeyGenType.TOKEN_PAYMENT);
        commonService.deleteTokenVNPT(KeyGenType.TOKEN_PAYMENT.getKey(), token);
        String finalToken = StringUtil.replaceToken(token);
        //
        List<String> billIdStrLst = responseStatus.getLstBillId();
        String cartCode = null;
        log.info("===== handlePaymentShoppingCart  responseStatus.getLstBillId().size() {}", billIdStrLst.size());
        log.info("===== handlePaymentShoppingCart  responseStatus.getLstBillId data {}", Arrays.toString(billIdStrLst.toArray()));
        boolean isDevice = false;
        for (String billingIdStr : billIdStrLst) {
            SubscriptionRegisterInfo subscriptionRegisterInfo =
                saveBatchKafkaAndBatchDHSXKD(Long.valueOf(StringUtils.trim(billingIdStr)), finalToken, responseStatus,
                    PaymentMethodEnum.VNPTPAY);

            if (Objects.nonNull(subscriptionRegisterInfo)) {
                if (subscriptionRegisterInfo.isDevice()) {
                    isDevice = true;
                }
                cartCode = Objects.nonNull(subscriptionRegisterInfo.getMainSubscription()) &&
                    Objects.nonNull(subscriptionRegisterInfo.getMainSubscription().getCartCode()) ?
                    subscriptionRegisterInfo.getMainSubscription().getCartCode() : null;
                subscriptionRegisterInfoList.add(subscriptionRegisterInfo);
            }
        }

        if (StringUtils.equals(reqDTO.getResponseCode(), PaymentStatusEnum.SUCCESS.getValue()) && Objects.nonNull(cartCode)) {
            creditNoteService.updateOldCreditNotesWithCartCode(cartCode);

            if (isDevice) {
                // lịch sử đơn order
                String histContentCart = SubscriptionHistoryConstant.ORDER_SUCCESS;
                Integer histTypeCart = ContentType.ORDER_DEVICE;
                subscriptionHistoryService.addSubscriptionHistory(cartCode, histContentCart, histTypeCart);

                // lưu lịch sử thanh toán thành công
                String content = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
                Integer type = ContentType.PAYMENT_SUCCESS;
                subscriptionHistoryService.addSubscriptionHistory(cartCode, content, type);

                // log đơn hàng đang đóng gói
                subscriptionHistoryService.addSubscriptionHistory(cartCode, SubscriptionHistoryConstant.PACKING_SUBSCRIPTION,
                    ContentType.PACKING_SUBSCRIPTION);
            }
        }

        return vnptPayResponseRepository.save(responseStatus);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public VNPTPayResponse getVnptPayResponseQR(QRNotifyPaymentReqDTO reqDTO, VNPTPayResponse responseStatus,
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList) {
        responseStatus.setResponseCode(reqDTO.getResponseCode());
        responseStatus.setMerchantServiceId(reqDTO.getMerchantClientId());
        responseStatus.setDescription(reqDTO.getDescription());
        responseStatus.setAmount(new BigDecimal(reqDTO.getAmount()));
        responseStatus.setVnptpayTransactionId(reqDTO.getQrTxnId());
        responseStatus.setPaymentMethod(PaymentMethodEnum.VNPTPAY_QR.name());
        responseStatus.setAdditionalInfo(reqDTO.getAdditionalInfo());
        responseStatus.setOrderStatus(OrderStatusEnum.PROCESSED.value);
        responseStatus.setSecureCode(reqDTO.getChecksum());
        responseStatus.setPayDate(LocalDateTime.parse(reqDTO.getPayDate(), DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS)));
        responseStatus.setBuyNow(0);
        CustomUserDetails userDetails = AuthUtil.getCurrentUser();
        if (userDetails != null && userDetails.getBearerToken() != null) {
            responseStatus.setAccessToken(userDetails.getBearerToken());
        }
        //
        String token = commonService.getVNPTPayToken(KeyGenType.TOKEN_PAYMENT);
        commonService.deleteTokenVNPT(KeyGenType.TOKEN_PAYMENT.getKey(), token);
        String finalToken = StringUtil.replaceToken(token);
        //
        List<String> billIdStrLst = responseStatus.getLstBillId();
        String cartCode = null;
        log.info("===== handlePaymentShoppingCart  responseStatus.getLstBillId().size() {}", billIdStrLst.size());
        log.info("===== handlePaymentShoppingCart  responseStatus.getLstBillId data {}", Arrays.toString(billIdStrLst.toArray()));
        for (String billingIdStr : billIdStrLst) {
            SubscriptionRegisterInfo subscriptionRegisterInfo =
                    saveBatchKafkaAndBatchDHSXKD(Long.valueOf(StringUtils.trim(billingIdStr)), finalToken, responseStatus,
                    PaymentMethodEnum.VNPTPAY_QR);
            cartCode = Objects.nonNull(subscriptionRegisterInfo) &&
                    Objects.nonNull(subscriptionRegisterInfo.getMainSubscription()) &&
                    Objects.nonNull(subscriptionRegisterInfo.getMainSubscription().getCartCode()) ?
                    subscriptionRegisterInfo.getMainSubscription().getCartCode() : null;
            subscriptionRegisterInfoList.add(subscriptionRegisterInfo);
        }

        if (StringUtils.equals(reqDTO.getResponseCode(), PaymentQRStatusEnum.SUCCESS.getValue()) && Objects.nonNull(cartCode)) {
            creditNoteService.updateOldCreditNotesWithCartCode(cartCode);
        }
        return vnptPayResponseRepository.save(responseStatus);
    }

    private SubscriptionRegisterInfo saveBatchKafkaAndBatchDHSXKD(Long billingId, String finalToken, VNPTPayResponse reqDTO,
                                                                  PaymentMethodEnum paymentMethod) {
        BillStatusEnum billStatusEnum;
        SubscriptionRegisterInfo subscriptionRegisterInfo = new SubscriptionRegisterInfo();
        Bills billsVnptRes = billsRepository.findById(billingId).orElse(null);
        if (billsVnptRes == null) {
            return null;
        }
        Integer actionType = billsVnptRes.getActionType();

        Pricing pricing = null;
        ComboPlan comboPlan = null;
        if (billsVnptRes.getSubscriptionsId() == null) {
            return null;
        }
        Optional<Subscription> subOpt = subscriptionRepository.findById(billsVnptRes.getSubscriptionsId());
        if (!subOpt.isPresent()) {
            throw new VNPTPayResponseException(paymentMethod.equals(PaymentMethodEnum.VNPTPAY_QR) ?
                    getPaymentStatusQR(PaymentQRStatusEnum.ERROR_OTHER.getMessageKey(), "", PaymentQRStatusEnum.ERROR_OTHER.getValue()) :
                    getPaymentStatus(
                            PaymentStatusEnum.ERROR_OTHER.getMessageKey(),
                            ErrorKey.SECURE_CODE,
                            PaymentStatusEnum.ERROR_OTHER.getValue(),
                            reqDTO.getMerchantServiceId(),
                            reqDTO.getMerchantOrderId()
                    ));
        }
        Subscription subscription = subOpt.get();
        Long userCreatedSub = subscription.getCreatedBy();
        //Lấy change_subs để lưu lịch sử
        List<SubscriptionComboAddon> subscriptionComboAddonsOld = new ArrayList<>();
        if (Objects.nonNull(subscription.getComboPlanId())) {
            subscriptionComboAddonsOld = subscriptionComboAddonRepository.findBySubscriptionId(subscription.getId());
        }

        boolean isDevice = false;
        if (Objects.nonNull(subscription.getPricingId())) {
            pricing = subscriptionService.checkExistPricing(billsVnptRes.getPricingId());
            updatePricingDHSX(subscription, pricing);
            ServiceEntity serviceEntity = serviceRepository.findById(pricing.getServiceId()).orElse(null);
            if (Objects.nonNull(serviceEntity)) {
                isDevice = Objects.equals(ServiceProductTypeEnum.DEVICE, serviceEntity.getProductType());
            }
        } else if (Objects.nonNull(subscription.getComboPlanId())) {
            comboPlan = subscriptionService.validateComboPlan(billsVnptRes.getComboPlanId());
            isDevice = comboPlanRepository.checkDeviceInCombo(comboPlan.getId());
        } else if (Objects.nonNull(subscription.getVariantId())) {  // Chỉ mua thiết bị
            isDevice = true;
        }

        Integer pricingType;
        if (Objects.nonNull(pricing)) {
            pricingType = pricing.getPricingType();
        } else if (Objects.nonNull(comboPlan)) {
            pricingType = comboPlan.getComboPlanType();
        } else {
            pricingType = PricingTypeEnum.PREPAY.value;
        }

        Subscription oldSubscription = new Subscription();
        Subscription newSubscription;
        // Lưu thông tin transaction

        subscriptionRegisterInfo.getRegisterInfoDTO().setPricing(pricing);
        subscriptionRegisterInfo.getRegisterInfoDTO().setComboPlan(comboPlan);
        subscriptionRegisterInfo.setMainSubscription(subscription);
        subscriptionRegisterInfo.setMainBills(billsVnptRes);
        //==============================Nếu thanh toán thành công================================
        if (StringUtils.equals(reqDTO.getResponseCode(), PaymentStatusEnum.SUCCESS.getValue())) {
            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
            // Đồng bộ trạng thái confirm_status giữa thuê bao chính và thuê bao được tặng
            subscriptionService.confirmGiveAwaySubscription(subscription.getId());

            List<Bills> billsList = billsRepository.findBySubscriptionsId(subscription.getId());
            if (!checkBillOutOfDate(billsList) && subscription.getStartedAt().compareTo(new Date()) <= 0) {
                subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
            }
            subscription = subscriptionService.autoActivateSub(subscription, billsList);
            subscription.setPaymentMethod(paymentMethod.value);
            Subscription saveInfo = subscriptionRepository.save(subscription);
            // Lưu lịch sử thay đổi trạng thái thuê bao
            subscriptionHistoryService.saveStatusHistory(saveInfo);
            log.info("===============> SUB: " + saveInfo);
            billStatusEnum = BillStatusEnum.PAID;
            Subscription finalSubscription = saveInfo;

            BeanUtils.copyProperties(subscription, oldSubscription);

            //Nếu thanh toán trả trước
            if (Objects.equals(PricingTypeEnum.PREPAY.value, pricingType)
                    || Objects.equals(PricingTypeEnum.POSTPAID.value, pricingType)) { // update xu ly cho goi tra sau
                // Neu action type la Update
                if (Objects.equals(actionType, ACTION_UPDATE)) {
                    if (Objects.nonNull(finalSubscription.getPricingId())) {
                        //================= XÓA THÔNG TIN ADDON, COUPON CŨ ============================//
                        subscriptionAddonsRepository.deleteAllBySubscriptionId(subscription.getId());
                        newSubscription = subscriptionService.updatePricingByBatch(finalSubscription.getId(),
                                ActionChangeSubEnum.NOW.value, null);
                    } else if (Objects.nonNull(finalSubscription.getComboPlanId())) {
                        newSubscription = subscriptionService.updateComboByBatch(finalSubscription,
                                ActionChangeSubEnum.NOW.value, null);
                        //Lưu lịch sử addon
                        List<SubscriptionComboAddon> subscriptionComboAddonsNew = subscriptionComboAddonRepository
                                .findBySubscriptionId(newSubscription.getId());
                        comboService.saveSubComboHistoryChangeAddon(newSubscription.getId(), subscriptionComboAddonsOld,
                                subscriptionComboAddonsNew);
                    }
                    //Lưu thông tin send kafka
                    batchKafkaRepository.save(
                            new BatchKafka(null, finalSubscription.getId(), 1, finalToken, DeletedFlag.NOT_YET_DELETED.getValue(), null)
                    );
                    //Lưu thông tin batch_dhsxkd
                    batchDhsxkdRepository.save(
                            new BatchDHSXKD(null, finalSubscription.getId(), billingId, reqDTO.getMerchantOrderId(),
                                    ActionTypeEnum.UPDATE_SUBSCRIPTION.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));
                } else if (Objects.equals(actionType, ACTION_CHANGE_PLAN)) {
                    if (Objects.nonNull(finalSubscription.getPricingId())) {
                        Subscription result = subscriptionService.swapPricingByBatch(finalSubscription, ActionChangeSubEnum.NOW.value, null);
                        Pricing pricingOld = pricingRepository.findByIdAndDeletedFlag(oldSubscription.getPricingId(),
                                DeletedFlag.NOT_YET_DELETED.getValue())
                            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID,
                                String.valueOf(oldSubscription.getPricingId())));
                        // lưu lịch sử đổi gói
                        String histContent = String
                                .format(SubscriptionHistoryConstant.CHANGE_PRICING, pricingOld.getPricingName(), pricing.getPricingName());
                        subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent, billsVnptRes.getId(),
                                SubscriptionHistoryConstant.ContentType.CHANGE_PRICING);
                    } else if (Objects.nonNull(finalSubscription.getComboPlanId())) {
                        Subscription result = subscriptionService.swapComboPlanByBatch(finalSubscription, ActionChangeSubEnum.NOW.value, null);
                    }
                    //Lưu thông tin send kafka
                    // Khởi tạo bằng giá trị xóa, khi nào batchDHSXKD xử lý xong dữ liệu mới sẽ update lại để chạy kafka sau
                    batchKafkaRepository.save(
                            new BatchKafka(null, finalSubscription.getId(), 2, finalToken, DeletedFlag.DELETED.getValue(), null)
                    );
                    //Lưu thông tin batch_dhsxkd
                    batchDhsxkdRepository.save(
                            new BatchDHSXKD(null, finalSubscription.getId(), billingId, reqDTO.getMerchantOrderId(),
                                    ActionTypeEnum.CHANGE_PLAN.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));
                } else {
                    if (!isDevice) {
                    //Lưu thông tin send kafka
                    batchKafkaRepository.save(
                            new BatchKafka(null, finalSubscription.getId(), 0, finalToken, DeletedFlag.NOT_YET_DELETED.getValue(), null)
                    );
                    //Lưu thông tin batch_dhsxkd
                    batchDhsxkdRepository.save(
                            new BatchDHSXKD(null, finalSubscription.getId(), billingId, reqDTO.getMerchantOrderId(),
                                    ActionTypeEnum.CREATE_SUBSCRIPTION.getValue(), DeletedFlag.NOT_YET_DELETED.getValue()));

                    if (StringUtils.isNotBlank(subOpt.get().getTrafficId())) {
                        saveMasofferWaitingSent(saveInfo.getId(), MasOfferStatusEnum.PENDING);
                        subscription.setIsCallMasoffer(YesNoEnum.NO.value);
                    }
                } else {
                        subscriptionRegisterInfo.setDevice(true);
                        saveInfo.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);
                        subscriptionRepository.save(saveInfo);
                    }

                    // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
                    inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
                    // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
                    if (subscription.getQuotationId() != null) {
                        quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER,
                            PortalType.UNSET, new ChangeStatusMetadataDTO(subscription.getCode(), subscription.getCreatedBy()));
                    }
                }
            }
        } else {
            billStatusEnum = BillStatusEnum.FAILURE;
        }

        //Thực hiện lưu thông tin hóa đơn nếu thanh toán thành công

        billsVnptRes.setStatus(billStatusEnum.value);
        if (StringUtils.equals(reqDTO.getResponseCode(), PaymentStatusEnum.SUCCESS.getValue())) {
            billsVnptRes.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
        }
        //Ngày thanh toán sẽ được set khi thanh toán thành công
        if (billStatusEnum == BillStatusEnum.PAID) {
            billsVnptRes.setPaymentDate(new Date());
            // gui mail bill
            //update trạng thái credit note thành đã hoàn trả khi thanh toán thành công
            List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository.getAllChangeCreditNoteByBillingId(billsVnptRes.getId());
                /*Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteList = listCreditNote.stream()
                    .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue));
                creditNoteService.createCreditNote(subscription.getId(), billsVnptRes, creditNoteList, CreditNoteDetailStatusEnum.ADJUSTED,
                    null,
                    isChangePlanSubscription);
                changeCreditNoteRepository.updateChangeCreditNoteStatusByBillingId(billsVnptRes.getId(), YesNoEnum.YES.value);*/
            creditNoteService.createNewCreditNote(billsVnptRes, listCreditNote, userCreatedSub, false);
        }

        billsRepository.save(billsVnptRes);
        if (Objects.equals(billsVnptRes.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value) && Objects
                .equals(billsVnptRes.getStatus(), BillStatusEnum.PAID.value)) {
            creditNoteRepository.updateCreditNoteByBillingApply(billsVnptRes.getId(), CreditNoteDetailStatusEnum.REFUNDED.code);
        }
        couponItemService.used(subscription);

        boolean allowCallApiOrderService = subscriptionValidateService.checkServiceTypeAndPricingCodeIsLong(subscription);

        if (allowCallApiOrderService) {
            orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricing, comboPlan, null);
        }
        return subscriptionRegisterInfo;
    }

    /**
     * Cấu hình gửi mail
     */
    public void sendMail(Subscription subscription, Bills bills, Pricing pricing, ComboPlan comboPlan, String actor) {

        EmailCodeEnum emailCodeEnum;
        Map<String, String> mapDefaultValue = new HashMap<>();
        Optional<User> userOpt =
            userRepository.findByIdAndDeletedFlagAndStatus(subscription.getUserId(),
                EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userOpt.isPresent()) {
            return;
        }
        User user = userOpt.get();
        if(Objects.nonNull(pricing)){
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_SERVICE.getValue(),
                Objects.isNull(pricing.getServiceEntity()) ? StringUtils.EMPTY
                    : pricing.getServiceEntity().getServiceName());
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_PRICING.getValue(),
                Objects.isNull(pricing.getPricingName()) ? StringUtils.EMPTY
                    : pricing.getPricingName());
        } else if(Objects.nonNull(comboPlan)){
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO.getValue(),
                Objects.isNull(comboPlan.getCombo()) || Objects.isNull(comboPlan.getCombo().getComboName()) ? StringUtils.EMPTY
                    : comboPlan.getCombo().getComboName());
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO_PRICING.getValue(),
                Objects.isNull(comboPlan.getComboName()) ? StringUtils.EMPTY
                    : comboPlan.getComboName());
        }
        mapDefaultValue.putIfAbsent(ParamEmailEnum.CODE_TRANSACTION.getValue(),
                subscription.getSubCode());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.CODE_INVOICE.getValue(), bills.getBillCode());
        Locale localeVN = new Locale("vi", "VN");
        NumberFormat currencyVN = NumberFormat.getCurrencyInstance(localeVN);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.TOTAL_AMOUNT.getValue(),
                Objects.isNull(bills.getTotalAmount()) ? StringUtils.EMPTY
                        : currencyVN.format(bills.getTotalAmount()));
        String billingDate = Objects.isNull(bills.getBillingDate()) ? StringUtils.EMPTY
                : DateUtil.convertLocalDateToString(DateUtil.toLocalDate(bills.getBillingDate()),
                        DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String endDate = Objects.isNull(bills.getEndDate()) ? StringUtils.EMPTY
                : DateUtil.convertLocalDateToString(DateUtil.toLocalDate(bills.getEndDate()),
                        DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String remark = "Thanh toán cho chu kì 1 (từ " + billingDate + " đến " + endDate + ")";
        mapDefaultValue.put(ParamEmailEnum.REMARKS.getValue(), remark);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(),
                StringUtils.isEmpty(user.getName()) ? StringUtils.EMPTY : user.getName());
        emailCodeEnum = EmailCodeEnum.IV03;
        Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService
                .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, actor);

        if (emailTemplateOpt.isPresent()) {
            emailService.save(user.getEmail(), emailTemplateOpt.get());
        }

        // Send mail IV-04
        Long provinceId = Objects.nonNull(user.getProvinceId()) ? user.getProvinceId() : -1L;
        List<User> adminProvince = provinceId.equals(-1L) || Objects.isNull(provinceId)
            ? Collections.EMPTY_LIST
            : userRepository.getListAdminByProvinceId(provinceId);

        mapDefaultValue.put(ParamEmailEnum.CUSTOMER_COMPANY_NAME.getValue(),
            StringUtils.isEmpty(user.getCompany()) ? StringUtils.EMPTY : user.getCompany());
        mapDefaultValue.put(ParamEmailEnum.USER_PAYMENT.getValue(),
            user.getFirstName() + " " + user.getLastName());
        mapDefaultValue.put(ParamEmailEnum.USER_PAYMENT_ROLE.getValue(),
            String.join(",", userRepository.getRole(user.getId())));
        mapDefaultValue.put(ParamEmailEnum.DEADLINE_PAYMENT.getValue(), Objects.isNull(bills.getFinalPaymentTerm()) ? StringUtils.EMPTY
            : DateUtil.convertLocalDateToString(DateUtil.toLocalDate(bills.getFinalPaymentTerm()),
                DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        // Duyệt list user admin có cùng tỉnh thành với người tạo
        List<MailSendParamDTO> param = adminProvince.stream().map(admin -> {

            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(admin.getEmail());

            mapDefaultValue.put(ParamEmailEnum.USER.getValue(), admin.getName());

            List<MailParamResDTO> mailParamResDTOs = mapDefaultValue.entrySet().stream()
                    .map(entry -> new MailParamResDTO(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());

            mailSendParamDTO.setListMailParam(mailParamResDTOs);
            return mailSendParamDTO;
        }).collect(Collectors.toList());
        emailService.sendMultiMail(EmailCodeEnum.IV04, param);

        if (Objects.isNull(user.getParentId())
            || SubscriptionConstant.PARENT_ID.equals(user.getParentId())) {
            return;
        }

        Optional<User> userParentOpt = userRepository.findByIdAndDeletedFlagAndStatus(
                user.getParentId(), EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userParentOpt.isPresent()) {
            return;
        }
        emailCodeEnum = EmailCodeEnum.IV03;
        mapDefaultValue.put(ParamEmailEnum.USER.getValue(), userParentOpt.get().getName());
        Optional<com.model.dto.EmailTemplate> emailTemplateUserParentOpt = emailTemplateService
                .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, actor);
        if (emailTemplateUserParentOpt.isPresent()) {
            emailService.save(userParentOpt.get().getEmail(), emailTemplateOpt.get());
        }

    }

        /**
     * Cấu hình gửi mail shopping cart thanh toán thành công
     */
    @Override
    public void sendMailSuccessPayment(List<SubscriptionRegisterInfo> subscriptionRegisterInfos,
        String userInfo, BigDecimal totalAmount, String actor) {
        List<Subscription> subscriptions = subscriptionRegisterInfos.stream()
            .map(SubscriptionRegisterInfo::getMainSubscription)
            .collect(Collectors.toList());
        List<Bills> bills = subscriptionRegisterInfos.stream()
            .map(SubscriptionRegisterInfo::getMainBills)
            .collect(Collectors.toList());
        List<Pricing> lstPricing = subscriptionRegisterInfos.stream()
            .map(pricing -> pricing.getRegisterInfoDTO().getPricing())
            .filter(p -> Objects.nonNull(p)).collect(Collectors.toList());
        List<ComboPlan> lstComboPlan = subscriptionRegisterInfos.stream()
            .map(comboPlan -> comboPlan.getRegisterInfoDTO().getComboPlan())
            .filter(c -> Objects.nonNull(c)).collect(Collectors.toList());

        // List danh sách dev/admin đăng ký hộ sme
        Optional<User> userOptionals = userRepository.findById(
            subscriptions.get(0).getRegistedBy());
        if (!userOptionals.isPresent()) {
            return;
        }

        InfoSendMailDTO infoSendMailDTO = subscriptionNotificationService.getInformationForSendMailSub(
            subscriptions.get(0), !CollectionUtils.isEmpty(lstPricing) ? lstPricing.get(0) : null,
            !CollectionUtils.isEmpty(lstComboPlan) ? lstComboPlan.get(0) : null, userInfo,
            userOptionals, true);
        if (Objects.isNull(infoSendMailDTO)) {
            return;
        }
        SubscriptionActiveMailParamDTO activeMailParamDTO = infoSendMailDTO.getActiveMailParamDTO();
        if (Objects.isNull(activeMailParamDTO)) {
            return;
        }

        EmailCodeEnum emailCodeEnum;
        Map<String, String> mapDefaultValue = new HashMap<>();
        Optional<User> userOpt =
            userRepository.findByIdAndDeletedFlagAndStatus(subscriptions.get(0).getUserId(),
                EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userOpt.isPresent()) {
            return;
        }
        User user = userOpt.get();
        Long provinceId = Objects.nonNull(user.getProvinceId()) ? user.getProvinceId() : -1L;
        List<User> adminProvince = provinceId.equals(-1L) || Objects.isNull(provinceId)
            ? Collections.EMPTY_LIST
            : userRepository.getListAdminByProvinceId(provinceId);
        List<User> smeAdmin = userRepository.getSmeAdminById(user.getId());
        StringBuilder bodyEmailAll = new StringBuilder();
        StringBuilder bodyEmailSME = new StringBuilder();
        for (int i = 0; i < subscriptionRegisterInfos.size(); i++) {
            SubscriptionRegisterInfo subscriptionRegisterInfo = subscriptionRegisterInfos.get(i);
            Pricing pricing = subscriptionRegisterInfo.getRegisterInfoDTO().getPricing();
            ComboPlan comboPlan = subscriptionRegisterInfo.getRegisterInfoDTO().getComboPlan();
            if (Objects.nonNull(pricing)) {
                String str1 =
                    "<p style=\"margin: 0;\">" + (i + 1) + CharacterConstant.DOT
                        + CharacterConstant.SPACE + "Dịch vụ " + (i
                        + 1)
                        + CharacterConstant.COLON
                        + CharacterConstant.SPACE + pricing.getServiceEntity().getServiceName()
                        + "</span></p>" +
                        "<p style=\"margin: 0;\">- Gói dịch vụ: <span>"
                        + CharacterConstant.COLON.concat(
                        CharacterConstant.SPACE + pricing.getPricingName()) + "</span></p>";
                bodyEmailAll.append(str1);
                bodyEmailSME.append(str1);
            } else if (Objects.nonNull(comboPlan)) {
                String str2 = "<p style=\"margin: 0;\">" + (i + 1) + CharacterConstant.DOT
                    + CharacterConstant.SPACE + "Dịch vụ " + (i
                    + 1)
                    + CharacterConstant.COLON
                    + CharacterConstant.SPACE + comboPlan.getCombo().getComboName()
                    + "</span></p>" +
                    "<p style=\"margin: 0;\">- Gói dịch vụ: <span>"
                    + CharacterConstant.COLON.concat(
                    CharacterConstant.SPACE + comboPlan.getComboName()) + "</span></p>";
                bodyEmailAll.append(str2);
                bodyEmailSME.append(str2);
            }
            bodyEmailSME.append(
                "<p style=\"margin: 0;\">- Mã giao dịch: <span>" + CharacterConstant.SPACE
                    + activeMailParamDTO.getCodeTransaction()
                    + "</span></p>");
        }
        mapDefaultValue.putIfAbsent(ParamEmailEnum.ALL_SERVICE.getValue(),
            String.valueOf(bodyEmailAll));
        mapDefaultValue.putIfAbsent(ParamEmailEnum.CODE_INVOICE.getValue(),
            bills.get(0).getCartCode());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.TOTAL_AMOUNT.getValue(),
            String.valueOf(totalAmount));
//        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(),
//            StringUtils.isEmpty(user.getName()) ? StringUtils.EMPTY : user.getName());
        emailCodeEnum = EmailCodeEnum.IV07;
        Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService
            .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, actor);
        mapDefaultValue.put(ParamEmailEnum.CODE_ORDER.getValue(), // code_order : Mã đơn hàng (ID giỏ hàng)
            subscriptionRegisterInfos.get(0).getMainSubscription().getCartCode());
        if (user.getCustomerType().equals(CustomerTypeEnum.PERSONAL.getValue())) {
            mapDefaultValue.put(ParamEmailEnum.CUSTOMER_COMPANY_NAME.getValue(),
                activeMailParamDTO.getNameRegister());
        } else {
            mapDefaultValue.put(ParamEmailEnum.CUSTOMER_COMPANY_NAME.getValue(),
                StringUtils.isEmpty(user.getName()) ? StringUtils.EMPTY : user.getName());
        }
        mapDefaultValue.put(ParamEmailEnum.USER_PAYMENT.getValue(),
            activeMailParamDTO.getNameRegister());
        List<String> userRoles = userOptionals.get().getRoles().stream().map(x -> x.getName()).collect(Collectors.toList());
        mapDefaultValue.put(ParamEmailEnum.USER_PAYMENT_ROLE.getValue(),
            String.join(",", userRoles));

        // Duyệt list user admin có cùng tỉnh thành với người tạo
//        send for province admin
        List<MailSendParamDTO> param = adminProvince.stream().map(admin -> {

            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(admin.getEmail());
//            mapDefaultValue.put(ParamEmailEnum.USER.getValue(), admin.getName());

            List<MailParamResDTO> mailParamResDTOs = mapDefaultValue.entrySet().stream()
                .map(entry -> new MailParamResDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

            mailSendParamDTO.setListMailParam(mailParamResDTOs);
            return mailSendParamDTO;
        }).collect(Collectors.toList());
        emailService.sendMultiMail(EmailCodeEnum.IV08, param);
//        Send for sme, adminSme
        StringBuilder finalBodyEmailSME = bodyEmailSME;
        List<MailSendParamDTO> paramSme = smeAdmin.stream().map(sme -> {

            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(sme.getEmail());
//            mapDefaultValue.put(ParamEmailEnum.USER.getValue(), admin.getName());
            mapDefaultValue.put(ParamEmailEnum.ALL_SERVICE.getValue(),
                String.valueOf(finalBodyEmailSME));
            List<MailParamResDTO> mailParamResDTOs = mapDefaultValue.entrySet().stream()
                .map(entry -> new MailParamResDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

            mailSendParamDTO.setListMailParam(mailParamResDTOs);
            return mailSendParamDTO;
        }).collect(Collectors.toList());
        emailService.sendMultiMail(EmailCodeEnum.IV07, paramSme);

        if (Objects.isNull(user.getParentId())
            || SubscriptionConstant.PARENT_ID.equals(user.getParentId())) {
            return;
        }

        Optional<User> userParentOpt = userRepository.findByIdAndDeletedFlagAndStatus(
            user.getParentId(), EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userParentOpt.isPresent()) {
            return;
        }
        emailCodeEnum = EmailCodeEnum.IV07;
        mapDefaultValue.put(ParamEmailEnum.USER.getValue(), userParentOpt.get().getName());
        Optional<com.model.dto.EmailTemplate> emailTemplateUserParentOpt = emailTemplateService
            .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, actor);
        if (emailTemplateUserParentOpt.isPresent()) {
            emailService.save(userParentOpt.get().getEmail(), emailTemplateOpt.get());
        }
    }


    /**
     * Lưu data masoffer chờ gửi
     */
    private void saveMasofferWaitingSent(Long id, MasOfferStatusEnum pending) {
        BatchMasoffer masoffer = new BatchMasoffer();
        masoffer.setSubscriptionId(id);
        masoffer.setActionStatus(pending.key);
        this.batchMasofferRepository.save(masoffer);
    }

    /**
     *  Kiểm tra mã bảo mật
     *
     * @param reqDTO the req dto
     *
     * @return the boolean
     */
    private boolean validateSecureCode(PaymentStatusReqDTO reqDTO, Long subscriptionId) {
        List<Integer> set3rdServiceOwner = Arrays.asList(ServiceOwnerEnum.SAAS.getValue(), ServiceOwnerEnum.NONE.getValue());
        // Lấy thông tin private key của đơn hàng theo merchantServiceId
        String privateKey = getPrivateKeyByMerchantServiceId(reqDTO.getMerchantServiceId());

        // Nếu là mua 1 dịch vụ/combo của nhà cung cấp bên thứ 3, sử dụng chung một ví duy nhất. Cần sử dụng private key này để xác thực
        if (subscriptionId != null) {
            IServiceOwnerInfoDTO ownerInfo = subscriptionRepository.findServiceOwnerInfo(subscriptionId);
            if (ownerInfo != null &&
                ownerInfo.getServiceOwnerPartner() == null && // Không phải dịch vụ của partner
                (ownerInfo.getServiceOwner() != null && set3rdServiceOwner.contains(ownerInfo.getServiceOwner()))) { // Dịch vụ từ 3rd party
                Wallet3rdPartyDTO systemConfig = (Wallet3rdPartyDTO) systemParamService.getParam(SystemParamTypeEnum.WALLET_3RD_PARTY);
                if (systemConfig != null && systemConfig.getPrivateKey() != null) {
                    privateKey = systemConfig.getPrivateKey();
                    log.info("validateSecureCode: validate secured code for 3rd party, using private key from system config");
                }
            }
        }
        String privateKeyMask = privateKey != null ? privateKey.substring(privateKey.length() - 4) : Strings.EMPTY;
        log.info("validateSecureCode: validate secured code by private key '***{}", privateKeyMask);

        List<String> actualArr = Arrays.asList(
                reqDTO.getPayAction(),
                reqDTO.getResponseCode(),
                reqDTO.getMerchantServiceId(),
                reqDTO.getMerchantOrderId(),
                String.valueOf(reqDTO.getAmount()),
                reqDTO.getCurrencyCode(),
                reqDTO.getVnptpayTransactionId(),
                reqDTO.getPaymentMethod(),
                reqDTO.getPayDate(),
                reqDTO.getAdditionalInfo(),
                privateKey
        );
        String actualString = Strings.join(actualArr, '|');
        String actualEncode = DigestUtils.sha256Hex(actualString);
        return !StringUtils.equals(actualEncode, reqDTO.getSecureCode());
    }

    private boolean validateChecksum(QRNotifyPaymentReqDTO reqDTO, VNPTPayResponse responseStatus) {
        Long userIdLogin = null;
        if (Objects.nonNull(responseStatus) && Objects.nonNull(responseStatus.getAccessToken())) {
            userIdLogin = getCurrentUserId(responseStatus.getAccessToken());
        }
        if (Objects.isNull(userIdLogin) && Objects.nonNull(responseStatus) && Objects.nonNull(responseStatus.getSubscriptionId())) {
            Subscription subscription = subscriptionRepository.findById(responseStatus.getSubscriptionId()).orElse(null);
            if (Objects.nonNull(subscription)) userIdLogin = subscription.getCreatedBy();
        }
        if (Objects.isNull(userIdLogin) && Objects.nonNull(responseStatus) && !CollectionUtils.isEmpty(responseStatus.getLstSubId())) {
            Subscription subscription = subscriptionRepository.findById(Long.valueOf(responseStatus.getLstSubId().get(0))).orElse(null);
            if (Objects.nonNull(subscription)) userIdLogin = subscription.getCreatedBy();
        }
        if (Objects.isNull(userIdLogin)) {
            CustomUserDetails userDetails = AuthUtil.getCurrentUser();
            if (Objects.nonNull(userDetails)) userIdLogin = userDetails.getId();
        }
        if (Objects.isNull(userIdLogin)) return true;
        //
        ClueDTO clueDTO = subscriptionRepository.getMerchantByUserId(userIdLogin);
        List<String> actualArr = Arrays.asList(
                reqDTO.getResponseCode(),
                reqDTO.getDescription(),
                reqDTO.getMerchantClientId(),
                reqDTO.getMerchantCode(),
                reqDTO.getTerminalId(),
                reqDTO.getBillNumber(),
                reqDTO.getTxnId(),
                reqDTO.getMsgType(),
                reqDTO.getCustomerName(),
                reqDTO.getAccountNo(),
                reqDTO.getMobile(),
                reqDTO.getAmount(),
                reqDTO.getCcy(),
                reqDTO.getQrcodeType(),
                reqDTO.getQrTxnId(),
                reqDTO.getPaymentMethod(),
                reqDTO.getPayDate(),
                Objects.nonNull(reqDTO.getAdditionalInfo()) ? reqDTO.getAdditionalInfo() : "null",
                Objects.nonNull(clueDTO) ? clueDTO.getQrSecretKey() : ""
        );
        String actualString = Strings.join(actualArr, '|');
        String actualEncode = DigestUtils.sha256Hex(actualString);
        return !StringUtils.equals(actualEncode, reqDTO.getChecksum());
    }

    /**
     * Trả về trạng thái payment
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     *
     * @return the bad request exception
     */
    private PaymentStatusResDTO getPaymentStatus(String messageKeyConstant, String errorKey, String responseCode, String merchantServiceId, String merchantOrderId) {

        String message = messageSource.getMessage(messageKeyConstant, new Object[]{errorKey},
                LocaleContextHolder.getLocale());

        LocalDateTime currentDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS);
        String createdDate = currentDate.format(formatter);

        List<String> stringArr = Arrays.asList(
                responseCode,
                message,
                createdDate,
                merchantServiceId,
                merchantOrderId,
                getPrivateKeyByMerchantServiceId(merchantServiceId)
        );

        PaymentStatusResDTO resDTO = PaymentStatusResDTO.builder()
            .responseCode(responseCode)
            .description(message)
            .createDate(createdDate)
            .merchantServiceId(merchantServiceId)
            .merchantOrderId(merchantOrderId)
            .secureCode(DigestUtils.sha256Hex(Strings.join(stringArr, '|')))
            .build();
        log.info("==========Response api notification-receiver: " + resDTO.toString());
        return resDTO;
    }

    private QRNotifyPaymentResDTO getPaymentStatusQR(String messageKeyConstant, String errorKey, String responseCode) {

        String message = messageSource.getMessage(messageKeyConstant, new Object[]{errorKey},
                LocaleContextHolder.getLocale());
        QRNotifyPaymentResDTO resDTO = QRNotifyPaymentResDTO.builder()
                .responseCode(responseCode)
                .description(message)
                .data(null)
                .build();

        log.info("==========Response api notification-receiver-qr: " + resDTO.toString());
        return resDTO;
    }

    /**
     * xử lý cho giỏ hàng
     *
     */
    private TransactionStatusResDTO checkStatusTransactionShoppingCart(TransactionStatusReqDTO transactionStatusReqDTO, String token,
        VNPTPayResponse vnptPayResponse, TransactionStatusResDTO transactionStatusResDTO) {
        log.info("shopping cart Start method checkStatusTransaction");
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList = new ArrayList<>();
        BigDecimal totalAmount = vnptPayResponse.getAmount();
        boolean checkSendMail = getTransactionStatusResDTO(transactionStatusReqDTO, token, vnptPayResponse,
            transactionStatusResDTO, subscriptionRegisterInfoList);

        if (checkSendMail) return transactionStatusResDTO;
        sendEmailAndNotify(vnptPayResponse, subscriptionRegisterInfoList, totalAmount);
        transactionStatusResDTO.setStatus(VnptPayTransactionStatusEnum.valueOf(vnptPayResponse.getTransactionStatus()));
        transactionStatusResDTO.setTransactionStatus(vnptPayResponse.getTransactionStatus());
        log.info(" end checkStatusTransactionShoppingCart " + Thread.currentThread().getId() + ": " + Thread.currentThread().getName());
        return transactionStatusResDTO;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected boolean getTransactionStatusResDTO(TransactionStatusReqDTO transactionStatusReqDTO, String token,
        VNPTPayResponse vnptPayResponse, TransactionStatusResDTO transactionStatusResDTO,
        List<SubscriptionRegisterInfo> subscriptionRegisterInfoList) {
        List<String> lstSubIdStr = vnptPayResponse.getLstSubId();
        List<Subscription> lstSubObj = new ArrayList<>();
        Map<Long, Subscription> idSubMap = new HashMap();
        for (String subIdStr : lstSubIdStr) {
            Subscription subscription = subscriptionService.findByIdAndDeletedFlag(Long.valueOf(subIdStr), DeletedFlag.NOT_YET_DELETED.getValue());
            lstSubObj.add(subscription);
            idSubMap.put(subscription.getId(), subscription);
            log.info("===== shoping cart Check subId {}, code dhsxkd - verify1: {}", subscription.getId(), subscription.getSubCodeDHSXKD());
        }
        List<String> billIdStrLst = vnptPayResponse.getLstBillId();
        transactionStatusResDTO.setLstBillId(billIdStrLst);
        List<Bills> billObjLst = new ArrayList<>();
        for (String billIdStr : billIdStrLst) {
            Bills bill = billsRepository.findById(Long.valueOf(billIdStr))
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.BILL, ErrorKey.ID, billIdStr));
            billObjLst.add(bill);
            log.info("===== shoping cart Check billId {}, cart code - verify: {}", bill.getId(), bill.getCartCode());
        }
        Subscription firstSubs = lstSubObj.get(0);
        if(firstSubs == null ) {
            String message = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, subscriptionError,
                    LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SUBSCRIPTION,
                ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        transactionStatusResDTO.setCartCodeSub(firstSubs.getCartCode());
        Long userAdminSme = firstSubs.getUserId();
        int portalType;
        try {
            portalType = Integer.parseInt(transactionStatusResDTO.getPortalType());
        } catch (Exception e) {
            portalType = PortalType.SME.getType();
        }
        switch (portalType) {
            case ADMIN:
                transactionStatusResDTO.setPortalType(PortalType.ADMIN.name());
                break;
            case DEV:
                transactionStatusResDTO.setPortalType(PortalType.DEV.name());
                break;
            default:
                transactionStatusResDTO.setPortalType(PortalType.SME.name());
                break;
        }

        Bills billsVnptRes = billObjLst.get(0);
        Integer actionType = billsVnptRes.getActionType();
        actionType = (actionType == null) ? OTHER : actionType ;
        transactionStatusResDTO.setAction(actionType);

        transactionStatusResDTO.setCartCodeBill(billsVnptRes.getCartCode());
        transactionStatusResDTO.setActionType(
            (Objects.nonNull(billsVnptRes) && Objects.nonNull(billsVnptRes.getActionType()))
                ? ActionTypeEnum.valueOf(billsVnptRes.getActionType()) : ActionTypeEnum.CREATE_SUBSCRIPTION);

        transactionStatusResDTO.setMerchantOrderId(transactionStatusReqDTO.getMerchantOrderId());
        transactionStatusResDTO.setScreenType(vnptPayResponseRepository.getScreenType(transactionStatusReqDTO.getMerchantOrderId()));
        if (Objects.nonNull(transactionStatusResDTO.getActionType()) && (Objects
            .equals(transactionStatusResDTO.getActionType().name(), ActionTypeEnum.CHANGE_PLAN.name()) || Objects
            .equals(transactionStatusResDTO.getActionType().name(), ActionTypeEnum.UPDATE_SUBSCRIPTION.name()))) {
            transactionStatusResDTO.setScreenType("SUBSCRIPTION");
        }
        log.info("=====Info vnptPayResponse======" + (Objects.nonNull(vnptPayResponse) ? vnptPayResponse.toString() : null));
        // Nếu trạng thái giao dịch khác PROCESSING
        if (!Objects.equals(OrderStatusEnum.PROCESSING.value,
            vnptPayResponse.getOrderStatus())) {
            log.info("Status Order: " + vnptPayResponse.getOrderStatus());
            transactionStatusResDTO.setTransactionStatus(Objects.equals("00", vnptPayResponse.getResponseCode()) ? 0 : 2);
            return true;
        }

        // Trạng thái là PROCESSING thì call api VNPT
        boolean isSuccessTransaction;
        ClueDTO clueDTO = subscriptionRepository.getMerchantByUserId(userAdminSme);
        if (Objects.nonNull(vnptPayResponse.getPaymentMethod()) && vnptPayResponse.getPaymentMethod().equals(PaymentMethodEnum.VNPTPAY_QR.name())) {
            // header
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setBearerAuth(clueDTO.getQrApiKey());
            // request
            QRTransactionCheckReqDTO rep = new QRTransactionCheckReqDTO();
            rep.setMerchantClientId(qrConfig.getMerchantClientId());
            rep.setMerchantCode(qrConfig.getMerchantCode());
            rep.setTerminalId(clueDTO.getQrTerminalId());
            rep.setBillNumber(vnptPayResponse.getMerchantOrderId());
            List<String> checksums = Arrays.asList(
                    rep.getMerchantClientId(),
                    rep.getMerchantCode(),
                    rep.getTerminalId(),
                    rep.getBillNumber(),
                    clueDTO.getQrSecretKey()
            );
            rep.setChecksum(DigestUtils.sha256Hex(Strings.join(checksums, '|')));
            String url = qrConfig.getInit_url().concat("/query_transaction_by_billNumber");
            QRTransactionCheckResDTO res = httpUtil.callRest(url, HttpMethod.POST, headers, rep, QRTransactionCheckResDTO.class);
            //
            int apiStatus = Objects.nonNull(res) &&
                    Objects.equals(SubscriptionConstant.VNPTPayQRResponseCode.SUCCESS, res.getTransactionStatus()) ?
                    YesNoEnum.NO.value : YesNoEnum.YES.value;
            isSuccessTransaction = Objects.equals(apiStatus, YesNoEnum.NO.value);
            //
            for (Subscription subscription : lstSubObj) {
                executiveProducerService.saveHistoryCallApiDHSX(url, rep, res, apiStatus, PartnerCallAPI.PAY, subscription.getId(), null);
            }
            if (!isSuccessTransaction) {
                transactionStatusResDTO.setTransactionStatus(Integer.valueOf(res.getTransactionStatus()));
                return true;
            }
            vnptPayResponse.setResponseCode(res.getResponseCode());
            vnptPayResponse.setDescription(res.getDescription());
            vnptPayResponse.setAmount(new BigDecimal(res.getAmount()));
            vnptPayResponse.setVnptpayTransactionId(res.getTransactionId());
            vnptPayResponse.setPayDate(LocalDateTime.parse(res.getPayDate(), DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS)));
            vnptPayResponse.setTransactionStatus(Integer.valueOf(res.getTransactionStatus()));
            vnptPayResponse.setSecureCode(res.getOrderCode());
        } else {
            log.info("=================Info request checkStatusTransaction: " + clueDTO);
            String merchantServiceId = clueDTO.getMerchantServiceId() == null ? null : String.valueOf(clueDTO.getMerchantServiceId());
            String secretKey = clueDTO.getPrivateKey();
            String baseUrl = clueDTO.getBaseUrl();
            String tokenVNPTPay = clueDTO.getApiKey();
            if (!ObjectUtils.allNotNull(merchantServiceId, secretKey, baseUrl, tokenVNPTPay, baseUrl)) {
                throw exceptionFactory.badRequest(MessageKeyConstant.MISS_DATA_TRANSACTION, Resources.TRANSACTION,
                        ErrorKey.DATA);
            }
            String url = baseUrl.concat(PaymentConstant.URL_VERIFY);
            log.info("=================url checkStatusTransaction: " + url);
            LocalDateTime currentDate = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS);
            String createdDate = currentDate.format(formatter);

            TradingResultDTO tradingResultDTO = new TradingResultDTO();
            tradingResultDTO.setAction(PaymentConstant.ACTION);
            tradingResultDTO.setVersion(PaymentConstant.VERSION);
            tradingResultDTO.setMerchantServiceId(merchantServiceId);
            tradingResultDTO.setMerchantOrderId(transactionStatusReqDTO.getMerchantOrderId());
            tradingResultDTO.setCreateDate(createdDate);
            List<String> actualArr = Arrays.asList(
                    tradingResultDTO.getAction(),
                    tradingResultDTO.getVersion(),
                    tradingResultDTO.getMerchantServiceId(),
                    tradingResultDTO.getMerchantOrderId(),
                    tradingResultDTO.getCreateDate(),
                    secretKey
            );
            String actualString = Strings.join(actualArr, '|');
//        Chữ ký bản tin sinh ra theo thuật toán SHA-256
            String actualEncode = DigestUtils.sha256Hex(actualString);
            tradingResultDTO.setSecureCode(actualEncode);
            log.info("Start call API VNPT transaction payment");
            log.info("==============Request: " + tradingResultDTO);
            TransactionStatusVnptResDTO statusVnptResDTO = restTemplate.postResponse(tokenVNPTPay, url, tradingResultDTO);
            log.info(
                    "=================Response checkStatusTransaction: " + (Objects.nonNull(statusVnptResDTO) ? statusVnptResDTO.toString() : null));

            // Lưu lịch sử call api sang VNPT Pay
            // apiStatus 0 là thành công
            int apiStatus = determineApiStatus(statusVnptResDTO);
            isSuccessTransaction = Objects.equals(apiStatus, YesNoEnum.NO.value);
//        executiveProducerService.saveHistoryCallApiDHSX(url, tradingResultDTO, statusVnptResDTO, apiStatus, PartnerCallAPI.PAY, subscription.getId());
            for (Subscription subscription : lstSubObj) {
                executiveProducerService
                        .saveHistoryCallApiDHSX(url, tradingResultDTO, statusVnptResDTO, apiStatus, PartnerCallAPI.PAY, subscription.getId(), null);
            }
             // Nếu trạng thái đang xử lý thì return True
            if (Objects.equals(VnptPayTransactionStatusEnum.PROCESSING.getValue(), Integer.valueOf(statusVnptResDTO.getTransactionStatus()))) {
                transactionStatusResDTO.setTransactionStatus(Integer.valueOf(statusVnptResDTO.getTransactionStatus()));
                return true;
            }
            vnptPayResponse.setResponseCode(statusVnptResDTO.getResponseCode());
            vnptPayResponse.setDescription(statusVnptResDTO.getDescription());
            vnptPayResponse.setAmount(BigDecimal.valueOf(statusVnptResDTO.getAmount()));
            vnptPayResponse.setLocale(statusVnptResDTO.getLocale());
            vnptPayResponse.setCurrencyCode(statusVnptResDTO.getCurrencyCode());
            vnptPayResponse.setVnptpayTransactionId(statusVnptResDTO.getVnptPayTransactionId());
            vnptPayResponse.setPaymentMethod(statusVnptResDTO.getPaymentMethod());
            vnptPayResponse.setAdditionalInfo(statusVnptResDTO.getAdditionalInfo());
            vnptPayResponse.setPayDate(LocalDateTime.parse(statusVnptResDTO.getPayDate(), formatter));
            vnptPayResponse.setTransactionStatus(Integer.valueOf(statusVnptResDTO.getTransactionStatus()));
            vnptPayResponse.setSecureCode(statusVnptResDTO.getSecureCode());
        }
        vnptPayResponse.setOrderStatus(OrderStatusEnum.PROCESSED.value);
        vnptPayResponseRepository.save(vnptPayResponse);
        log.info("=====Info vnptPayResponse======" + vnptPayResponse);

        boolean isUpdateSubscription = Objects.equals(actionType, ACTION_UPDATE);
        boolean isChangePlanSubscription = Objects.equals(actionType, ACTION_CHANGE_PLAN);

//        create info to callDHSXWhenSub
        Long userRegSubId = firstSubs.getUserId();
        User userRegSub = null;
        CustomerInformationResDTO customerInformationResDTO = null;
        Optional<User> userDB = userRepository.findByIdAndDeletedFlag(userRegSubId, DeletedFlag.NOT_YET_DELETED.getValue());
        if (userDB.isPresent()) {
            userRegSub = userDB.get();
            customerInformationResDTO = executiveProducerService.callApiGetInfoDHSXKD(null, userRegSub, null);
        }
        boolean isUpdateOldCreditNote = false;
        String cartCode = "";
        List<Subscription> lstSuccessSubscriptions = new ArrayList<>();
        boolean isDevice = false;
        for (Bills billing : billObjLst) {
            Long subscriptionsId = billing.getSubscriptionsId();
            Subscription subscription = idSubMap.get(subscriptionsId);
            if (subscription == null) {
                subscription = subscriptionRepository.findById(subscriptionsId)
                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SUBSCRIPTION, ErrorKey.ID, String.valueOf(subscriptionsId)));
            }
            SubscriptionRegisterInfo subscriptionRegisterInfo = new SubscriptionRegisterInfo();
            //Lấy change_subs để lưu lịch sử
            List<SubscriptionComboAddon> subscriptionComboAddonsOld = new ArrayList<>();
            if (Objects.nonNull(subscription.getComboPlanId())) {
                subscriptionComboAddonsOld = subscriptionComboAddonRepository.findBySubscriptionId(subscription.getId());
            }
            Bills billOld = billsRepository.findBillOldBySubscriptionId(subscription.getId()).orElse(null);

            BillStatusEnum billStatusEnum;
            Pricing pricingDHSX = null;
            ComboPlan comboPlanDHSX = null;
            if (Objects.nonNull(subscription.getPricingId())) {
                pricingDHSX = subscriptionService.checkExistPricing(subscription.getPricingId());
                updatePricingDHSX(subscription, pricingDHSX);
            } else if (Objects.nonNull(subscription.getComboPlanId())) {
                comboPlanDHSX = subscriptionService.validateComboPlan(subscription.getComboPlanId());
                isDevice = comboPlanRepository.checkDeviceInCombo(comboPlanDHSX.getId());
            } else if (Objects.nonNull(subscription.getVariantId())) {  // Chỉ mua thiết bị
                isDevice = true;
            }
            ServiceEntity serviceEntity =
                subscription.getServiceId() != null ? serviceRepository.findById(subscription.getServiceId()).orElse(null) : null;
            if (Objects.nonNull(serviceEntity)) {
                isDevice = Objects.equals(ServiceProductTypeEnum.DEVICE.value, serviceEntity.getProductType().value);
            }
            subscriptionRegisterInfo.getRegisterInfoDTO().setPricing(pricingDHSX);
            subscriptionRegisterInfo.getRegisterInfoDTO().setComboPlan(comboPlanDHSX);
            Integer pricingType = Objects.nonNull(pricingDHSX) ? pricingDHSX.getPricingType()
                : Objects.nonNull(comboPlanDHSX) ? comboPlanDHSX.getComboPlanType() : PricingTypeEnum.PREPAY.value;
            Subscription oldSubscription = new Subscription();
            Subscription newSubscription;
            Long billId = billing.getId();
            if (isSuccessTransaction) {
                subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
                // Đồng bộ trạng thái confirm_status giữa thuê bao chính và thuê bao được tặng
                subscriptionService.confirmGiveAwaySubscription(subscription.getId());

                List<Bills> billsList = billsRepository.findBySubscriptionsId(subscription.getId());
                if (!checkBillOutOfDate(billsList) && subscription.getStartedAt().compareTo(new Date()) <= 0) {
                    subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                }
                subscriptionRegisterInfo.setMainSubscription(subscription);
                subscriptionRegisterInfo.setMainBills(billing);
                subscription = subscriptionService.autoActivateSub(subscription, billsList);
                subscription.setPaymentMethod(vnptPayResponse.getPaymentMethod().equals(PaymentMethodEnum.VNPTPAY_QR.name()) ?
                        PaymentMethodEnum.VNPTPAY_QR.value : PaymentMethodEnum.VNPTPAY.value);
                Subscription saved = subscriptionRepository.save(subscription);
                // Lưu lịch sử thay đổi trạng thái thuê bao
                subscriptionHistoryService.saveStatusHistory(saved);
                Long oldQuantity = saved.getQuantity();
                billStatusEnum = BillStatusEnum.PAID;
                BeanUtils.copyProperties(saved, oldSubscription);
                //Nếu thanh toán trả trước
                if (Objects.equals(pricingType, PricingTypeEnum.PREPAY.value) || Objects.equals(pricingType, PricingTypeEnum.POSTPAID.value)) { // update cho cả trả sau khi
                    // Neu action type la Update
                    if (isUpdateSubscription) {
                        if (Objects.nonNull(subscription.getPricingId())) {
                            // Gọi method update những thay đổi của sub
                            newSubscription = subscriptionService.updatePricingByBatch(subscription.getId(),
                                ActionChangeSubEnum.NOW.value, null);
                            // Send ban tin kafka action type = UPDATE_SUBSCRIPTION
                            final Subscription result = newSubscription;
                            if (!StringUtils.isEmpty(token)) {
                                pricingRepository
                                    .findById(result.getPricingId())
                                    .ifPresent(p -> integrationService.transactionOneSME(token, result, p,
                                        IntegrationActionTypeEnum.UPDATE_SUBSCRIPTION, null, null, null, false, null)
                                    );
                            }
                            // Lưu lịch sử khi thay đổi số lượng gói dv chính
                            if (!Objects.equals(oldQuantity, subscription.getQuantity()) &&
                                    billing.getActionType().equals(ActionTypeEnum.UPDATE_SUBSCRIPTION.getValue())) {
                                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(),
                                    SubscriptionHistoryConstant.CHANGE_QUANTITY_PRICING, billId,
                                    SubscriptionHistoryConstant.ContentType.CHANGE_QUANTITY_PRICING);
                            }
                        } else if (Objects.nonNull(subscription.getComboPlanId())) {
                            newSubscription = subscriptionService.updateComboByBatch(subscription,
                                ActionChangeSubEnum.NOW.value, null);
                            final Subscription result = newSubscription;
                            comboPlanRepository.findById(result.getComboPlanId())
                                .ifPresent(c -> {
                                    if (!StringUtils.isEmpty(token)) {
                                        integrationService.transactionOneSMECombo(token, result, c,
                                            IntegrationActionTypeEnum.UPDATE_SUBSCRIPTION, null, null, null,
                                            null, null);
                                    }
                                });
                            //Lưu lịch sử addon
                            List<SubscriptionComboAddon> subscriptionComboAddonsNew = subscriptionComboAddonRepository
                                .findBySubscriptionId(newSubscription.getId());
                            comboService
                                .saveSubComboHistoryChangeAddon(newSubscription.getId(), subscriptionComboAddonsOld, subscriptionComboAddonsNew);
                        }
                    } else if (isChangePlanSubscription) {
                        //Nếuu action type đổi gói
                        if (Objects.nonNull(billOld) && Objects.nonNull(billOld.getPricingId())) {
                            // Send ban tin kafka action type = UPDATE_SUBSCRIPTION
                            Optional<Pricing> pricingOpt = pricingRepository
                                .findByIdAndDeletedFlag(billOld.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
                            if (!StringUtils.isEmpty(token)) {
                                Subscription result = subscriptionService.swapPricingByBatch(subscription,
                                    ActionChangeSubEnum.NOW.value, null);
                                if (pricingOpt.isPresent()) {
                                    Pricing pricingOld = pricingOpt.get();
                                    integrationService.transactionOneSME(token, result, pricingDHSX,
                                        IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null, false, null);

                                    // lưu lịch sử đổi gói
                                    String histContent = String
                                        .format(SubscriptionHistoryConstant.CHANGE_PRICING, pricingOld.getPricingName(),
                                            pricingDHSX.getPricingName());
                                    subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent, billId,
                                        SubscriptionHistoryConstant.ContentType.CHANGE_PRICING);
                                }
                            }

                        } else if (Objects.nonNull(billOld) && Objects.nonNull(billOld.getComboPlanId())) {
                            Subscription result = subscriptionService.swapComboPlanByBatch(subscription,
                                ActionChangeSubEnum.NOW.value, null);
                            Optional<ComboPlan> comboPlanOpt = comboPlanRepository.findById(billOld.getComboPlanId());
                            if (comboPlanOpt.isPresent() && !StringUtils.isEmpty(token)) {
                                ComboPlan comboPlanOld = comboPlanOpt.get();
                                integrationService.transactionOneSMECombo(token, result, comboPlanDHSX,
                                    IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null,
                                    null, null);
                                // lưu lịch sử đổi gói
                                String histContent = String
                                    .format(SubscriptionHistoryConstant.CHANGE_PRICING, comboPlanOld.getComboName(),
                                        comboPlanDHSX.getComboName());
                                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent,
                                    SubscriptionHistoryConstant.ContentType.CHANGE_PRICING);
                            }
                        }
                    } else {
                        //Nếu action type tạo sub
                        // call postback pending khi thay thanh toán thành công
                        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
                            if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) {
                                integrationService.sendPostbackAccesstrade(subscription, vnptPayResponse.getMerchantOrderId());
                                integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            } else if (Objects.equals(SubscriptionConstant.APINFO, subscription.getTrafficSource())) {
                                integrationService.sendPostbackApinfo(subscription, subscription.getTrafficId(), AccessTradeStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            } else {
                                integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(),
                                        MasOfferStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            }
                        }
                        // saveBatchKafkaAndBatchDHSXKD internal
                        if (Objects.nonNull(subscription.getCartCode()) || Objects.nonNull(subscription.getGroupCode())) {
                            saveBatchKafkaAndBatchDHSXKD(billId, StringUtil.replaceToken(token), vnptPayResponse, PaymentMethodEnum.VNPTPAY);
                        }

                        lstSuccessSubscriptions.add(subscription);
                    }
                    log.info("========== Start call API VNPT transaction payment Save Successful ==========");
                }
            } else {
                billStatusEnum = BillStatusEnum.FAILURE;
            }

            //Thực hiện lưu thông tin hóa đơn nếu thanh toán thành công
            BillStatusEnum finalBillStatusEnum = billStatusEnum;
            billing.setStatus(finalBillStatusEnum.value);
            if (isSuccessTransaction) {
                billing.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
            }
            //Ngày thanh toán sẽ được set khi thanh toán thành công
            if (Objects.equals(BillStatusEnum.PAID.name(), finalBillStatusEnum.name())) {
                billing.setPaymentDate(new Date());
                //update trạng thái credit note thành đã hoàn trả khi thanh toán thành công
                List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository
                    .getAllChangeCreditNoteByBillingId(billing.getId());
                /*Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteList = listCreditNote.stream()
                    .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue));
                creditNoteService
                    .createCreditNote(subscription.getId(), billing, creditNoteList, CreditNoteDetailStatusEnum.ADJUSTED, null,
                        isChangePlanSubscription);
                changeCreditNoteRepository.updateChangeCreditNoteStatusByBillingId(billing.getId(), YesNoEnum.YES.value);*/
                creditNoteService.createNewCreditNote(billsVnptRes, listCreditNote, subscription.getCreatedBy(), false);
                isUpdateOldCreditNote = true;
                cartCode = billsVnptRes.getCartCode();
            }
            //Nếu thanh toán trả trước
            if (Objects.equals(pricingType, PricingTypeEnum.PREPAY.value) || Objects.equals(pricingType, PricingTypeEnum.POSTPAID.value)) {
                Long vnptPayResponseId = vnptPayResponse.getId();
//                if (isSuccessTransaction && (Objects.equals(YesNoEnum.NO.value, vnptPayResponse.getIsCalledDhsxkd()) || Objects
//                    .isNull(vnptPayResponse.getIsCalledDhsxkd())) && (Objects.isNull(billing.getActionType())
//                    || Objects.equals(-1, billing.getActionType()))) {
//                    if (customerInformationResDTO != null) {
//                        executiveProducerService
//                            .callDHSXWhenSubInShoppingCart(subscription, pricingDHSX, comboPlanDHSX, userRegSub, customerInformationResDTO);
//                        log.info("===== shoping cart Check subId {}, code dhsxkd - verify5: {}", subscription.getId(),
//                            subscription.getSubCodeDHSXKD());
//                    }
//                    // send mail
//                    // sendMail(subscription, bills.get(), pricingDHSX, comboPlanDHSX, null);
//                    if (i == (billObjLst.size() - 1)) {
//                        vnptPayResponseRepository.updateIsCalledDhsxkd(vnptPayResponseId);
//                    }
//                }
//
//                // Nếu chưa call update sang dhsxkd
//                if (isSuccessTransaction && isUpdateSubscription
//                    && (Objects.equals(YesNoEnum.NO.value, vnptPayResponse.getIsCalledDhsxkd()) || Objects
//                    .isNull(vnptPayResponse.getIsCalledDhsxkd())) && Objects.nonNull(transactionStatusResDTO.getActionType())) {
//                    subscriptionOrderService.callDHSXUpdateSubSMEVNPTPay(subscription, null);
//                    if (i == (billObjLst.size() - 1)) {
//                        vnptPayResponseRepository.updateIsCalledDhsxkd(vnptPayResponseId);
//                    }
//                }
                        /*
                        Boolean sendBillMail = false;
                        if (billing.getSendMail() == null) {
                            billing.setSendMail(1);
                            sendBillMail = true;
                        }
                        // send ban du thao hoa don
                        billsRepository.save(billing);
                        if (sendBillMail) {
                            billsService.sendBillMail(billing.getId());
                        }
                         */
                couponItemService.used(subscription);
                if (Objects.equals(billing.getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value) && Objects
                    .equals(billing.getStatus(), BillStatusEnum.PAID.value)) {
                    creditNoteRepository.updateCreditNoteByBillingApply(billing.getId(), CreditNoteDetailStatusEnum.REFUNDED.code);
                }
                // =====================HiepNT Call API tiepnhan_yc_shop===================//
                List<OrderServiceReceive> orderServiceReceives = orderServiceReceiveRepository
                    .findBySubscriptionId(subscription.getId());
                if ((orderServiceReceives == null || orderServiceReceives.isEmpty())
                    && Objects.nonNull(subscription.getPricingId()) && Objects.nonNull(subscription.getServiceId())
                    && executiveProducerService.checkCallApiOrderServiceDHSXKD(subscription)) {
                    if (Objects.nonNull(subscription.getPricingId())) {
                        Pricing pricing = pricingRepository.findById(subscription.getPricingId()).orElse(null);
                        if (Objects.nonNull(pricing)) {
                            orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricing, null, null);
                        }
                    }
                    if (Objects.nonNull(subscription.getComboPlanId())) {
                        ComboPlan comboPlan = comboPlanRepository.findById(subscription.getComboPlanId()).orElse(null);
                        if (Objects.nonNull(comboPlan)) {
                            orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, null, comboPlan, null);
                        }
                    }
                }
            }
            Bills saveBill = billsRepository.save(billing);
            subscriptionRegisterInfo.setMainBills(saveBill);
            subscriptionRegisterInfoList.add(subscriptionRegisterInfo);
            boolean allowCallApiOrderService = subscriptionValidateService.checkServiceTypeAndPricingCodeIsLong(subscription);
            if (allowCallApiOrderService) {
                orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricingDHSX, comboPlanDHSX, null);
            }
        }
        if (Boolean.TRUE.equals(isUpdateOldCreditNote)) {
            creditNoteService.updateOldCreditNotesWithCartCode(cartCode);
        }
        // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
        inventoryService.onSubscriptionRegistered(lstSuccessSubscriptions);
        // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
        String subCartCode = lstSuccessSubscriptions.isEmpty() ? "" : lstSuccessSubscriptions.get(0).getCartCode();
        Long createdBy = lstSuccessSubscriptions.isEmpty() ? -1L : lstSuccessSubscriptions.get(0).getCreatedBy();
        lstSuccessSubscriptions.stream().map(Subscription::getQuotationId).filter(Objects::nonNull).distinct()
            .forEach(quotationId -> quotationService.changeStatus(Collections.singleton(quotationId), QuotationStatusEnum.ORDER,
                PortalType.UNSET, new ChangeStatusMetadataDTO(subCartCode, createdBy)));

        // Lưu log thanh toán onl qua shopping cart - Áp dụng cho oneSME
        if (isDevice) {

            // lịch sử đơn order
            String histContentCart = SubscriptionHistoryConstant.ORDER_SUCCESS;
            Integer histTypeCart = ContentType.ORDER_DEVICE;
            subscriptionHistoryService.addSubscriptionHistory(subCartCode, histContentCart, histTypeCart);

            // lưu lịch sử thanh toán thành công
            String content = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
            Integer type = ContentType.PAYMENT_SUCCESS;
            subscriptionHistoryService.addSubscriptionHistory(subCartCode, content, type);

            // log đơn hàng đang đóng gói
            subscriptionHistoryService.addSubscriptionHistory(subCartCode, SubscriptionHistoryConstant.PACKING_SUBSCRIPTION,
                ContentType.PACKING_SUBSCRIPTION);
        }
        return false;
    }

    // Check API status
    private int determineApiStatus(TransactionStatusVnptResDTO statusVnptResDTO) {
        if (Objects.isNull(statusVnptResDTO)) {
            return StatusCallAPIEnum.FAIL.getStatus();
        }

        boolean isSuccessfulTransaction = Objects.equals(
            VnptPayTransactionStatusEnum.SUCCESS.getValue(),
            Integer.valueOf(statusVnptResDTO.getTransactionStatus())
        );

        return isSuccessfulTransaction ? YesNoEnum.NO.value : YesNoEnum.YES.value;
    }

    public void sendEmailAndNotify(VNPTPayResponse vnptPayResponse, List<SubscriptionRegisterInfo> inputSubscription, BigDecimal totalAmount) {
        // send mail and noti đăng ký thành công
        log.info(" start sendEmailAndNotify " + Thread.currentThread().getId() + ": " + Thread.currentThread().getName());
        if (PortalType.SME.getType() == vnptPayResponse.getPortalType()) {
            subscriptionNotificationService
                .sendListNotifySubForSmeDevOrAdmin(inputSubscription,
                    MailParams.SME, vnptPayResponse.getBuyNow(), totalAmount);
        } else {
            subscriptionNotificationService
                .sendListNotifySubForSmeDevOrAdmin(inputSubscription,
                    null, vnptPayResponse.getBuyNow(), totalAmount);
        }
        // send mail thanh toan thành công
        if (PortalType.SME.getType() == vnptPayResponse.getPortalType()) {
            sendMailSuccessPayment(inputSubscription, MailParams.SME, totalAmount, null);
        } else if (PortalType.ADMIN.getType() == vnptPayResponse.getPortalType()) {
            sendMailSuccessPayment(inputSubscription, null, totalAmount, null);
        }
        log.info(" end sendEmailAndNotify " + Thread.currentThread().getId() + ": " + Thread.currentThread().getName());
    }

    private void updatePricingDHSX(Subscription subscription, Pricing pricingDHSX) {
        if (subscription.getPricingMultiPlanId() != null) {
            Optional<PricingMultiPlan> pricingMultiPlanOptional = pricingMultiPlanRepository.findById(subscription.getPricingMultiPlanId());
            if (pricingMultiPlanOptional.isPresent()) {
                PricingMultiPlan pricingMultiPlan = pricingMultiPlanOptional.get();
                pricingDHSX.setNumberOfCycles(pricingMultiPlan.getNumberOfCycle());
                pricingDHSX.setPricingPlan(pricingMultiPlan.getPricingPlan());
                pricingDHSX.setPaymentCycle(Objects.nonNull(pricingMultiPlan.getPaymentCycle()) ? pricingMultiPlan.getPaymentCycle().intValue() : null);
                pricingDHSX.setCycleType(pricingMultiPlan.getCircleType());
                pricingDHSX.setCurrencyId(pricingMultiPlan.getCurrencyId());
                pricingDHSX.setPrice(pricingMultiPlan.getPrice());
                pricingDHSX.setTrialType(Objects.nonNull(pricingMultiPlan.getTrialType()) ? pricingMultiPlan.getTrialType().intValue() : null);
                pricingDHSX.setNumberOfTrial(pricingMultiPlan.getNumberOfTrial());
                pricingDHSX.setFreeQuantity(Objects.nonNull(pricingMultiPlan.getFreeQuantity()) ? Long.valueOf(pricingMultiPlan.getFreeQuantity()) : null);
                pricingDHSX.setEstimateQuantity(pricingMultiPlan.getEstimateQuantity());
            }
        }
    }

    @Transactional
    @Override
    public TransactionStatusResDTO checkStatusTransaction(
            TransactionStatusReqDTO transactionStatusReqDTO, String token, VNPTPayResponse vnptPayResponse) {
        log.info("=========== Payment checkStatusTransaction check code isDeploy =========: " + new Date());
        log.info("Start method checkStatusTransaction");

        TransactionStatusResDTO transactionStatusResDTO = vnptPayTransactionStatusMapper
            .toDto(vnptPayResponse);

        /* nếu là thanh toán với giỏ hàng */
        if (vnptPayResponse.getSubscriptionId() == null && vnptPayResponse.getLstSubId() != null) {
            automationRuleService.autoAssignListNewRecordObject(
                vnptPayResponse.getLstSubId().stream().map(Long::valueOf).collect(Collectors.toList()), CrmObjectTypeEnum.SUBSCRIPTION);
            return checkStatusTransactionShoppingCart(transactionStatusReqDTO, token, vnptPayResponse, transactionStatusResDTO);
        }

        Subscription subscription = subscriptionService.findByIdAndDeletedFlag(vnptPayResponse.getSubscriptionId(),
            DeletedFlag.NOT_YET_DELETED.getValue());
        Long subId = subscription.getId();
        transactionStatusResDTO.setIsON(subscriptionService.checkIsONSub(subId));
        transactionStatusResDTO.setQuotationId(subscription.getQuotationId());
        log.info("===== Check subId {}, code dhsxkd - verify1: {}", subId, subscription.getSubCodeDHSXKD());
        //Lấy change_subs để lưu lịch sử
        List<SubscriptionComboAddon> subscriptionComboAddonsOld = new ArrayList<>();
        if (Objects.nonNull(subscription.getComboPlanId())){
            subscriptionComboAddonsOld = subscriptionComboAddonRepository.findBySubscriptionId(subId);
        }

        transactionStatusResDTO.setPricingMultiPlanId(subscription.getPricingMultiPlanId());
        transactionStatusResDTO.setServiceId(subscription.getServiceId());
        transactionStatusResDTO.setSubscriptionCode(subscription.getSubCode());
        transactionStatusResDTO.setSubscriptionId(subId);
        int portalType = UNSET;
        try {
            portalType = Integer.parseInt(transactionStatusResDTO.getPortalType());
        } catch (Exception e) {
            portalType = PortalType.SME.getType();
        }
        switch (portalType) {
            case ADMIN:
                transactionStatusResDTO.setPortalType(PortalType.ADMIN.name());
                break;
            case DEV:
                transactionStatusResDTO.setPortalType(PortalType.DEV.name());
                break;
            default:
                transactionStatusResDTO.setPortalType(PortalType.SME.name());
                break;
        }
        Integer actionType = billsRepository.findById(vnptPayResponse.getBillingId())
            .map(Bills::getActionType)
            .orElse(OTHER);
        boolean isReActiveSub = Objects.equals(actionType, ACTION_RE_ACTIVE_OUT_OF_DATE) || Objects.equals(actionType, ACTION_RE_ACTIVE_IN_PAYMENT);

        transactionStatusResDTO.setAction(actionType);
        Bills billsVnptRes = billsRepository.findById(vnptPayResponse.getBillingId())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.BILL, ErrorKey.ID, String.valueOf(vnptPayResponse.getBillingId())));
        if (Objects.nonNull(vnptPayResponse.getBillingId())) {
            transactionStatusResDTO.setBillingId(billsVnptRes.getId());
            transactionStatusResDTO.setActionType(
                (Objects.nonNull(billsVnptRes) && Objects.nonNull(billsVnptRes.getActionType()))
                    ? ActionTypeEnum.valueOf(billsVnptRes.getActionType()) : ActionTypeEnum.CREATE_SUBSCRIPTION);
        }
        if (Objects.nonNull(subscription.getComboPlanId()) && Objects.isNull(subscription.getPricingId())) {
            transactionStatusResDTO.setType(SubsTypeEnum.COMBO);

            transactionStatusResDTO.setComboPlanId(subscription.getComboPlanId());
            comboPlanRepository.findById(subscription.getComboPlanId()).ifPresent(comboPlan -> {
                comboRepository.findById(comboPlan.getComboId()).ifPresent(combo -> {
                    transactionStatusResDTO.setComboId(combo.getId());
                    transactionStatusResDTO.setComboName(combo.getComboName());
                });
            });

        } else if (Objects.isNull(subscription.getComboPlanId()) && Objects.nonNull(subscription.getServiceId())) {

            if (Objects.nonNull(subscription.getServiceGroupId())) {
                transactionStatusResDTO.setType(SubsTypeEnum.GROUP_SERVICE);
            } else {
                transactionStatusResDTO.setType(SubsTypeEnum.SERVICE);
            }

            serviceRepository.findById(subscription.getServiceId()).ifPresent(serviceEntity -> {
                if (Objects.nonNull(serviceEntity.getServiceOwner())) {
                    transactionStatusResDTO.setServiceOwner(ServiceTypeEnum.valueOf(serviceEntity.getServiceOwner()).toString());
                }
                transactionStatusResDTO.setServiceName(serviceEntity.getServiceName());
            });
        }

        transactionStatusResDTO.setPlanId(subscription.getPricingId());
        transactionStatusResDTO.setMerchantOrderId(transactionStatusReqDTO.getMerchantOrderId());
        transactionStatusResDTO.setScreenType(vnptPayResponseRepository.getScreenType(transactionStatusReqDTO.getMerchantOrderId()));
        if (Objects.nonNull(transactionStatusResDTO.getActionType()) && (Objects
            .equals(transactionStatusResDTO.getActionType().name(), ActionTypeEnum.CHANGE_PLAN.name()) || Objects
            .equals(transactionStatusResDTO.getActionType().name(), ActionTypeEnum.UPDATE_SUBSCRIPTION.name()))) {
            transactionStatusResDTO.setScreenType("SUBSCRIPTION");
        }

        log.info("=====Info vnptPayResponse======" + (Objects.nonNull(vnptPayResponse) ? vnptPayResponse.toString() : null));

//        Nếu trạng thái giao dịch khác PROCESSING
        if (!vnptPayResponseService.isPaymentProcessing(transactionStatusReqDTO.getMerchantOrderId())) {
            log.info("===== Check subId {}, code dhsxkd - verify2: {}", subId, subscription.getSubCodeDHSXKD());
            log.info("Status Order: " + vnptPayResponse.getOrderStatus());
            transactionStatusResDTO.setTransactionStatus(Objects.equals("00", vnptPayResponse.getResponseCode()) ? 0 : 2);
            if (Objects.equals(transactionStatusResDTO.getTransactionStatus(), 0)) {
                // Lưu lịch sử khi xác nhận thanh toán
                String histContent = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
                Integer histType = ContentType.PAYMENT_SUCCESS;
                subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent, histType);
                // log đơn hàng đang đóng gói
                if (ObjectUtil.getOrDefault(subscription.getOs3rdStatus(), -1L).intValue() == StatusOrderEnum.PREPARING.status) {
                    subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), SubscriptionHistoryConstant.PACKING_SUBSCRIPTION,
                        ContentType.PACKING_SUBSCRIPTION);
                }
            }
            // Gửi mail km
            sendMailPromotion(subscription.getUserId(), subscription.getId());
            return transactionStatusResDTO;
        }
        
        TransactionLog transactionLogFinal = new TransactionLog();
        // Lưu thông tin transaction
        Optional<TransactionLog> transactionLog = transactionLogRepository.findByVnptPayResponseId(vnptPayResponse.getId());
        
        if(!transactionLog.isPresent()) {
            // TH repay tạo ra vnpt_pay_response mới thì update lại transaction log với vnpt_pay_response_id mới này
            Optional<TransactionLog> transactionLogCurrent = transactionLogRepository.findFirstBySubscriptionIdOrderByIdDesc(subscription.getId());
            if(transactionLogCurrent.isPresent()) {
                transactionLogCurrent.get().setVnptPayResponseId(vnptPayResponse.getId());
                transactionLogRepository.save(transactionLogCurrent.get());
                transactionLogFinal = transactionLogCurrent.get();
            } else {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, vnptPayError, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.TRANSACTION_LOG, ErrorKey.VNPT_PAY.VNPT_PAY_RESPONSE_ID, MessageKeyConstant.NOT_FOUND);
            }
        } else {
            transactionLogFinal = transactionLog.get();
        }
        CommonActivityLogInfoDTO activityLogInfoDTO = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, transactionLogFinal);
        log.info("===== init activityLog with actionType {} and transaction_id {} ", activityLogInfoDTO.getActionType(), transactionLogFinal.getId());
        ActivityLog activityLog = activityLogService.commonActivityLogData(ActivityCodeEnum.PAY_CHECK_TRANSACTION_RESULT, activityLogInfoDTO);
        // Trạng thái là PROCESSING thì call api VNPT
        Integer apiStatus; // 0 là thành công
        ClueDTO clueDTO = subscriptionDetailService.getMerchantInfo(subscription);
        if (Objects.nonNull(vnptPayResponse.getPaymentMethod()) && vnptPayResponse.getPaymentMethod().equals(PaymentMethodEnum.VNPTPAY_QR.name())) {
            // header
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setBearerAuth(clueDTO.getQrApiKey());
            // request
            QRTransactionCheckReqDTO rep = new QRTransactionCheckReqDTO();
            rep.setMerchantClientId(qrConfig.getMerchantClientId());
            rep.setMerchantCode(qrConfig.getMerchantCode());
            rep.setTerminalId(clueDTO.getQrTerminalId());
            rep.setBillNumber(vnptPayResponse.getMerchantOrderId());
            List<String> checksums = Arrays.asList(
                    rep.getMerchantClientId(),
                    rep.getMerchantCode(),
                    rep.getTerminalId(),
                    rep.getBillNumber(),
                    clueDTO.getQrSecretKey()
            );
            rep.setChecksum(DigestUtils.sha256Hex(Strings.join(checksums, '|')));
            String url = qrConfig.getInit_url().concat("/query_transaction_by_billNumber");
            QRTransactionCheckResDTO res = httpUtil.callRest(url, HttpMethod.POST, headers, rep, QRTransactionCheckResDTO.class);
            //
            apiStatus = Objects.nonNull(res) &&
                    Objects.equals(SubscriptionConstant.VNPTPayQRResponseCode.SUCCESS, res.getTransactionStatus()) ?
                    YesNoEnum.NO.value : YesNoEnum.YES.value;
            //
            activityLog.updateResponseReq(
                    rep, res, tokenVNPTPay,
                    Objects.equals(apiStatus, YesNoEnum.NO.value) ? TransactionLogStatusEnum.SUCCESS : TransactionLogStatusEnum.FAIL,
                    Objects.nonNull(res) ? res.getTransactionStatus() : null,
                    Objects.nonNull(res) ? res.getDescription() : null,
                    activityLogInfoDTO);
            executiveProducerService.saveHistoryCallApiDHSX(url, rep, res, apiStatus, PartnerCallAPI.PAY, subId, activityLogInfoDTO);

            log.info("===== Check subId {}, code dhsxkd - verify3: {}", subId, subscription.getSubCodeDHSXKD());
            if (Objects.equals(apiStatus, YesNoEnum.YES.value)) {
                transactionStatusResDTO.setTransactionStatus(Integer.valueOf(res.getTransactionStatus()));
                log.info("===== Check subId {}, code dhsxkd - verify4: {}", subId, subscription.getSubCodeDHSXKD());
                return transactionStatusResDTO;
            }
            // update vnptPayResponse
            vnptPayResponse.setResponseCode(res.getResponseCode());
            vnptPayResponse.setDescription(res.getDescription());
            vnptPayResponse.setAmount(new BigDecimal(res.getAmount()));
            vnptPayResponse.setVnptpayTransactionId(res.getTransactionId());
            vnptPayResponse.setPayDate(LocalDateTime.parse(res.getPayDate(), DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS)));
            vnptPayResponse.setTransactionStatus(Integer.valueOf(res.getTransactionStatus()));
            vnptPayResponse.setSecureCode(res.getOrderCode());
            vnptPayResponse.setMerchantServiceId(res.getMerchantClientId());
        } else {
            log.info("=================Info request checkStatusTransaction: " + clueDTO);
            String merchantServiceId = clueDTO.getMerchantServiceId() == null ? null : String.valueOf(clueDTO.getMerchantServiceId());
            String secretKey = clueDTO.getPrivateKey();
            String baseUrl = clueDTO.getBaseUrl();
            String tokenVNPTPay = clueDTO.getApiKey();
            if (!ObjectUtils.allNotNull(merchantServiceId, secretKey, baseUrl, tokenVNPTPay)) {
                throw exceptionFactory.badRequest(MessageKeyConstant.MISS_DATA_TRANSACTION, Resources.TRANSACTION,
                        ErrorKey.DATA);
            }
            String url = baseUrl.concat(PaymentConstant.URL_VERIFY);
            log.info("=================url checkStatusTransaction: " + url);
            LocalDateTime currentDate = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_YYYY_MMDD_HHMMSS);
            String createdDate = currentDate.format(formatter);

            TradingResultDTO tradingResultDTO = new TradingResultDTO();
            tradingResultDTO.setAction(PaymentConstant.ACTION);
            tradingResultDTO.setVersion(PaymentConstant.VERSION);
            tradingResultDTO.setMerchantServiceId(merchantServiceId);
            tradingResultDTO.setMerchantOrderId(transactionStatusReqDTO.getMerchantOrderId());
            tradingResultDTO.setCreateDate(createdDate);
            List<String> actualArr = Arrays.asList(
                    tradingResultDTO.getAction(),
                    tradingResultDTO.getVersion(),
                    tradingResultDTO.getMerchantServiceId(),
                    tradingResultDTO.getMerchantOrderId(),
                    tradingResultDTO.getCreateDate(),
                    secretKey
            );
            tradingResultDTO.setSecureCode(DigestUtils.sha256Hex(Strings.join(actualArr, '|')));
            log.info("Start call API VNPT transaction payment");
            log.info("==============Request: " + tradingResultDTO);
            TransactionStatusVnptResDTO statusVnptResDTO = restTemplate.postResponse(tokenVNPTPay, url, tradingResultDTO);
            log.info("=================Response checkStatusTransaction: " + (Objects.nonNull(statusVnptResDTO) ? statusVnptResDTO.toString() : null));
            //
            apiStatus = Objects.nonNull(statusVnptResDTO) &&
                    Objects.equals(VnptPayTransactionStatusEnum.SUCCESS.getValue(), Integer.valueOf(statusVnptResDTO.getTransactionStatus())) ?
                    YesNoEnum.NO.value : (Objects.nonNull(statusVnptResDTO) ? YesNoEnum.YES.value : StatusCallAPIEnum.FAIL.getStatus());
            //
            activityLog.updateResponseReq(
                    tradingResultDTO, statusVnptResDTO, tokenVNPTPay,
                    Objects.equals(apiStatus, YesNoEnum.NO.value) ? TransactionLogStatusEnum.SUCCESS : TransactionLogStatusEnum.FAIL,
                    Objects.nonNull(statusVnptResDTO) ? statusVnptResDTO.getTransactionStatus() : null,
                    Objects.nonNull(statusVnptResDTO) ? statusVnptResDTO.getDescription() : null,
                    activityLogInfoDTO);
            executiveProducerService.saveHistoryCallApiDHSX(url, tradingResultDTO, statusVnptResDTO, apiStatus, PartnerCallAPI.PAY,
                    subId, activityLogInfoDTO);

            log.info("===== Check subId {}, code dhsxkd - verify3: {}", subId, subscription.getSubCodeDHSXKD());
            if (Objects.equals(apiStatus, YesNoEnum.YES.value)) {
                transactionStatusResDTO.setTransactionStatus(Integer.valueOf(statusVnptResDTO.getTransactionStatus()));
                log.info("===== Check subId {}, code dhsxkd - verify4: {}", subId, subscription.getSubCodeDHSXKD());
                return transactionStatusResDTO;
            }
            // update vnptPayResponse
            vnptPayResponse.setResponseCode(statusVnptResDTO.getResponseCode());
            vnptPayResponse.setDescription(statusVnptResDTO.getDescription());
            vnptPayResponse.setAmount(BigDecimal.valueOf(statusVnptResDTO.getAmount()));
            vnptPayResponse.setLocale(statusVnptResDTO.getLocale());
            vnptPayResponse.setCurrencyCode(statusVnptResDTO.getCurrencyCode());
            vnptPayResponse.setVnptpayTransactionId(statusVnptResDTO.getVnptPayTransactionId());
            vnptPayResponse.setPaymentMethod(statusVnptResDTO.getPaymentMethod());
            vnptPayResponse.setAdditionalInfo(statusVnptResDTO.getAdditionalInfo());
            vnptPayResponse.setPayDate(LocalDateTime.parse(statusVnptResDTO.getPayDate(), formatter));
            vnptPayResponse.setTransactionStatus(Integer.valueOf(statusVnptResDTO.getTransactionStatus()));
            vnptPayResponse.setSecureCode(statusVnptResDTO.getSecureCode());
        }
        vnptPayResponse.setOrderStatus(OrderStatusEnum.PROCESSED.value);
        // set tgian phản hồi
        activityLog.setResponseAt(LocalDateTime.now());
        if (Objects.nonNull(activityLogInfoDTO.getActivityLog())) {
            activityLogInfoDTO.getActivityLog().setResponseAt(LocalDateTime.now());
        }
        // bổ sung token lưu lại trong bảng này, hỗ trợ tích hợp IDC Portal
        if (AuthUtil.getCurrentUser() != null && AuthUtil.getCurrentUser().getBearerToken() != null) {
            vnptPayResponse.setAccessToken(AuthUtil.getCurrentUser().getBearerToken());
        }
        vnptPayResponseRepository.save(vnptPayResponse);
        log.info("=====Info vnptPayResponse======" + (Objects.nonNull(vnptPayResponse) ? vnptPayResponse.toString() : null));
        // update sub, bill, ....
        boolean isUpdateSubscription = Objects.equals(actionType, ACTION_UPDATE);
        boolean isChangePlanSubscription = Objects.equals(actionType, ACTION_CHANGE_PLAN);
        Bills billOld = billsRepository.findBillOldBySubscriptionId(vnptPayResponse.getSubscriptionId()).orElse(null);
        BillStatusEnum billStatusEnum;
        Pricing pricingDHSX = null;
        ComboPlan comboPlanDHSX = null;
        boolean isDevice = false;
        if (Objects.nonNull(subscription.getPricingId())) {
            pricingDHSX = subscriptionService.checkExistPricing(billsVnptRes.getPricingId());
        } else if (Objects.nonNull(subscription.getComboPlanId())) {
            comboPlanDHSX = subscriptionService.validateComboPlan(billsVnptRes.getComboPlanId());
            isDevice = comboPlanRepository.checkDeviceInCombo(comboPlanDHSX.getId());
        } else if (Objects.nonNull(subscription.getVariantId())) {  // Chỉ mua thiết bị
            isDevice = true;
        }
        ServiceEntity serviceEntity =
            subscription.getServiceId() != null ? serviceRepository.findById(subscription.getServiceId()).orElse(null) : null;
        if (Objects.nonNull(serviceEntity)) {
            isDevice = Objects.equals(ServiceProductTypeEnum.DEVICE.value, serviceEntity.getProductType().value);
        }

        Long paymentCyclePmp = null;
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            paymentCyclePmp = pricingMultiPlanRepository.getPaymentCycle(subscription.getPricingMultiPlanId());
        }

        Integer pricingType = Objects.nonNull(pricingDHSX) ? pricingDHSX.getPricingType()
            : Objects.nonNull(comboPlanDHSX) ? comboPlanDHSX.getComboPlanType() : PricingTypeEnum.PREPAY.value;
        Integer paymentCycle = null;
        Integer cycleTypePricing = null;
        if (isReActiveSub) {
            paymentCycle = Objects.nonNull(paymentCyclePmp) ? Math.toIntExact(paymentCyclePmp)
                    : Objects.nonNull(pricingDHSX) ? pricingDHSX.getPaymentCycle() : null;
            cycleTypePricing = Objects.nonNull(pricingDHSX) ? pricingDHSX.getCycleType()
                    : Objects.nonNull(comboPlanDHSX) ? comboPlanDHSX.getCycleType() : null;
        }

        boolean isSuccessTransaction = Objects.equals(apiStatus, YesNoEnum.NO.value);
        Subscription oldSubscription = new Subscription();
        Subscription newSubscription = new Subscription();
        boolean isCalledTrans = false;
        Long oldQuantity = null;
        boolean changePlanEndOfCylcePayNow = false;
        if (isSuccessTransaction) {
            log.info("===== Status transaction VNPT Payment: " + "1");
            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
            // Đồng bộ trạng thái confirm_status giữa thuê bao chính và thuê bao được tặng
            if (!isReActiveSub) {
                subscriptionService.confirmGiveAwaySubscription(subId);
                List<Bills> billsList = billsRepository.findBySubscriptionsId(subId);
                if (!checkBillOutOfDate(billsList) && subscription.getStartedAt().compareTo(new Date()) <= 0
                    && !Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.NOT_EXTEND.value)) {
                    subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                }
                subscription = subscriptionService.autoActivateSub(subscription, billsList);
                subscription.setPaymentMethod(vnptPayResponse.getPaymentMethod().equals(PaymentMethodEnum.VNPTPAY_QR.name()) ?
                        PaymentMethodEnum.VNPTPAY_QR.value : PaymentMethodEnum.VNPTPAY.value);
                Subscription saved = subscriptionRepository.save(subscription);
                subscriptionHistoryService.saveStatusHistory(saved);
                // Lưu lịch sử thay đổi trạng thái thuê bao
                oldQuantity = saved.getQuantity();
                log.info("=====Info Subscription updated when checkStatusTransaction======" + (Objects.nonNull(saved) ? saved.toString() : null));
                BeanUtils.copyProperties(saved, oldSubscription);
            }

            billStatusEnum = BillStatusEnum.PAID;
            //Nếu thanh toán trả trước
            if (Objects.equals(pricingType, PricingTypeEnum.PREPAY.value) || (!Objects.equals(pricingType, PricingTypeEnum.PREPAY.value) && isReActiveSub)) {
                // nếu là re-active
                if (isReActiveSub) {
                    // lưu lịch sử thay đổi thuê bao
                    String hisContent = SubscriptionHistoryConstant.SUB_REACTIVE;
                    int hisType = ContentType.SUB_REACTIVE;
                    subscriptionHistoryService.addSubscriptionHistoryNoAuth(subId, hisContent, hisType, token);
                    Subscription subscriptionReactive = reactiveSub(subscription, cycleTypePricing, vnptPayResponse,token, pricingDHSX, portalType, actionType, paymentCycle);
                    subscriptionRepository.save(subscriptionReactive);
                } else if (isUpdateSubscription) { // Neu action type la Update
                    if (Objects.nonNull(subscription.getPricingId())) {
                        //================= XÓA THÔNG TIN ADDON, COUPON CŨ ============================//
                        subscriptionAddonsRepository.deleteAllBySubscriptionId(subId);
                        // Gọi method update những thay đổi của sub
                        newSubscription = subscriptionService.updatePricingByBatch(subId,
                            ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                        // Send ban tin kafka action type = UPDATE_SUBSCRIPTION
                        final Subscription result = newSubscription;
                        if (!StringUtils.isEmpty(token)) {
                            pricingRepository
                                .findById(result.getPricingId())
                                .ifPresent(p -> integrationService.transactionOneSME(token, result, p,
                                    IntegrationActionTypeEnum.UPDATE_SUBSCRIPTION, null, null, null, false,
                                    activityLogInfoDTO)
                                );
                        }
                        // Lưu lịch sử khi thay đổi số lượng gói dv chính
                        if (!Objects.equals(oldQuantity, subscription.getQuantity()) &&
                                billsVnptRes.getActionType().equals(ActionTypeEnum.UPDATE_SUBSCRIPTION.getValue())) {
                            subscriptionHistoryService.addSubscriptionHistory(subId,
                                SubscriptionHistoryConstant.CHANGE_QUANTITY_PRICING, billsVnptRes.getId(),
                                SubscriptionHistoryConstant.ContentType.CHANGE_QUANTITY_PRICING);
                        }
                    } else if (Objects.nonNull(subscription.getComboPlanId())) {
                         newSubscription = subscriptionService.updateComboByBatch(subscription,
                            ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                        final Subscription result = newSubscription;
                        comboPlanRepository.findById(result.getComboPlanId())
                            .ifPresent(c -> {
                                if (!StringUtils.isEmpty(token)) {
                                    integrationService.transactionOneSMECombo(token, result, c,
                                        IntegrationActionTypeEnum.UPDATE_SUBSCRIPTION, null, null, null,
                                        null, activityLogInfoDTO);
                                }
                            });
                        //Lưu lịch sử addon
                        List<SubscriptionComboAddon> subscriptionComboAddonsNew = subscriptionComboAddonRepository
                            .findBySubscriptionId(newSubscription.getId());
                        comboService.saveSubComboHistoryChangeAddon(newSubscription.getId(), subscriptionComboAddonsOld, subscriptionComboAddonsNew);
                    }
                } else if (isChangePlanSubscription) {
                    //Nếuu action type đổi gói
                    if (Objects.nonNull(billOld) && Objects.nonNull(billOld.getPricingId())) {
                        // Send ban tin kafka action type = UPDATE_SUBSCRIPTION
                        Optional<Pricing> pricingOpt =  pricingRepository
                            .findByIdAndDeletedFlag(billOld.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
                        if (!StringUtils.isEmpty(token)) {
                            Subscription result = subscriptionService.swapPricingByBatch(subscription,
                                ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                            if (pricingOpt.isPresent()) {
                                Pricing pricingOld = pricingOpt.get();
                                IntegrationResponseDTO response = integrationService.transactionOneSME(token, result, pricingDHSX,
                                    IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null, false,
                                    activityLogInfoDTO);
                                // Case đổi từ gói c cuối chu kỳ mới đổi gói và thanh toán ngay sang gói mới
                                if (pricingOld.getChangePricingPaymentTime().equals(ChangePricingPaymentTimeEnum.NOW.getValue())
                                    && pricingOld.getChangePricingDate().equals(ChangePricingPaymentTimeEnum.END_OF_PERIOD.getValue())) {
                                    // Hóa đơn đổi gói cũ chuyển thành hủy
                                    changePlanEndOfCylcePayNow = true;
                                    Bills bill = billsRepository.getSubscriptionsIdAndCurrentCycle(subscription.getId(), subscription.getCurrentCycle());
                                    if (Objects.nonNull(bill)) {
                                        List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository.getAllChangeCreditNoteByBillingId(bill.getId());
                                        if(!CollectionUtils.isEmpty(listCreditNote)){
                                            bill.setStatus(BillStatusEnum.DELETED.value);
                                        }
                                        billsRepository.save(bill);
                                        creditNoteRepository.deleteCreditNoteByBillingId(bill.getId());
                                    }
                                }
                                // lưu lịch sử đổi gói
                                String histContent = String
                                    .format(SubscriptionHistoryConstant.CHANGE_PRICING, pricingOld.getPricingName(), pricingDHSX.getPricingName());
                                addSubscriptionHistoryBatch(subscription.getId(), histContent, billsVnptRes.getId(),
                                        SubscriptionHistoryConstant.ContentType.CHANGE_PRICING, vnptPayResponse);
                                isCalledTrans = !Objects.isNull(response) && response.isCallKafka();
                            }
                        }

                    } else if (Objects.nonNull(billOld) && Objects.nonNull(billOld.getComboPlanId())) {
                        Subscription result = subscriptionService.swapComboPlanByBatch(subscription,
                            ActionChangeSubEnum.NOW.value, activityLogInfoDTO);
                        Optional<ComboPlan> comboPlanOpt = comboPlanRepository.findById(billOld.getComboPlanId());
                        if(comboPlanOpt.isPresent() && !StringUtils.isEmpty(token)){
                            ComboPlan comboPlanOld = comboPlanOpt.get();
                            IntegrationResponseDTO response = integrationService.transactionOneSMECombo(token, result, comboPlanDHSX,
                                IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null,
                                null, activityLogInfoDTO);
                            // lưu lịch sử đổi gói
                            String histContent = String
                                .format(SubscriptionHistoryConstant.CHANGE_PRICING, comboPlanOld.getComboName(), comboPlanDHSX.getComboName());
                            subscriptionHistoryService.addSubscriptionHistory(subId, histContent,
                                SubscriptionHistoryConstant.ContentType.CHANGE_PRICING);
                            isCalledTrans = !Objects.isNull(response) && response.isCallKafka();
                        }
                    }
                } else if (Objects.equals(transactionLogFinal.getTransactionCode(), TransactionCodeEnum.RENEW.value)) {
                    // Luu lich su
                    Long userIdCreateBill = getUserIdCreateBill(billsVnptRes.getCreatedBy());
                    subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), SubscriptionHistoryConstant.RENEWAL_SUBSCRIPTION,
                        SubscriptionHistoryConstant.ContentType.RENEWAL_SUBSCRIPTION, userIdCreateBill);
                    if (!StringUtils.isEmpty(token)) {
                        if (Objects.nonNull(subscription.getPricingId())) {
                            Subscription subscription1 = subscription;
                            pricingRepository
                                .findById(subscription.getPricingId())
                                .ifPresent(p -> {
                                        Subscription finalSubscription1 = renewSubscriptionByBatch(p, subId, subscription1,
                                            ActionChangeSubEnum.NOW.value, activityLogInfoDTO, billsVnptRes);
                                        integrationService.transactionOneSME(token, finalSubscription1, p,
                                            IntegrationActionTypeEnum.RENEW_SUBSCRIPTION, null, null, null, false,
                                            activityLogInfoDTO);
                                    }
                                );
                        } else if ((Objects.nonNull(subscription.getComboPlanId()))) {
                            Subscription subscription2 = subscription;
                            comboPlanRepository.findById(subscription.getComboPlanId())
                                .ifPresent(c -> integrationService.transactionOneSMECombo(token, subscription2, c,
                                    IntegrationActionTypeEnum.RENEW_SUBSCRIPTION, null, null, null,
                                    null, activityLogInfoDTO));
                        }
                    }
                } else {
                    //Nếu action type tạo sub
                    // call postback pending khi thay thanh toán thành công
                    if (!isDevice) {
                        if (StringUtils.isNotBlank(subscription.getTrafficId())) {
                            if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) {
                                integrationService.sendPostbackAccesstrade(subscription, vnptPayResponse.getMerchantOrderId());
                                integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            } else if (Objects.equals(SubscriptionConstant.APINFO, subscription.getTrafficSource())) {
                                integrationService.sendPostbackApinfo(subscription, subscription.getTrafficId(),
                                    AccessTradeStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            } else {
                                integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(),
                                    MasOfferStatusEnum.PENDING.value, null);
                                subscription.setIsCallMasoffer(YesNoEnum.YES.value);
                            }
                        }
                    } else {
                        subscription.setOs3rdStatus((long) StatusOrderEnum.PREPARING.status);
                        subscriptionRepository.save(subscription);
                    }

                    // NgoNC: Gọi sang hệ thống Inventory để cập nhật đơn hàng thành công
                    inventoryService.onSubscriptionRegistered(Collections.singletonList(subscription));
                    // NgoNC: Chuyển trạng thái báo giá cho đơn hàng thành công
                    if (subscription.getQuotationId() != null) {
                        quotationService.changeStatus(Collections.singleton(subscription.getQuotationId()), QuotationStatusEnum.ORDER,
                            PortalType.UNSET, new ChangeStatusMetadataDTO(subscription.getCode(),subscription.getCreatedBy()));
                    }
                }
                log.info("========== Start call API VNPT transaction payment Save Successful ==========");
            }
            // Gửi thông báo khuyến mãi sản phẩm
            sendMailPromotion(subscription.getUserId(), subscription.getId());
        } else if (!isReActiveSub) {
            billStatusEnum = BillStatusEnum.FAILURE;
        } else {
            billStatusEnum = BillStatusEnum.FAILURE;
        }

        //Thực hiện lưu thông tin hóa đơn nếu thanh toán thành công
        if (Objects.nonNull(vnptPayResponse.getBillingId()) && !isReActiveSub) {
            BillStatusEnum finalBillStatusEnum = billStatusEnum;
            Optional<Bills> bills = billsRepository.findById(vnptPayResponse.getBillingId());
            if (bills.isPresent()) {
                transactionStatusResDTO.setBillingId(bills.get().getId());
                bills.get().setStatus(finalBillStatusEnum.value);
                if (isSuccessTransaction) {
                    bills.get().setConfirmStatus(SubscriptionConfirmStatusEnum.CONFIRM.value);
                }
                //Ngày thanh toán sẽ được set khi thanh toán thành công
                if (Objects.equals(BillStatusEnum.PAID.name(), finalBillStatusEnum.name())) {
                    bills.get().setPaymentDate(new Date());
                    //update trạng thái credit note thành đã hoàn trả khi thanh toán thành công
                    List<CreditNoteCalculateDTO> listCreditNote = creditNoteRepository.getAllChangeCreditNoteByBillingId(bills.get().getId());
                    Map<BigDecimal, List<CreditNoteCalculateDTO>> creditNoteList = listCreditNote.stream()
                        .collect(Collectors.groupingBy(CreditNoteCalculateDTO::getTaxValue));
                    creditNoteService.createCreditNote(subId, bills.get(), creditNoteList, CreditNoteDetailStatusEnum.ADJUSTED, null,
                        isChangePlanSubscription);
                    if(changePlanEndOfCylcePayNow){
                        List<CreditNoteCalculateDTO> usedCreditNote = createUsedCreditNote(listCreditNote);
                        creditNoteService.createNewCreditNote(billsVnptRes, usedCreditNote, subscription.getCreatedBy(), true);
                    }
                    changeCreditNoteRepository.updateChangeCreditNoteStatusByBillingId(bills.get().getId(), YesNoEnum.YES.value);
                    if (Objects.nonNull(bills.get().getAffiliateLinkCode()) && (actionType == -1 || Objects.isNull(actionType))) {
                        affiliateUserService.commissionCalculate(bills.get(), null, subscription.getPricingId(), subscription.getComboPlanId());
                    }
                }
                //Nếu thanh toán trả trước
                if (Objects.equals(pricingType, PricingTypeEnum.PREPAY.value)) {
                    if (isSuccessTransaction && (Objects.equals(YesNoEnum.NO.value, vnptPayResponse.getIsCalledDhsxkd()) || Objects
                        .isNull(vnptPayResponse.getIsCalledDhsxkd())) && (Objects.isNull(bills.get().getActionType())
                        || Objects.equals(-1, bills.get().getActionType())) && !Objects
                        .equals(transactionLogFinal.getTransactionCode(), TransactionCodeEnum.RENEW.value)) {
                        executiveProducerService.callDHSXWhenSub(subscription, subscription.getUserId(), pricingDHSX, comboPlanDHSX, activityLogInfoDTO);
                        log.info("===== Check subId {}, code dhsxkd - verify5: {}", subId, subscription.getSubCodeDHSXKD());
                        // send mail
                        sendMail(subscription, bills.get(), pricingDHSX, comboPlanDHSX, null);
                        vnptPayResponseRepository.updateIsCalledDhsxkd(vnptPayResponse.getId());
                    }

                    // Nếu chưa call update sang dhsxkd
                    if (isSuccessTransaction && isUpdateSubscription
                        && (Objects.equals(YesNoEnum.NO.value, vnptPayResponse.getIsCalledDhsxkd()) || Objects
                        .isNull(vnptPayResponse.getIsCalledDhsxkd())) && Objects.nonNull(transactionStatusResDTO.getActionType())) {
                        subscriptionOrderService.callDHSXUpdateSubSMEVNPTPay(subscription, activityLogInfoDTO);
                        vnptPayResponseRepository.updateIsCalledDhsxkd(vnptPayResponse.getId());
                    }
	                Boolean sendBillMail = false;
	                if(bills.get().getSendMail() == null){
	                    bills.get().setSendMail(1);
	                    sendBillMail = true;
	                }
	                billsRepository.save(bills.get());
	                if(sendBillMail) billsService.sendBillMail(bills.get().getId());
                    couponItemService.used(subscription);
                    if (Objects.equals(bills.get().getConfirmStatus(), SubscriptionConfirmStatusEnum.CONFIRM.value) && Objects
                        .equals(bills.get().getStatus(), BillStatusEnum.PAID.value)) {
                        creditNoteRepository.updateCreditNoteByBillingApply(bills.get().getId(), CreditNoteDetailStatusEnum.REFUNDED.code);
                    }
                    // =====================HiepNT Call API tiepnhan_yc_shop===================//
                    List<OrderServiceReceive> orderServiceReceives = orderServiceReceiveRepository.findBySubscriptionId(subId);
                    if (executiveProducerService.checkCallApiOrderServiceDHSXKD(subscription)) {
                        if ((orderServiceReceives == null || orderServiceReceives.isEmpty())
                            && Objects.nonNull(subscription.getPricingId()) && Objects.nonNull(subscription.getServiceId())) {
                            if (Objects.nonNull(subscription.getPricingId())) {
                                Pricing pricing = pricingRepository.findById(subscription.getPricingId()).orElse(null);
                                if (Objects.nonNull(pricing)) {
                                    orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, pricing, null, null);
                                }
                            }
                            if (Objects.nonNull(subscription.getComboPlanId())) {
                                ComboPlan comboPlan = comboPlanRepository.findById(subscription.getComboPlanId()).orElse(null);
                                if (Objects.nonNull(comboPlan)) {
                                    orderServiceReceiveService.createOrderServiceReceiveByApiDHSXKD(subscription, null, comboPlan, null);
                                }
                            }
                        }
                        if (Objects.nonNull(subscription.getComboPlanId()) && !isUpdateSubscription && !isChangePlanSubscription) {
                            // Combo OS tạo mới TT qua pay giữ nguyên Đang chờ
                            subscription.setStatus(SubscriptionStatusEnum.FUTURE.value);
                            subscriptionRepository.save(subscription);
                        }
                    }
                    // Neu action type không phải là update hoặc đổi gói -> tạo gói và thanh toán thành công
                    if (!isUpdateSubscription && !isChangePlanSubscription && isSuccessTransaction) {
                        try {
                            Subscription finalSubscription = (Subscription) subscription.clone();
                            transactionStatusResDTO.setSubscription(finalSubscription);
                            if (Objects.nonNull(finalSubscription.getPricingId())) {
                                transactionStatusResDTO.setCallTrans(SUBS_PRICING);
                            } else if (Objects.nonNull(finalSubscription.getComboPlanId())) {
                                transactionStatusResDTO.setCallTrans(SUBS_COMBO);
                            }
                        } catch (CloneNotSupportedException e) {
                            log.error(e.getMessage());
                        }
                    }
                }
            }
        }

        // gui mail khi tao sub pricing
        //TODO trường hợp qua pay xem lại luồng thông báo và gửi mail
        if (Objects.equals(pricingType, PricingTypeEnum.PREPAY.value) && !isReActiveSub &&
            isSuccessTransaction && Objects.equals(SubscriptionStatusEnum.ACTIVE.value, subscription.getStatus())) {
            if (Objects.nonNull(subscription.getPricingId())) {
//                if (PortalType.SME.equals(vnptPayResponse.getPortalType())) {
//                    subNotification.sendNotifySubForSmeDevOrAdmin(subscription, pricingDHSX, null, MailParams.SME);
//                } else {
//                    subNotification.sendNotifySubForSmeDevOrAdmin(subscription, pricingDHSX, null, null);
//                }
                subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, pricingDHSX, null, MailParams.SME);
            } else {
//                if (PortalType.SME.equals(vnptPayResponse.getPortalType())) {
//                    subNotification.sendNotifySubForSmeDevOrAdmin(subscription, pricingDHSX, null, MailParams.SME);
//                } else {
//                    subNotification.sendNotifySubForSmeDevOrAdmin(subscription, pricingDHSX, null, null);
//                }
                subscriptionNotificationService.sendNotifySubForSmeDevOrAdmin(subscription, null, comboPlanDHSX, MailParams.SME);
            }
        }

        // Nếu đổi gói thành công thì update calledTrans của sub
        if (isCalledTrans) {
            subscriptionRepository.updateCalledTrans(subId, 1);
        }

        // gửi mail khi reActive lại thuê bao
        log.info("======= End call API VNPT transaction payment ======");
        transactionStatusResDTO.setStatus(VnptPayTransactionStatusEnum.valueOf(vnptPayResponse.getTransactionStatus()));
        transactionStatusResDTO.setPricingMultiPlanId(subscription.getPricingMultiPlanId());
        transactionStatusResDTO.setTransactionStatus(vnptPayResponse.getTransactionStatus());
        transactionStatusResDTO.setTransactionLog(transactionLogFinal);
        transactionStatusResDTO.setActivityLogInfoDTO(activityLogInfoDTO);

        // Log lịch sử thanh toán khi verify thành công
        if (Objects.equals(apiStatus, 0)) {
            String histContent = SubscriptionHistoryConstant.PAYMENT_SUCCESS;
            Integer histType = ContentType.PAYMENT_SUCCESS;
            subscriptionHistoryService.addSubscriptionHistory(subscription.getId(), histContent, histType);
        }

        log.info("===== Check subId {}, code dhsxkd - verify6: {}", subId, subscription.getSubCodeDHSXKD());
        return transactionStatusResDTO;
    }

    @Override
    public void sendMailPromotion(Long userId, Long subscriptionId) {
        try {
            User userDB = userRepository.findById(userId).orElse(null);
            if (Objects.isNull(userDB)) {
                return;
            }
            Long subId = ObjectUtil.getOrDefault(subscriptionId, -1L);
            BigDecimal totalAmountPretax = subscriptionRepository.getTotalAmountPretaxBySubId(subId);
            Set<Long> lstCouponApplyId;
            if (Objects.isNull(subscriptionId)) {
                lstCouponApplyId = couponRepository.findLstCouponPromotionIdByObjectId(userId, userDB.getCustomerType(),
                    CrmObjectTypeEnum.USER.getValue(), subId, BigDecimal.ZERO);
            } else {
                lstCouponApplyId = couponRepository.findLstCouponPromotionIdByObjectId(userId, userDB.getCustomerType(),
                    CrmObjectTypeEnum.USER.getValue(), subscriptionId, totalAmountPretax);
            }
            // Lấy pricingId, multiPlanId, variantId theo version mới nhất đã duyệt
            List<IGetPromotionDetailDTO> promotionIDTOs = couponRepository.getListPromotionDetailByCouponIdIn(userDB.getCustomerType(),
                lstCouponApplyId);
            Map<Long, List<IGetPromotionDetailDTO>> mapListPromotionDetailByCouponId = promotionIDTOs.stream()
                .collect(Collectors.groupingBy(IGetPromotionDetailDTO::getCouponId));
            validateCouponPromotion(userDB.getId(), mapListPromotionDetailByCouponId);
            log.info("========== send mail promotion ==========");
            // Nếu khách hàng được áp dụng dụng ít nhất 1 chương trình khuyến mại thì gửi mail khuyến mại
            if (!mapListPromotionDetailByCouponId.isEmpty()) {
                ActionNotificationParamDTO paramEmailDTO;
                if (Objects.equals(CustomerTypeEnum.PERSONAL.getValue(), userDB.getCustomerType())) {
                    String linkPromotion = webHost + String.format("/personal?emailPromotion=true&lstSubId=%s", subId);
                    paramEmailDTO = new KM16(mapListPromotionDetailByCouponId, linkPromotion, userDB).getParam();
                } else {
                    String promotionEndpoint = (Objects.equals(CustomerTypeEnum.HOUSE_HOLD.getValue(), userDB.getCustomerType()) ?
                        "/house-hold?emailPromotion=true&lstSubId=%s" : "/enterprise?emailPromotion=true&lstSubId=%s");
                    String linkPromotion = webHost + String.format(promotionEndpoint, subId);
                    paramEmailDTO = new CP16(mapListPromotionDetailByCouponId, linkPromotion, userDB).getParam();
                }
                actionNotificationService.sendMailByBizfly(paramEmailDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("========== send mail promotion failed ==========");
        }
    }

    private void validateCouponPromotion(Long parentId, Map<Long, List<IGetPromotionDetailDTO>> mapListPromotionDetailByCouponId) {
        Set<Long> lstCouponIdNotValid  = new HashSet<>();
        for (Map.Entry<Long, List<IGetPromotionDetailDTO>> entry : mapListPromotionDetailByCouponId.entrySet()) {
            Long couponId = entry.getKey();
            IGetPromotionDetailDTO couponDetail = entry.getValue().get(0);
            //Số lần coupon này đã đc áp dụng
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(couponId);
            //Số lần khách hàng này sử dụng coupon này -- Số lần áp dụng ứng với số subscriptions áp dụng CTKM
            Long companyUsedCoupon = subscriptionRepository.countNumberOfSubscriptionTheCompanyUsedCoupon(parentId , couponId);
            Date now = new Date();
            //Vì date tính từ 00h của ngày nên ngày kết thúc phải tính sang ngày hôm sau
            Date endDate = Objects.isNull(couponDetail.getEndDate()) ? new Date("01/01/3000") : Date
                .from(Instant.ofEpochMilli(couponDetail.getEndDate().getTime()).atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1)
                    .atStartOfDay(ZoneId.systemDefault()).toInstant());
            //Check xem số lượng sử dụng CTKM này đã được áp dụng hết số lần áp dụng chưa
            if ((Objects.nonNull(couponDetail.getMaxUsed()) && subscriptionUsedCoupon >= couponDetail.getMaxUsed())
                //Check ngày subscription có trong khoảng thời gian mà CTKM này có hiệu lực không
                || (Objects.nonNull(couponDetail.getStartDate()) && now.before(couponDetail.getStartDate()))
                || (Objects.nonNull(couponDetail.getEndDate()) && now.after(endDate))
                //Check xem khách hàng này đã sử dụng coupon này bao nhiêu lần
                || (Objects.nonNull(couponDetail.getMaximumPromotion()) && companyUsedCoupon >= couponDetail.getMaximumPromotion())) {
                lstCouponIdNotValid.add(couponId);
            }
        }
        for (Long couponId : lstCouponIdNotValid) {
            mapListPromotionDetailByCouponId.remove(couponId);
        }
    }

    /**
     * tinh toán tiền cho creditNote đã dùng.
     */
    public List<CreditNoteCalculateDTO> createUsedCreditNote(List<CreditNoteCalculateDTO> listCreditNote){
        List<CreditNoteCalculateDTO> result = new ArrayList<>();
        for (CreditNoteCalculateDTO item : listCreditNote) {
            CreditNoteCalculateDTO creditNoteCalculateDTO = new CreditNoteCalculateDTO();
            BeanUtils.copyProperties(item, creditNoteCalculateDTO);
            BigDecimal remainingAmountRefund = creditNoteCalculateDTO.getRemainingAmountRefund();
            BigDecimal amountRefund = creditNoteCalculateDTO.getAmountRefund();
            if (creditNoteCalculateDTO.getId() == null && remainingAmountRefund != null && remainingAmountRefund.compareTo(amountRefund) != 0) {
                BigDecimal usedMoney = amountRefund.subtract(remainingAmountRefund);
                if (usedMoney.compareTo(BigDecimal.ZERO) > 0) {
                    creditNoteCalculateDTO.setAmountRefund(usedMoney);
                    result.add(creditNoteCalculateDTO);
                }
            }
        }
        return result;
    }

    /**
     * update thông tin vào bảng subscription khi qua pay thành công
     */
    @Transactional
    protected Subscription renewSubscriptionByBatch(Pricing pricing, Long subscriptionId, Subscription subscription, Integer actionChangeSubEnum,
        CommonActivityLogInfoDTO activityLogInfoDTO, Bills bills) {
        ChangeSubscription changeSubscription = changeSubscriptionRepository.findBySubscriptionIdAndStatusAndAction(subscriptionId,StatusEnum.INACTIVE.value, actionChangeSubEnum);
        Integer cycleType = pricing.getCycleType(), paymentCycle = pricing.getPaymentCycle();
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            PricingMultiPlan pricingMultiPlan = subscription.getPricingMultiPlanPricing();
            cycleType = pricingMultiPlan.getCircleType();
            paymentCycle = pricingMultiPlan.getPaymentCycle().intValue();
        }
        if(Objects.nonNull(changeSubscription)) {
            subscription.setModifiedAt(new Date());
            subscription.setConfirmStatus(StatusEnum.ACTIVE.value);
            subscription.setNumberOfCycles(changeSubscription.getNumberOfCycles());
            subscription.setNumberOfCyclesDefault(changeSubscription.getNumberOfCycles());
            BigDecimal nextTotalAmount = bills.getNextTotalAmount();
            if(Objects.nonNull(nextTotalAmount)){
                subscription.setNextPaymentAmount(nextTotalAmount);
            }
            Date renewDate = DateUtil.toDate(LocalDate.now());
            Date newExpired;
            Integer currentCycle = subscription.getCurrentCycle();
            if (Objects.isNull(changeSubscription.getNumberOfCycles())
                || changeSubscription.getNumberOfCycles() == -1
                || Objects.isNull(subscription.getExpiredTime())) {
                newExpired = null;
            } else {
                newExpired = subscriptionCalculateService.caculateNewExpriredTime(subscription.getExpiredTime(),
                    CycleTypeEnum.valueOf(cycleType), changeSubscription.getCycleQuantityRenew(), paymentCycle);
            }

            if (Objects.nonNull(changeSubscription.getCycleQuantityRenew())) {
                Integer cycleQuantity = changeSubscription.getCycleQuantityRenew();
//                Bills newestBillOfSub = billsRepository.findNewestBillOfSub(subscription.getId());
//                Integer actionType = newestBillOfSub.getActionType();
                int currentCycleRenewNew = 1 + cycleQuantity;
//                boolean lastTimeIsRenewSub = actionType != null && (actionType.equals(ActionTypeEnum.RENEW_SUBSCRIPTION.getValue()));
                // TODO: update: khi chỉnh sửa sub -> gia hạn vẫn lấy currentCycleRenew, đổi gói đã chuyển về null
                if (Objects.nonNull(subscription.getCurrentCycleRenew())) { // lần gần nhất là gia hạn; subscription.getCurrentCycleRenew() => khác null
                    currentCycleRenewNew = subscription.getCurrentCycleRenew() + cycleQuantity;
                }
                subscription.setCurrentCycleRenew(currentCycleRenewNew);
                if (Objects.nonNull(subscription.getExpiredTime()) && subscription.getExpiredTime().compareTo(renewDate) < 0) {
                    subscription.setCurrentCycleRenew(currentCycleRenewNew + 1);
                    subscription.setCurrentCycle(currentCycle + 1);
                } else if (Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.NOT_EXTEND.value)) {
                    subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                    subscription.setCurrentCycle(currentCycle + 1);
                    subscription.setCurrentCycleRenew(cycleQuantity);
                    subscription.setStartedAt(new Date());
                }
            } else if (Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.NOT_EXTEND.value)) {
                subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                subscription.setCurrentCycle(currentCycle + 1);
                subscription.setStartedAt(new Date());
            }
            subscription.setExpiredTime(newExpired);
            subscription.setStartCurrentCycle(changeSubscription.getStartCurrentCycle());
            subscription.setEndCurrentCycle(changeSubscription.getEndCurrentCycle());
            subscription.setCurrentPaymentDate(changeSubscription.getCurrentPaymentDate());
            subscription.setNextPaymentTime(changeSubscription.getNextPaymentTime());
            subscription.setTypeReactive(null);
            subscription.setAwaitingCancel(null);
            subscription.setChangeDate(null); // cần set về null đối với sub đổi gói cuối chu kỳ trước đó
            subscription.setChangeStatus(null);    // cần set về null  đối với sub đổi gói cuối chu kỳ trước đó
            subscription.setEndCurrentCycleNew(
                DateUtil.toDate(
                    DateUtil.calculateCycleDate(
                        DateUtil.toDate(DateUtil.convertDateToLocalDateTime(changeSubscription.getEndCurrentCycle())),
                        paymentCycle,
                        CycleTypeEnum.valueOf(cycleType),
                        false,
                        subscription.getCurrentCycleRenew() - 1)
                )
            );
            if (Objects.nonNull(changeSubscription.getPaymentMethod())) {
                subscription.setPaymentMethod(changeSubscription.getPaymentMethod());
            }
            if (changeSubscription.getSwapNewDate() != null) {
                subscription.setStartedAtSwap(changeSubscription.getSwapNewDate());
            }
            subscription.setCurrentCycleNonExtend(changeSubscription.getCurrentCycleNonExtend());
            changeSubscription.setStatus(StatusEnum.ACTIVE.value);
            changeSubscriptionRepository.save(changeSubscription);

            if (Objects.nonNull(activityLogInfoDTO)) {
                activityLogInfoDTO.setIsCalledDHSX(true);
            }
            subscriptionRepository.save(subscription);

            // gửi email assignee khi SME gia hạn
            if (Objects.nonNull(subscription.getAssigneeId())) {
                User customer = userRepository.findUserById(subscription.getUserId()).orElse(null);
                Integer finalPaymentCycle = paymentCycle;
                Integer finalCycleType = cycleType;
                Long userIdRenew = changeSubscription.getCreatedBy();
                User renewUser = userService.findUserById(userIdRenew);
                userRepository.findByIdAndDeletedFlag(subscription.getAssigneeId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .ifPresent(assignee -> actionNotificationService.send(new SCD05(assignee, customer, subscription, finalPaymentCycle,
                        finalCycleType, renewUser.getFullName())));
            }
        }
            return subscription;
    }

    private Subscription reactiveSub(Subscription subscription, Integer cycleTypePricing, VNPTPayResponse vnptPayResponse, String token, Pricing pricingDHSX, int portalType, Integer actionType, Integer paymentCycle) {
        log.info("=========== reactiveSub start =========: "+ subscription);
        boolean reactiveFeature = Boolean.TRUE;
        Date currentDateReactive = DateUtil.toDate(LocalDate.now());
        if (Objects.nonNull(subscription.getReactiveDate()) && subscription.getReactiveDate().compareTo(currentDateReactive) > 0) {
            reactiveFeature = Boolean.FALSE;
            subscription.setReactiveStatus(1);
        }
        Integer cycleType = Objects.isNull(subscription.getCycleType()) ? cycleTypePricing : subscription.getCycleType();
        if (Objects.equals(portalType, SME)) {
            subscription.setStartChargeAt(new Date());
            if (pricingDHSX.getTypeActiveInPaymentType() == 1 && (subscription.getCurrentCycle() < subscription.getNumberOfCycles())) {
                // ko sử dụng chu kỳ cũ, nếu là chu kỳ cuối thì không tăng chu kỳ hiện tại
                subscription.setCurrentCycle(subscription.getCurrentCycle() + 1); // chu kỳ sử dụng
                // nếu sub đã đổi gói
                if (Objects.equals(subscription.getIsSwap(), SWAP)) {
                    subscription.setCurrentCycleSwap(Objects.nonNull(subscription.getCurrentCycleSwap()) ? subscription.getCurrentCycleSwap() + 1 : null);
                }
            } else { // cập nhật ngày thanh toán tiếp theo
                subscription.setNextPaymentTime(
                        DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getCurrentPaymentDate(),
                                paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                2)));
            }

        }

        boolean isPaymentRequest = Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == 0;
        if (reactiveFeature) {
            subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
            // ngày kết thúc sử dụng
            if (!Objects.isNull(subscription.getNumberOfCycles()) && subscription.getNumberOfCycles() != -1L && reactiveFeature) {
                LocalDate newExpiredTime = DateUtil
                        .calculateCycleDate(Objects.equals(portalType, SME) ? new Date() : subscription.getReactiveDate(), paymentCycle,
                                CycleTypeEnum.valueOf(subscription.getCycleType()), true,
                                Objects.equals(portalType, SME) ? subscription.getNumberOfCycles() - subscription.getCurrentCycle() + 1
                                        : subscription.getNumberOfCycles() - subscription.getCurrentCycleReactive() +1);
                subscription.setExpiredTime(DateUtil.toDate(newExpiredTime));
            }
            // ngày yêu cầu thanh toán
            if (pricingDHSX.getPricingType() == PricingTypeEnum.PREPAY.value) {
                subscription.setStartChargeAt(new Date());
            } else {
                subscription.setStartChargeAt(DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getCurrentPaymentDate(),
                        paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                        1)));
            }
            // Thuê bao cấu hình Thay đổi chu kỳ thanh toán mới được bắt đầu từ ngày kích hoạt lại
            if ((ChangeContinueEnum.CHANGE.value.equals(pricingDHSX.getTypeActiveInPaymentType()) && Objects.equals(actionType, ACTION_RE_ACTIVE_IN_PAYMENT))
                    || Objects.equals(actionType, ACTION_RE_ACTIVE_OUT_OF_DATE)) {
                subscription.setStartedAt(new Date());
                subscription.setStartCurrentCycle(new Date()); // ngày bắt đầu sd chu kỳ mới
                // ngày kết thúc chu kỳ mới
                LocalDate newEndCurrentCycle = DateUtil
                        .calculateCycleDate(new Date(), paymentCycle,
                                CycleTypeEnum.valueOf(cycleType), true, 1);
                subscription.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));

                // ngày thanh toán chu kỳ hiện tại
                subscription.setCurrentPaymentDate(new Date());

                // ngày thanh toán chu kỳ tiếp theo
                subscription.setNextPaymentTime(
                        DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getCurrentPaymentDate(),
                                paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                1)));

            }

            if (portalType != SME) {
                if (Objects.nonNull(subscription.getReactiveDate()) && subscription.getReactiveDate().getTime() > new Date().getTime()) {
                    subscription.setStatus(SubscriptionStatusEnum.CANCELED.value);
                }
                if (subscription.getReactiveDate() != null) {
                    subscription.setStartedAt(subscription.getReactiveDate());
                }
                if (subscription.getCurrentCycleReactive() != null) {
                    subscription.setCurrentCycle(subscription.getCurrentCycleReactive());
                } else if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == ChangeContinueEnum.CHANGE.value){
                    subscription.setCurrentCycle(subscription.getCurrentCycle() + 1); // chu kỳ sử dụng
                }
                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() != 0) { // ko sử dụng chu kỳ cũ
                    subscription.setStartCurrentCycle(subscription.getReactiveDate()); // ngày bắt đầu sd chu kỳ mới
                    // ngày kết thúc chu kỳ mới
                    LocalDate newEndCurrentCycle = DateUtil
                            .calculateCycleDate(subscription.getReactiveDate(), paymentCycle,
                                    CycleTypeEnum.valueOf(cycleType), true, 1);
                    subscription.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));
                }

                // ngày thanh toán chu kỳ hiện tại
                subscription.setCurrentPaymentDate(new Date());

                if (isPaymentRequest) {
                    // ngày thanh toán chu kỳ tiếp theo khi sd chu ky thanh toan cu
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    2)));
                } else {
                    // ngày thanh toán chu kỳ tiếp theo
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    1)));
                }
            }

            // Lưu lịch sử thay đổi trạng thái thuê bao ( gọi sang SPDV)
            if (!StringUtils.isEmpty(token)) {
                integrationService.transactionOneSME(token, subscription, pricingDHSX,
                        IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION, null, null, null, false, null);
            }

            // Call API update Subscription DHSXKD
            if (executiveProducerService.checkCallApiDHSXKD(subscription)) {
                executiveProducerService.callApiUpdateSubDHSXKD(subscriptionRepository.getProvinceIdByCreateBy(subscription.getUserId()),
                        subscription, 1, subscription.getSubscriptionContractId(), null);
            }
        }

        if (pricingDHSX.getPricingType() == PricingTypeEnum.PREPAY.value) {
            Optional<Bills> bills = billsRepository.findById(vnptPayResponse.getBillingId());
            log.info("=========== reactiveSub start bill =========: "+ bills.get());
            bills.get().setStatus(BillStatusEnum.PAID.value);
            if (Objects.equals(portalType, PortalType.SME)) {
                if (pricingDHSX.getTypeActiveInPaymentType() == 1) { // chu kỳ thanh toán mới
                    bills.get().setCurrentCycle(subscription.getCurrentCycle());
                } else {
                    bills.get().setCurrentCycle(subscription.getCurrentCycle() + 1);
                }
            } else if (reactiveFeature){
                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == ChangeContinueEnum.CHANGE.value) {
                    bills.get().setCurrentCycle(subscription.getCurrentCycle());
                } else {
                    bills.get().setCurrentCycle(subscription.getCurrentCycle() + 1);
                }
            } else {
                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == ChangeContinueEnum.CHANGE.value) {
                    bills.get().setCurrentCycle(subscription.getCurrentCycle() + 1);
                } else {
                    bills.get().setCurrentCycle(subscription.getCurrentCycle() + 2);
                }
            }
            bills.get().setPaymentDate(new Date());
            if (!isPaymentRequest) {
                bills.get().setEndDate(subscription.getEndCurrentCycle());
            }

            billsRepository.save(bills.get());
            log.info("=========== reactiveSub end bill =========: "+ bills.get());
        } else if (actionType == ACTION_RE_ACTIVE_OUT_OF_DATE) {
            Optional<Bills> bills = billsRepository.findById(vnptPayResponse.getBillingId());
            log.info("=========== reactiveSub start bill =========: "+ bills.get());
            bills.get().setStatus(BillStatusEnum.INIT.value);
            bills.get().setCurrentCycle(subscription.getCurrentCycle());
            bills.get().setPaymentDate(new Date());
            bills.get().setEndDate(subscription.getEndCurrentCycle());
            billsRepository.save(bills.get());

            Bills billOFD = billsRepository.findBillOutOfDate(subscription.getId());
            if (billOFD != null) {
                billOFD.setStatus(BillStatusEnum.PAID.value);
                billOFD.setEndDate(subscription.getEndCurrentCycle());
                billsRepository.save(billOFD);
            }
            log.info("=========== reactiveSub end bill =========: "+ bills.get());
        }
        log.info("======= send mail reactive ======");

        Long userIdLogin = getCurrentUserId(vnptPayResponse.getAccessToken());
        if (userIdLogin != null) {
            User actor = userService.findByIdAndDeletedFlag(userIdLogin, DeletedFlag.NOT_YET_DELETED.getValue());

            boolean isPersonal = userService.isPersonal(actor);
            User receiverDev = userRepository.getProviderByService(subscription.getId());
            Pricing pricing = pricingService.findByIdAndDeletedFlag(subscription.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
            ServiceEntity subPricingService = servicesService.findByIdAndDeletedFlag(pricing.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
            User userUseSub = userRepository.findUserById(subscription.getUserId()).get();
            // admin tỉnh của SME sử dụng dịch vụ
            List<User> receiverAdmin = userRepository.getListAdminProvince(userUseSub.getProvinceId());
            List<User> userDevAdmin = userRepository.getAllDevAdmin(actor.getId());
            List<User> userSmeAdmin = userRepository.getAllSmeAdmin(actor.getId());
            ServicePricingInfo servicePricingInfo = subscription != null ? serviceRepository.getServicePricingName(subscription.getId()) : null;
            List<User> lstUserDevAdmin = userRepository.getAllDevAdmin(userIdLogin);
            String actorName;
            String[] paramsContent;
            String[] paramsTitle;
            String title;
            String content;
            String userName;
            switch (portalType) {
                case ADMIN:
                    // gửi cho SME
                    subscriptionHelperService.sendMailReactive(actor, null, userUseSub, subscription,
                        isPersonal ? EmailCodeEnum.TB34 : EmailCodeEnum.SC34, portalType, isPersonal, SubscriptionConstant.SME_PORTAL);
                    //gửi thông báo cho dev/admin khi Dev/Admin kích hoạt lại thuê bao
                    title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB33.getTitle(), null)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC33.getTitle(), null);
                    //kiểm tra người kích hoạt lại có phải devAdmin ko
                    if (lstUserDevAdmin.size() > 0) {
                        actorName = "Quản trị viên";
                    } else {
                        actorName = isPersonal ? "Bạn" : "Doanh nghiệp của bạn";
                    }
                    paramsContent = new String[]{actorName, servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName(),
                            userUseSub.getCustomerType() == "CN" ? userUseSub.getLastName() + " " + userUseSub.getFirstName() : userUseSub.getName()};

                    content = NotifyUtil.getContent(ActionNotificationEnum.TB33.getContent(), paramsContent);

                    //gửi cho admin
                    if (receiverAdmin.size() > 0) {
                        for (User adminNoti : receiverAdmin) {
                            sendNotify(
                                    title,
                                    content,
                                    isPersonal ? ActionNotificationEnum.TB33 : ActionNotificationEnum.SC33,
                                    PortalType.ADMIN,
                                    subscription.getId(),
                                    adminNoti.getId()
                            );
                        }
                    }

                    //gửi cho SME khi đc kích hoạt lại thuê bao
                    paramsTitle = new String[]{lstUserDevAdmin.size() > 0 ? "Quản trị viên" : "Nhà cung cấp dịch vụ"};
                    title = NotifyUtil.getContent(ActionNotificationEnum.TB34.getTitle(), paramsTitle);
                    paramsContent = new String[]{userUseSub.getCustomerType() == "CN" ? "Khách hàng" : "Doanh nghiệp",
                            lstUserDevAdmin.size() > 0 ? "Quản trị viên" : "Nhà cung cấp", servicePricingInfo.getServiceName()};
                    content = userUseSub.getCustomerType() == "CN" ? NotifyUtil.getContent(ActionNotificationEnum.TB34.getTitle(), paramsContent) :
                            NotifyUtil.getContent(ActionNotificationEnum.SC34.getTitle(), paramsContent);

                    sendNotify(
                            title,
                            content,
                            userUseSub.getCustomerType() == "CN" ? ActionNotificationEnum.TB34 : ActionNotificationEnum.SC34,
                            PortalType.SME,
                            subscription.getId(),
                            userIdLogin
                    );

                    //gửi thông báo cho dev/admin khi Dev/Admin kích hoạt lại thuê bao
                    title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB33.getTitle(), null)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC33.getTitle(), null);
                    //kiểm tra người kích hoạt lại có phải devAdmin ko
                    if (lstUserDevAdmin.size() > 0) {
                        actorName = "Quản trị viên";
                    } else {
                        actorName = isPersonal ? "Bạn" : "Doanh nghiệp của bạn";
                    }
                    paramsContent = new String[]{actorName, servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName(),
                            userUseSub.getCustomerType() == "CN" ? userUseSub.getLastName() + " " + userUseSub.getFirstName() : userUseSub.getName()};

                    content = NotifyUtil.getContent(ActionNotificationEnum.TB33.getContent(), paramsContent);
                    //gửi cho Devadmin
                    if (lstUserDevAdmin.size() > 0) {
                        for (User userDevAdminNoti : lstUserDevAdmin) {
                            sendNotify(
                                    title,
                                    content,
                                    isPersonal ? ActionNotificationEnum.TB33 : ActionNotificationEnum.SC33,
                                    PortalType.DEV,
                                    subscription.getId(),
                                    userDevAdminNoti.getId()
                            );
                        }
                    }

                    break;
                case DEV:
                    // gửi cho dev, dev admin
                    sendMailAdminProvince(userDevAdmin, subscription, actor, subPricingService, pricing, portalType, isPersonal, isPersonal ? EmailCodeEnum.TB33 : EmailCodeEnum.SC33, DEV);
                    // gửi cho SME
                    subscriptionHelperService.sendMailReactive(actor, null, userUseSub, subscription,
                        isPersonal ? EmailCodeEnum.TB34 : EmailCodeEnum.SC34, portalType, isPersonal, SubscriptionConstant.SME_PORTAL);

                    //gửi thông báo cho dev/admin khi Dev/Admin kích hoạt lại thuê bao
                    title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB33.getTitle(), null)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC33.getTitle(), null);
                    //kiểm tra người kích hoạt lại có phải devAdmin ko
                    actorName = isPersonal ? "Bạn" : "Doanh nghiệp của bạn";
                    paramsContent = new String[]{actorName, servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName(),
                            userUseSub.getCustomerType() == "CN" ? userUseSub.getLastName() + " " + userUseSub.getFirstName() : userUseSub.getName()};

                    content = NotifyUtil.getContent(ActionNotificationEnum.TB33.getContent(), paramsContent);
                    //gửi cho Dev trực tiếp kích hoạt lại thuê bao
                    sendNotify(
                            title,
                            content,
                            isPersonal ? ActionNotificationEnum.TB33 : ActionNotificationEnum.SC33,
                            PortalType.DEV,
                            subscription.getId(),
                            userIdLogin
                    );
                    //gửi cho Devadmin
                    if (lstUserDevAdmin.size() > 0) {
                        for (User userDevAdminNoti : lstUserDevAdmin) {
                            sendNotify(
                                    title,
                                    content,
                                    isPersonal ? ActionNotificationEnum.TB33 : ActionNotificationEnum.SC33,
                                    PortalType.DEV,
                                    subscription.getId(),
                                    userDevAdminNoti.getId()
                            );
                        }
                    }
                    //gửi cho SME khi đc kích hoạt lại thuê bao
                    paramsTitle = new String[]{"Nhà cung cấp dịch vụ"};
                    title = NotifyUtil.getContent(ActionNotificationEnum.TB34.getTitle(), paramsTitle);
                    paramsContent = new String[]{"Nhà cung cấp", servicePricingInfo.getServiceName()};
                    content = userUseSub.getCustomerType() == "CN" ? NotifyUtil.getContent(ActionNotificationEnum.TB34.getContent(), paramsContent) :
                            NotifyUtil.getContent(ActionNotificationEnum.SC34.getContent(), paramsContent);

                    sendNotify(
                            title,
                            content,
                            userUseSub.getCustomerType() == "CN" ? ActionNotificationEnum.TB34 : ActionNotificationEnum.SC34,
                            PortalType.SME,
                            subscription.getId(),
                            userIdLogin
                    );

                    //gửi cho admin cùng tỉnh với SME
                    if (!receiverAdmin.isEmpty()) {
                        for (User userAdminNoti : receiverAdmin) {
                            sendNotify(
                                    title,
                                    content,
                                    userUseSub.getCustomerType() == "CN" ? ActionNotificationEnum.TB34 : ActionNotificationEnum.SC34,
                                    PortalType.ADMIN,
                                    subscription.getId(),
                                    userAdminNoti.getId()
                            );
                        }
                    }

                    break;
                case SME:
                    // gửi cho SME, SME Admin
                    sendMailAdminProvince(userSmeAdmin, subscription, actor, subPricingService, pricing, portalType, isPersonal, isPersonal ? EmailCodeEnum.TB32 : EmailCodeEnum.SC32, SME);

                    if (!userDevAdmin.contains(receiverDev)) {
                        // gửi cho nhà cung cấp dịch vụ
                        subscriptionHelperService.sendMailReactive(actor, null, receiverDev, subscription,
                            isPersonal ? EmailCodeEnum.TB35 : EmailCodeEnum.SC35, portalType, isPersonal, SubscriptionConstant.DEV_PORTAL);
                    }
                    //gửi thông báo cho SME khi SME tự kích hoạt lại thuê bao
                    paramsContent = new String[]{servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName()};
                    title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB32.getTitle(), null)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC32.getTitle(), null);
                    content = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB32.getContent(), paramsContent)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC32.getContent(), paramsContent);
                    sendNotify(
                            title, content,
                            isPersonal ? ActionNotificationEnum.TB32 : ActionNotificationEnum.SC32,
                            PortalType.SME, subscription.getId(),
                            userIdLogin
                    );

                    //gửi thông báo cho nhà cung cấp khi SME tự kích hoạt lại thuê bao
                    actorName = actor.getCustomerType().equals("CN") ? "Khách hàng" : "Doanh nghiệp";
                    paramsTitle = new String[]{actorName};
                    title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB35.getTitle(), paramsTitle)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC35.getTitle(), paramsTitle);
                    userName = isPersonal ? userUseSub.getLastName() + " " + userUseSub.getFirstName() : userUseSub.getName();
                    paramsContent = new String[]{actorName, userName, servicePricingInfo.getServiceName()};
                    content = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB35.getContent(), paramsContent)
                            : NotifyUtil.getContent(ActionNotificationEnum.SC35.getContent(), paramsContent);
                    sendNotify(
                            title,
                            content,
                            isPersonal ? ActionNotificationEnum.TB35 : ActionNotificationEnum.SC35,
                            PortalType.DEV,
                            subscription.getId(),
                            receiverDev.getId()
                    );

                    //gửi thông báo cho admin cùng tỉnh với sme
                    for (User user : receiverAdmin) {
                        sendNotify(
                                title,
                                content,
                                isPersonal ? ActionNotificationEnum.TB35 : ActionNotificationEnum.SC35,
                                PortalType.ADMIN,
                                subscription.getId(),
                                user.getId()
                        );
                    }

                default:
            }
            // gửi admin tỉnh
            if (receiverAdmin != null) {
                sendMailAdminProvince(receiverAdmin, subscription, actor, subPricingService, pricing, portalType, isPersonal, isPersonal ? EmailCodeEnum.TB35 : EmailCodeEnum.SC35, ADMIN);
            }
            Subscription save = subscriptionRepository.save(subscription);
            log.info("=========== reactiveSub end =========: "+ subscription);
            log.info("=========== save end =========: "+ save);
        }
        return subscription;
    }

    public Long getCurrentUserId(String bearerToken) {
        RedisTokenStore redisStore = (RedisTokenStore) tokenStore;
        OAuth2Authentication authentication = redisStore
                .readAuthentication(bearerToken.substring(7));
        if (authentication != null) {
            CustomUserDetails user = (CustomUserDetails) authentication.getPrincipal();
            return user.getId();
        }
        return null;
    }

    //gửi thông báo khi kích hoạt lại thuê bao
    private void sendNotify (String title, String content, ActionNotificationEnum notificationEnum, PortalType portalType, Long subId, Long receiverUserId) {
        NotificationDTO notificationDTO = new NotificationDTO(title,
            content,
            notificationEnum.getScreentId(),
            receiverUserId,
            portalType.getType(), subId);
        NotifyUtil.sendNotify(notificationDTO, notificationEnum.getCode());
    }

    private void sendMailAdminProvince(List<User> receiverAdmin, Subscription subscription, User actor, ServiceEntity subPricingService, Pricing pricing, int portalType, boolean isPersonal, EmailCodeEnum emailCodeEnum, int typeLink) {
        List<MailSendParamDTO> param = new ArrayList<>();
        Map<String, String> response = new HashMap<>();

        // lấy thông tin user sử dụng dịch vụ
        User userUseService = userRepository.findUserById(subscription.getUserId()).get();
        boolean isPersonalUse = userService.isPersonal(userUseService);
        String sourceReactive = CharacterConstant.BLANK;
        String provider = CharacterConstant.BLANK;
        String smeName = CharacterConstant.BLANK;
//        Long idUserUse = subscriptionUserRepository.getUserIdUseSubscription(subscription.getId());
        switch (portalType) {
            case ADMIN:
                sourceReactive = MailParams.ADMIN;
                provider = CharacterConstant.BLANK;
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case DEV:
                sourceReactive = EmailCodeEnum.TB35.equals(emailCodeEnum) ? MailParams.DEV : MailParams.SME_YOU;
                provider = actor.getName() == null ? CharacterConstant.BLANK : actor.getName();
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case SME:
                sourceReactive = isPersonal ? MailParams.PERSONAL : MailParams.SME;
                provider = smeName = isPersonal ? String.join(" ", Optional.ofNullable(actor.getLastName()).orElse(""),
                        Optional.ofNullable(actor.getFirstName()).orElse("")).trim() :
                        actor.getName() == null ? CharacterConstant.BLANK : actor.getName();
                break;
            default:
        }
        String transactionCode = ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(), pricing.getPricingName());


        String nameReactive = isPersonal || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()) : actor.getName();
        DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String reactiveDate = df.format(Objects.nonNull(subscription.getReactiveDate()) ? subscription.getReactiveDate() : new Date());
        String dateStartReactive = df.format(subscription.getStartCurrentCycle());
        String dateEndReactive = df.format(subscription.getEndCurrentCycle());


        Subscription sub = subscriptionRepository.findById(subscription.getId()).get();
        Bills billCurrent = billsRepository.getFirstByOfCycle(sub.getId(), sub.getCurrentCycle());
        String numberOfCycle = (subscription.getNumberOfCycles() == null || subscription.getNumberOfCycles() == -1)
            ? SubscriptionConstant.MailParams.QUANTITY_UNLIMITED :
                subscription.getNumberOfCycles().toString();
        Department department = null;
        if (actor.getDepartmentId() != null) {
            department = departmentsRepository.findById(actor.getDepartmentId()).orElse(new Department());
        }
        String phoneProvince = department == null || department.getProvinceId() == null ?
                StringUtils.EMPTY : contactProvinceRepository.getPhoneInProvince(department.getProvinceId());

        //chu kỳ của gói
        String cycle = CharacterConstant.BLANK;
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            Optional<PricingMultiPlan> pricingMultiPlan = pricingMultiPlanRepository.findByIdAndDeletedFlag(subscription.getPricingMultiPlanId(),  DeletedFlag.NOT_YET_DELETED.getValue());
            if (pricingMultiPlan.isPresent()) {
                cycle = pricingMultiPlan.get().getPaymentCycle().toString() + CharacterConstant.SPACE + pricingService.getTitleCycleType(pricingMultiPlan.get().getCircleType());
            }
        } else {
            cycle = pricing.getPaymentCycle().toString() + CharacterConstant.SPACE + pricingService.getTitleCycleType(pricing.getCycleType());
        }

        response.put(SubscriptionConstant.MailParam.NAME_SERVICE, subPricingService.getServiceName());
        response.put(SubscriptionConstant.MailParam.NAME_PRICING, pricing.getPricingName());
//            response.put(SubscriptionConstant.MailParam.NAME_COMBO, objectName);
//            response.put(SubscriptionConstant.MailParam.NAME_COMBO_PRICING, objectPlanName);
        response.put(SubscriptionConstant.MailParam.CODE_TRANSACTION, transactionCode);
        response.put(SubscriptionConstant.MailParam.SOURCE_REACTIVE, sourceReactive);
        response.put(SubscriptionConstant.MailParam.SME_DEV_OR_ADMIN, sourceReactive);
        response.put(SubscriptionConstant.MailParam.SME_NAME, smeName);
        response.put(SubscriptionConstant.MailParam.PROVIDER, provider);
        response.put(SubscriptionConstant.MailParam.NAME_REACTIVE, nameReactive);
        response.put(SubscriptionConstant.MailParam.DATE_REACTIVE, reactiveDate);
        response.put(SubscriptionConstant.MailParam.DATE_START_REACTIVE, dateStartReactive);
        response.put(SubscriptionConstant.MailParam.DATE_END_REACTIVE, dateEndReactive);
        response.put(SubscriptionConstant.MailParam.NUMBER_OF_CYCLE, numberOfCycle);
        response.put(SubscriptionConstant.MailParam.CURRENT_CYCLE, cycle);
        response.put(SubscriptionConstant.MailParam.AMOUNT,
            billCurrent == null ? CharacterConstant.BLANK : billCurrent.getTotalAmount().toString());

        response.put(SubscriptionConstant.MailParam.HOTLINE_TINH, phoneProvince);
        receiverAdmin.forEach(u -> {
            boolean isPersonalReceiver = userService.isPersonal(u);
            String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ?
                    String.join(" ", Optional.ofNullable(u.getLastName()).orElse(""), Optional.ofNullable(u.getFirstName()).orElse("")).trim()
                    : u.getName();
            String typeLinkDetail = subscriptionDetailService.getLinkSubDetailSME(u.getId());
            String linkReActive = emailService.getLinkReActive(subscription.getId(), PortalType.valueOf(typeLink));

            if (typeLink == DEV) {
                typeLinkDetail = SubscriptionConstant.LINK_DEV_SUBS_SERVICE_DETAIL;
            } else if (typeLink == ADMIN) {
                typeLinkDetail = SubscriptionConstant.LINK_ADMIN_SUBS_SERVICE_DETAIL;
            }
            String linkDetail = String.format(typeLinkDetail, webHost, subscription.getId());

            response.put(SubscriptionConstant.MailParam.LINK_RE_ACTIVE, linkReActive);
            response.put(SubscriptionConstant.MailParam.LINK_DETAIL, linkDetail);
            response.put(SubscriptionConstant.MailParam.USER, actorName);
            List<MailParamResDTO> mailParam = getListPramMailDTO(response, isPersonalUse ? EmailCodeEnum.TB35 : EmailCodeEnum.SC35);
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(u.getEmail());
            mailSendParamDTO.setListMailParam(mailParam);
            param.add(mailSendParamDTO);
        });
        if (isPersonalUse && emailCodeEnum == EmailCodeEnum.SC35) {
            emailService.sendMultiMail(EmailCodeEnum.TB35, param);
        } else {
            emailService.sendMultiMail(emailCodeEnum, param);
        }
    }

    // lấy danh sách param
    private List<MailParamResDTO> getListPramMailDTO(Map<String, String> params, EmailCodeEnum emailCode) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(emailCode.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            mailParamResDTO.setValue(params.getOrDefault(mailParamResDTO.getParamName(), mailParamResDTO.getValue()));
        });
        return paramNameByCode;
    }

    @Override
    public void sendKafka(TransactionStatusResDTO transactionStatusDTO, String token) {
        if (Objects.nonNull(transactionStatusDTO.getCallTrans())) {
            Subscription subscription = transactionStatusDTO.getSubscription();
            if (Objects.equals(transactionStatusDTO.getCallTrans(), SUBS_PRICING)) {
                pricingRepository.findById(subscription.getPricingId())
                        .ifPresent(pri -> {
                            log.info("sendKafka: transactionOneSME, subscriptionId {}", subscription.getId());
                            integrationService.transactionOneSME(token,
                                    transactionStatusDTO.getSubscription(), pri,
                                    IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null,
                                    false, transactionStatusDTO.getActivityLogInfoDTO());
                        });
            } else if (Objects.equals(transactionStatusDTO.getCallTrans(), SUBS_COMBO)) {
                comboPlanRepository
                        .findById(subscription.getComboPlanId())
                        .ifPresent(combo -> {
                            log.info("sendKafka: transactionOneSMECombo, subscriptionId {}", subscription.getId());
                            integrationService.transactionOneSMECombo(token,
                                    transactionStatusDTO.getSubscription(), combo,
                                    IntegrationActionTypeEnum.CREATE_SUBSCRIPTION, null, null, null,
                                    null, transactionStatusDTO.getActivityLogInfoDTO());
                        });
            }
        }
        log.info("======= End call API VNPT transaction payment when create subs ======");
    }
    /**
     * Xóa Bill khi check giao dịch thanh toán qua VNPT Pay không thành công
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBillWhenPaymentFail(Long billId) {
        Optional<Bills> billDB = billsRepository.findById(billId);
        if (billDB.isPresent()) {
            Bills bill = billDB.get();
            List<BillItem> billItems = billItemRepository.findByBillingId(bill.getId());
            billItems.forEach(billItem -> {
                billCouponPrivateRepository.deleteByBillingItemId(billItem.getId());
                billCouponTotalRepository.deleteByBillingItemId(billItem.getId());
                billTaxRepository.deleteByBillingItemId(billItem.getId());
            });
            billsRepository.delete(bill);
        }
    }

    @Override
    @Transactional
    public SubscriptionRegisterResDTO repay(TransactionStatusReqDTO data, String ipAddress, Long billingId) {

        Optional<VNPTPayResponse> vnptPayResponseOptional = vnptPayResponseRepository.findByMerchantOrderId(data.getMerchantOrderId());

        /* nếu là thanh toán với giỏ hàng */
        if (vnptPayResponseOptional.get().getSubscriptionId() == null && vnptPayResponseOptional.get().getLstSubId() != null) {
            return repayForShoppingCart(vnptPayResponseOptional, ipAddress);
        }

        // Thực hiện nếu tồn tại order và order đó có trạng thái khác với trạng thái SUCCESS
        if (vnptPayResponseOptional.isPresent()
            && !Objects.equals(vnptPayResponseOptional.get().getTransactionStatus(), VnptPayTransactionStatusEnum.SUCCESS.getValue())) {
            VNPTPayResponse vnptPayResponseOld = vnptPayResponseOptional.get();
            if (!Objects.equals(billingId, vnptPayResponseOld.getBillingId())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_DATA_INVALID, Resources.VNPT_PAY_RESPONSE, ErrorKey.ID,
                    ErrorKey.BILL_ID);
            }
            //khoi tao giao dich moi
            VNPTPayResponse vnptPayResponseNew = new VNPTPayResponse();
            vnptPayResponseNew.setId(null);
            vnptPayResponseNew.setAmount(vnptPayResponseOld.getAmount().setScale(0, RoundingMode.HALF_UP));
            vnptPayResponseNew.setPaymentMethod(OrderConstant.PAYMENT_METHOD);
            vnptPayResponseNew.setCurrencyCode(OrderConstant.CURRENCY_CODE);
            vnptPayResponseNew.setLocale(OrderConstant.LOCALE);
            vnptPayResponseNew.setMerchantOrderId(commonService.getMaximumCode(KeyGenType.VNPT_PAY));
            vnptPayResponseNew.setSubscriptionId(vnptPayResponseOld.getSubscriptionId());
            vnptPayResponseNew.setOrderStatus(OrderStatusEnum.INIT.value);
            vnptPayResponseNew.setPortalType(vnptPayResponseOld.getPortalType());

            Optional<Subscription> subscriptionOptional = subscriptionRepository.findById(vnptPayResponseNew.getSubscriptionId());
            if (subscriptionOptional.isPresent()) {
                Subscription subscription = subscriptionOptional.get();
                ClueDTO clueDTO = subscriptionDetailService.getMerchantInfo(subscription);
                PaymentInitResDTO initResponse = subscriptionPaymentService.initPayment(vnptPayResponseNew, ipAddress, subscription, null);
                //Thuc hien cap nhat trang thai dich vu, khoi tao giao dich voi va xoa giao dich loi truoc do
                if (initResponse.getResponse_code().equals(SubscriptionConstant.INIT_SUCCESS)) {
                    Bills bill = billsRepository.findById(billingId).orElseThrow(() -> {
                        return exceptionFactory.badRequest(MessageKeyConstant.FIELD_DATA_INVALID, Resources.VNPT_PAY_RESPONSE, ErrorKey.ID,
                            ErrorKey.BILL_ID);
                    });
                    bill.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                    billsRepository.save(bill);

                    // nếu k phải là kích hoạt lại
                    if (!Objects.equals(bill.getActionType(), ACTION_RE_ACTIVE_OUT_OF_DATE) && !Objects.equals(bill.getActionType(), ACTION_RE_ACTIVE_IN_PAYMENT)) {
                        if (!Objects.equals(bill.getActionType(), ACTION_UPDATE) && !Objects.equals(bill.getActionType(), ACTION_CHANGE_PLAN)
                            && !Objects.equals(bill.getActionType(), ACTION_RENEW_SUBSCRIPTION) ) {
                            subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                        }
                        subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                        Subscription result = subscriptionRepository.save(subscription);
                        // Lưu lịch sử thay đổi trạng thái thuê bao
                        subscriptionHistoryService.saveStatusHistory(result);
                    }
                    //update order vnpt pay
                    vnptPayResponseNew.setResponseCode(SubscriptionConstant.INIT_SUCCESS);
                    vnptPayResponseNew.setDescription(initResponse.getDescription());
                    vnptPayResponseNew.setSecureCode(initResponse.getSecure_code());
                    vnptPayResponseNew.setOrderStatus(OrderStatusEnum.PROCESSING.value);
                    vnptPayResponseNew.setBillingId(billingId);
                    vnptPayResponseNew.setMerchantServiceId(
                        (Objects.nonNull(clueDTO) && Objects.nonNull(clueDTO.getMerchantServiceId())) ? clueDTO.getMerchantServiceId().toString()
                            : null);
                    //bổ sung token lưu lại trong bảng này, hỗ trợ tích hợp IDC Portal
                    if (AuthUtil.getCurrentUser() != null && AuthUtil.getCurrentUser().getBearerToken() != null) {
                        vnptPayResponseNew.setAccessToken(AuthUtil.getCurrentUser().getBearerToken());
                    }
                    vnptPayResponseRepository.save(vnptPayResponseNew);
                    return SubscriptionRegisterResDTO.builder().id(subscription.getId()).redirectURL(initResponse.getRedirect_url()).build();
                } else {
                    throw new VNPTPayResponseException(initResponse);
                }
            }
        }
        String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                new String[]{Resources.PAYMENT},
                LocaleContextHolder.getLocale());
        throw new BadRequestException(messageNotFound, Resources.PAYMENT,
                VNPT_PAY.MERCHANT_ORDER_ID,
                MessageKeyConstant.NOT_FOUND);
    }

    /**
     * Get private key for MerchantServiceId
     *
     */
    private String getPrivateKeyByMerchantServiceId(String merchantServiceId) {
        List<Clue> clues = clueRepository.findAllByMerchantServiceId(Long.valueOf(merchantServiceId));
        if (CollectionUtils.isEmpty(clues)) {
            return secretKey;
        }
        return clues.get(0).getPrivateKey();
    }

    public boolean checkBillOutOfDate(List<Bills> bills){
        return bills.stream().anyMatch(b -> Objects.equals(b.getStatus(), 4));
    }

    private SubscriptionRegisterResDTO repayForShoppingCart(Optional<VNPTPayResponse> vnptPayResponse, String ipAddress) {
        log.info("======repayForShoppingCart=============");
        SubscriptionRegisterResDTO subscriptionRegisterResDTO = null;
        if (vnptPayResponse.isPresent()
                && !Objects.equals(vnptPayResponse.get().getTransactionStatus(), VnptPayTransactionStatusEnum.SUCCESS.getValue())) {
            VNPTPayResponse vnptPayResponseOld = vnptPayResponse.get();

            //khoi tao giao dich moi
            VNPTPayResponse vnptPayResponseNew = new VNPTPayResponse();
            vnptPayResponseNew.setId(null);
            vnptPayResponseNew.setAmount(vnptPayResponseOld.getAmount().setScale(0, RoundingMode.HALF_UP));
            vnptPayResponseNew.setPaymentMethod(OrderConstant.PAYMENT_METHOD);
            vnptPayResponseNew.setCurrencyCode(OrderConstant.CURRENCY_CODE);
            vnptPayResponseNew.setLocale(OrderConstant.LOCALE);
            vnptPayResponseNew.setMerchantOrderId(commonService.getMaximumCode(KeyGenType.VNPT_PAY));
            vnptPayResponseNew.setLstSubId(vnptPayResponseOld.getLstSubId());
            vnptPayResponseNew.setLstBillId(vnptPayResponseOld.getLstBillId());
            vnptPayResponseNew.setOrderStatus(OrderStatusEnum.INIT.value);

            Optional<Subscription> subscriptionOptional = subscriptionRepository.findById(Long.valueOf(vnptPayResponseNew.getLstSubId().get(0)));
            if (subscriptionOptional.isPresent()) {
                ClueDTO clueDTO = subscriptionDetailService.getMerchantInfo(subscriptionOptional.get());
                PaymentInitResDTO initResponse = subscriptionPaymentService.initPayment(vnptPayResponseNew, ipAddress,
                    subscriptionOptional.get(), null);
                if (initResponse == null) {
                    throw new VNPTPayResponseException(initResponse);
                } else {
                    for (int i = 0; i < vnptPayResponseNew.getLstSubId().size(); i++) {
                        Subscription subscription = subscriptionRepository.findById(Long.valueOf(vnptPayResponseOld.getLstSubId().get(i))).get();
                        if (initResponse.getResponse_code().equals(SubscriptionConstant.INIT_SUCCESS)) {
                            Bills bill = billsRepository.findById(Long.valueOf(vnptPayResponseNew.getLstBillId().get(i))).orElseThrow(() -> exceptionFactory.badRequest(MessageKeyConstant.FIELD_DATA_INVALID, Resources.VNPT_PAY_RESPONSE, ErrorKey.ID,
                                ErrorKey.BILL_ID));
                            bill.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                            billsRepository.save(bill);
                            if (!Objects.equals(bill.getActionType(), ACTION_UPDATE) && !Objects.equals(bill.getActionType(), ACTION_CHANGE_PLAN)) {
                                subscription.setConfirmStatus(SubscriptionConfirmStatusEnum.WAITING.value);
                            }
                            subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                            Subscription result = subscriptionRepository.save(subscription);
                            // Lưu lịch sử thay đổi trạng thái thuê bao
                            subscriptionHistoryService.saveStatusHistory(result);
                        }
                    }
                }
                vnptPayResponseNew.setResponseCode(SubscriptionConstant.INIT_SUCCESS);
                vnptPayResponseNew.setDescription(initResponse.getDescription());
                vnptPayResponseNew.setSecureCode(initResponse.getSecure_code());
                vnptPayResponseNew.setOrderStatus(OrderStatusEnum.PROCESSING.value);
                vnptPayResponseNew.setMerchantServiceId(
                        (Objects.nonNull(clueDTO) && Objects.nonNull(clueDTO.getMerchantServiceId())) ? clueDTO.getMerchantServiceId().toString()
                                : null);
                vnptPayResponseRepository.save(vnptPayResponseNew);
                return SubscriptionRegisterResDTO.builder()
                    .id(Long.valueOf(vnptPayResponse.get().getLstSubId().get(0))).redirectURL(initResponse.getRedirect_url()).build();
            }
        }

        String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                new String[]{Resources.PAYMENT},
                LocaleContextHolder.getLocale());
        throw new BadRequestException(messageNotFound, Resources.PAYMENT,
                VNPT_PAY.MERCHANT_ORDER_ID,
                MessageKeyConstant.NOT_FOUND);
    }

    @Override
    public TransactionStatusResDTO checkOrderQr(Long billId) {
        VnptPayTransactionStatusEnum status;
        Bills bills = new Bills();
        if (Objects.isNull(billId)) status = VnptPayTransactionStatusEnum.NOT_FOUND;
        else {
            bills = billsRepository.findBilling(billId).orElse(null);
            if (Objects.isNull(bills)) status = VnptPayTransactionStatusEnum.NOT_FOUND;
            else {
                if (Objects.nonNull(bills.getStatus()) &&
                        Arrays.asList(BillStatusEnum.WAITING.value, BillStatusEnum.INVALID.value).contains(bills.getStatus())) {
                    status = VnptPayTransactionStatusEnum.PROCESSING;
                } else if (Objects.nonNull(bills.getStatus()) && !bills.getStatus().equals(BillStatusEnum.PAID.value)) {
                    status = VnptPayTransactionStatusEnum.FAIL;
                } else {
                    status = VnptPayTransactionStatusEnum.SUCCESS;
                }
            }
        }
        // TH thành công => thêm data
        TransactionStatusResDTO res = new TransactionStatusResDTO();
        if (status.equals(VnptPayTransactionStatusEnum.SUCCESS)) { // Lấy data tương tự như api /verify
            VNPTPayResponse vnptPayResponse = vnptPayResponseRepository.findByBillId(billId).orElse(null);
            if (Objects.nonNull(vnptPayResponse)) {
                Subscription subscription = bills.getSubscription();
                res = vnptPayTransactionStatusMapper.toDto(vnptPayResponse);
                res.setIsON(subscriptionRepository.isONSub(bills.getSubscriptionsId()));
                res.setPricingMultiPlanId(subscription.getPricingMultiPlanId());
                res.setServiceId(subscription.getServiceId());
                res.setSubscriptionCode(subscription.getSubCode());
                res.setSubscriptionId(bills.getSubscriptionsId());
                res.setPortalType(Objects.nonNull(res.getPortalType()) ? PortalType.getPortalTypeStr(vnptPayResponse.getPortalType()) : PortalType.SME.name());
                res.setAction(Objects.nonNull(bills.getActionType()) ? bills.getActionType() : -1);
                res.setBillingId(billId);
                res.setCartCodeSub(subscription.getCartCode());
                res.setCartCodeBill(bills.getCartCode());
                res.setActionType(
                        (Objects.nonNull(bills.getActionType()))
                                ? ActionTypeEnum.valueOf(bills.getActionType()) : ActionTypeEnum.CREATE_SUBSCRIPTION);
                if (Objects.nonNull(subscription.getComboPlanId()) && Objects.isNull(subscription.getPricingId())) {
                    res.setType(SubsTypeEnum.COMBO);
                    res.setComboPlanId(subscription.getComboPlanId());
                    ComboPlan comboPlan = comboPlanRepository.findById(subscription.getComboPlanId()).orElse(null);
                    if (Objects.nonNull(comboPlan)) {
                        Combo combo = comboRepository.findById(comboPlan.getComboId()).orElse(null);
                        if (Objects.nonNull(combo)) {
                            res.setComboId(combo.getId());
                            res.setComboName(combo.getComboName());
                        }
                    }

                } else if (Objects.isNull(subscription.getComboPlanId()) && Objects.nonNull(subscription.getServiceId())) {
                    if (Objects.nonNull(subscription.getServiceGroupId())) {
                        res.setType(SubsTypeEnum.GROUP_SERVICE);
                    } else {
                        res.setType(SubsTypeEnum.SERVICE);
                    }
                    res.setPlanId(subscription.getPricingId());
                    ServiceEntity serviceEntity = serviceRepository.findById(subscription.getServiceId()).orElse(null);
                    if (Objects.nonNull(serviceEntity)) {
                        if (Objects.nonNull(serviceEntity.getServiceOwner())) {
                            res.setServiceOwner(ServiceTypeEnum.valueOf(serviceEntity.getServiceOwner()).toString());
                        }
                        res.setServiceName(serviceEntity.getServiceName());
                    }
                }
                res.setScreenType(vnptPayResponseRepository.getScreenType(res.getMerchantOrderId()));
                if (Objects.nonNull(res.getActionType()) && (Objects
                        .equals(res.getActionType().name(), ActionTypeEnum.CHANGE_PLAN.name()) || Objects
                        .equals(res.getActionType().name(), ActionTypeEnum.UPDATE_SUBSCRIPTION.name()))) {
                    res.setScreenType("SUBSCRIPTION");
                }
                // SmartCA
                ActionTypeEnum actionType = res.getActionType();
                boolean enableSmartCA = subscriptionDetailService.checkEnableSmartCA(bills.getSubscriptionsId());
                if (enableSmartCA && actionType == ActionTypeEnum.CREATE_SUBSCRIPTION) {
                    ResponseEcontractDTO contractDTO = econtractService.createElectronicContractDraft(bills.getSubscriptionsId(), null);
                    res.setSmartCAPopupDTO(new SmartCAPopupDTO(contractDTO));
                    //lay access token
                    if (contractDTO != null && contractDTO.getObject().getContractId() != null) {
                        ResponseEcontractSSODTO sso = econtractService.getTokenSSO(null, token);
                        if (sso != null && sso.getObject() != null) {
                            res.getSmartCAPopupDTO().setAccessToken(sso.getObject().getAccessToken());
                        }
                    }
                }
            }
        }

        res.setStatus(status);
        return res;
    }
}
