package com.service.crm.revenueTarget.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.constant.enums.crm.revenueTarget.AdjustRevenueTargetEnum;
import com.constant.enums.crm.revenueTarget.TargetObjectTypeEnum;
import com.constant.enums.crm.revenueTarget.TargetSubTypeEnum;
import com.constant.enums.crm.revenueTarget.TargetTypeEnum;
import com.dto.common.IAdminRevenueDetail;
import com.dto.crm.dataPartition.ViewerDetailDTO;
import com.dto.crm.revenueTarget.AdminDetailRevenueTarget;
import com.dto.crm.revenueTarget.DetailPartitionListDTO;
import com.dto.crm.revenueTarget.DetailProductListDTO;
import com.dto.crm.revenueTarget.DuplicateTargetDTO;
import com.dto.crm.revenueTarget.GetListObjectRevenueTarget;
import com.dto.crm.revenueTarget.IDetailPartitionListDTO;
import com.dto.crm.revenueTarget.IDetailProductListDTO;
import com.dto.crm.revenueTarget.IGetListActualSubId;
import com.dto.crm.revenueTarget.IGetListObjectRevenueTarget;
import com.dto.crm.revenueTarget.IGetTargetFilterDTO;
import com.dto.crm.revenueTarget.IGetTargetIdDTO;
import com.dto.crm.revenueTarget.IListProductAdmin;
import com.dto.crm.revenueTarget.IObjectDetailRevenueTarget;
import com.dto.crm.revenueTarget.IOverlapTargetDTO;
import com.dto.crm.revenueTarget.IRevenueTargetDetailDTO;
import com.dto.crm.revenueTarget.ITargetValueAndAdminDetail;
import com.dto.crm.revenueTarget.ObjectDetailRevenueTarget;
import com.dto.crm.revenueTarget.ReminderDateConfigDTO;
import com.dto.crm.revenueTarget.RevenueTargetConfigDTO;
import com.dto.crm.revenueTarget.RevenueTargetDTO;
import com.dto.crm.revenueTarget.RevenueTargetListDTO;
import com.dto.crm.revenueTarget.RevenueTargetValueDTO;
import com.dto.crm.revenueTarget.TargetValueAndAdminDetail;
import com.dto.crm.revenueTarget.TargetValueDTO;
import com.dto.crm.revenueTarget.TargetValueUpdateContent;
import com.dto.report.dashboardSme.SubscriptionsReportResDTO;
import com.entity.crm.revenueTarget.RevenueTarget;
import com.entity.crm.revenueTarget.RevenueTargetValue;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.crm.CrmPermissionEnum;
import com.onedx.common.constants.enums.revenueTarget.IntervalTypeEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.systemParams.SystemParamTypeEnum;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.utils.ObjectMapperUtil;
import com.onedx.common.utils.ObjectUtil;
import com.repository.crm.dataPartition.CrmDataPartitionRepository;
import com.repository.crm.revenueTarget.RevenueTargetRepository;
import com.repository.crm.revenueTarget.RevenueTargetValueRepository;
import com.service.crm.dataPartition.CrmAdminPermissionUtil;
import com.service.crm.dataPartition.DataPartitionService;
import com.service.crm.revenueTarget.RevenueTargetService;
import com.service.notification.ActionNotificationService;
import com.service.notification.impl.ActionNotificationTemplateBase;
import com.service.notification.template.DT01;
import com.service.notification.template.DT02;
import com.service.notification.template.DT03;
import com.service.notification.template.DT04;
import com.service.notification.template.DT05;
import com.service.notification.template.DT06;
import com.service.notification.template.DT11;
import com.service.notification.template.DT12;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.RoleUtil;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RevenueTargetServiceImpl implements RevenueTargetService {

    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private RevenueTargetRepository targetRepository;
    @Autowired
    private RevenueTargetValueRepository targetValueRepository;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private ActionNotificationService actionNotificationService;
    @Autowired
    private CrmAdminPermissionUtil adminPermissionUtil;
    @Autowired
    private CrmDataPartitionRepository dataPartitionRepository;
    @Autowired
    private DataPartitionService dataPartitionService;

    @Value("${web.host}")
    private String webHost;

    @Override
    public List<DuplicateTargetDTO> getDuplicateTarget(RevenueTargetDTO dto) {
        // Lấy ra đối tượng áp dụng của các mục tiêu trùng lặp thời gian
        List<IOverlapTargetDTO> lstOverlapTarget = targetValueRepository.getTimeOverlapTarget(dto.getStartDate(), dto.getEndDate());

        // Kiểm tra sự trùng lặp đối tượng áp dụng
        return lstOverlapTarget.stream()
            .filter(target -> dto.getLstTargetValue().stream().anyMatch(targetDTO ->
                isEqual(target.getPartitionId(), targetDTO.getPartitionId())
                    && isEqual(target.getAdminId(), targetDTO.getAdminId())
                    && isEqual(target.getServiceId(), targetDTO.getServiceId())))
            .map(dupTarget -> new DuplicateTargetDTO(dupTarget.getTargetName(), dupTarget.getTargetCode()))
            .distinct()
            .collect(Collectors.toList());
    }

    @Transactional
    @Override
    public void createTarget(RevenueTargetDTO requestDTO) {
        log.info("====================== start method createTarget ======================");
        // Lưu thông tin doanh thu mục tiêu
        RevenueTarget target = new RevenueTarget();
        BeanUtils.copyProperties(requestDTO, target, "id");
        target.setCode(generateTargetCode());
        target.setCreatedBy(AuthUtil.getCurrentUserId());
        target.setCreatedAt(LocalDateTime.now());
        target.setReminderDate(calculateReminderDate(target));
        RevenueTarget targetSaved = targetRepository.save(target);

        // Lưu đối tượng áp dụng của doanh thu mục tiêu
        targetValueRepository.saveAll(requestDTO.getLstTargetValue().stream().map(dto -> {
            RevenueTargetValue targetValue = new RevenueTargetValue();
            BeanUtils.copyProperties(dto, targetValue);
            targetValue.setTargetId(targetSaved.getId());
            return targetValue;
        }).collect(Collectors.toList()));

        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(requestDTO.getObjectType());
        List<TargetValueAndAdminDetail> lstTargetValueDetail = getListTargetValueDetailByTargetId(targetSaved.getId(), objectType);

        // gửi notification
        Thread sendMailThread = new Thread(() -> sendMailCreateRevenueTarget(lstTargetValueDetail, objectType, targetSaved));
        sendMailThread.start();

        log.info("====================== end method createTarget ======================");
    }

    private void sendMailCreateRevenueTarget(List<TargetValueAndAdminDetail> lstTargetValueDetail, TargetObjectTypeEnum objectType, RevenueTarget target) {
        log.info("====================== start thread sendMailCreateRevenueTarget ======================");
        List<ActionNotificationTemplateBase> lstParamDTO = new ArrayList<>();

        switch (objectType) {
            case PARTITION: // chỉ gửi thư cho quản lý phân vùng
                lstParamDTO.add(new DT01(lstTargetValueDetail, target, webHost));
                break;

            case PRODUCT:  // gửi chung 1 mail nếu NVKD đc giao nhiều SPDV
                lstParamDTO.add(new DT03(lstTargetValueDetail, target, webHost));
                break;

            case ADMIN:
                lstParamDTO.add(new DT02(lstTargetValueDetail, target, webHost));
                break;
        }
        lstParamDTO.forEach(actionNotificationService::send);
        log.info("====================== end thread sendMailCreateRevenueTarget ======================");
    }

    @Transactional
    @Override
    public void updateTarget(RevenueTargetDTO requestDTO) {
        log.info("====================== start method updateTarget ======================");
        RevenueTarget target = getRevenueTargetById(requestDTO.getId());
        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(requestDTO.getObjectType());
        // lưu các target và target value cũ
        List<TargetValueAndAdminDetail> oldLstTargetValueDetail = getListTargetValueDetailByTargetId(target.getId(), objectType);
        // Map thông tin target value cũ với ID
        Map<Long, Long> mapOldTargetValueById = new HashMap<>();
        oldLstTargetValueDetail.stream().filter(item -> item.getTargetValue() > 0)
            .forEach(item -> mapOldTargetValueById.put(item.getTargetValueId(), item.getTargetValue()));
        CustomUserDetails currentUser = AuthUtil.getCurrentUser();
        Collection<? extends GrantedAuthority> authorities = Objects.nonNull(currentUser) ? currentUser.getAuthorities() : new HashSet<>();
        Long currentId = Objects.nonNull(currentUser) ? currentUser.getId() : null;
        // Nếu là full admin hoặc là nv tạo dtmt chỉnh sửa thì không validate theo cấu hình
        if (!(RoleUtil.checkRole(authorities, Collections.singletonList(RoleType.FULL_ADMIN.getValue())) || Objects.equals(currentId, target.getCreatedBy()))) {
            validateAdjustRevenueConfig(requestDTO.getLstTargetValue(), mapOldTargetValueById);
        }

        RevenueTarget oldTarget = new RevenueTarget();
        BeanUtils.copyProperties(target, oldTarget);

        // Lưu target mới
        BeanUtils.copyProperties(requestDTO, target, "id");
        target.setModifiedBy(AuthUtil.getCurrentUserId());
        target.setModifiedAt(LocalDateTime.now());
        target.setReminderDate(calculateReminderDate(target));
        targetRepository.save(target);

        // Xóa các đối tượng áp dụng cũ
        Set<Long> lstDeleteTargetValueId = ObjectUtil.getOrDefault(requestDTO.getLstDeletedTargetValueId(), new HashSet<>());
        lstDeleteTargetValueId.addAll(requestDTO.getLstTargetValue().stream().map(RevenueTargetValueDTO::getTargetValueId)
            .filter(Objects::nonNull).collect(Collectors.toSet()));
        if (!lstDeleteTargetValueId.isEmpty()) {
            targetValueRepository.deleteAllByTargetIdAndTargetValueIdIn(requestDTO.getId(), lstDeleteTargetValueId);
        }
        // Lưu đối tượng áp dụng của doanh thu mục tiêu
        targetValueRepository.saveAll(requestDTO.getLstTargetValue().stream().map(dto -> {
            RevenueTargetValue targetValue = new RevenueTargetValue();
            BeanUtils.copyProperties(dto, targetValue);
            targetValue.setTargetId(requestDTO.getId());
            return targetValue;
        }).collect(Collectors.toList()));

        // lưu các target value mới
        List<TargetValueAndAdminDetail> newLstTargetValueDetail = getListTargetValueDetailByTargetId(target.getId(), objectType);

        // gửi notification
        Thread sendMailThread = new Thread(
            () -> sendMailUpdateRevenueTarget(oldLstTargetValueDetail, newLstTargetValueDetail, objectType, target, oldTarget));
        sendMailThread.start();

        log.info("====================== end method updateTarget ======================");
    }

    private void validateAdjustRevenueConfig(List<RevenueTargetValueDTO> lstTargetValue, Map<Long, Long> mapOldTargetValueById) {
        RevenueTargetConfigDTO revenueTargetConfig = systemParamService.getRevenueTargetConfig();
        int adjustRevenueTarget = ObjectUtil.getOrDefault(revenueTargetConfig.getAdjustRevenueTarget(), AdjustRevenueTargetEnum.GREATER_THAN_OR_EQUAL.getValue());
        for (RevenueTargetValueDTO newTargetValue: lstTargetValue) {
            if (mapOldTargetValueById.containsKey(newTargetValue.getTargetValueId())) {
                Long oldValue = mapOldTargetValueById.get(newTargetValue.getTargetValueId());
                Long newValue = ObjectUtil.getOrDefault(newTargetValue.getTargetValue(), 0L);
                if ((Objects.equals(adjustRevenueTarget, AdjustRevenueTargetEnum.GREATER_THAN_OR_EQUAL.getValue()) && oldValue > newValue) ||
                    (Objects.equals(adjustRevenueTarget, AdjustRevenueTargetEnum.LESS_THAN_OR_EQUAL.getValue()) && newValue > oldValue) ) {
                    throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_DATA, Resources.REVENUE_TARGET_VALUE, Resources.REVENUE_TARGET_VALUE);
                }
            }
        }
    }

    @Transactional
    @Override
    public void deleteTarget(Long targetId) {
        log.info("====================== start method deleteTarget ======================");
        RevenueTarget target = getRevenueTargetById(targetId);
        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(target.getObjectType());
        List<TargetValueAndAdminDetail> lstTargetValueAndAdminDetail = getListTargetValueDetailByTargetId(targetId, objectType);

        targetRepository.delete(target);
        targetValueRepository.deleteAllByTargetId(targetId);

        Thread sendMailThread = new Thread(() -> sendMailDeleteRevenueTarget(target, lstTargetValueAndAdminDetail, objectType));
        sendMailThread.start();
        log.info("====================== end method deleteTarget ======================");
    }

    @Transactional
    @Override
    public void deleteTargetValue(Long objectId, Long targetId) {
        log.info("====================== start method deleteTargetValue ======================");
        RevenueTarget target = getRevenueTargetById(targetId);
        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(target.getObjectType());

        boolean haveToDeleteTarget = false; // cờ check xóa toàn bộ target khi đối tượng là phân vùng

        if (objectType == TargetObjectTypeEnum.PARTITION) {
            // Nếu đối tượng xóa phân vùng ĐNKD (parentId = -1) thuộc DTMT
            // Hoặc là targetValue của phân vùng cuối cùng (không phải PV ĐNKD) thuộc DTMT
            // -> Xóa cả DTMT
            haveToDeleteTarget = dataPartitionRepository.existsByIdAndParentId(objectId, -1L)
                || targetValueRepository.countAllByTargetId(targetId) <= 2;
        }
        if (haveToDeleteTarget) {
            deleteTarget(targetId);
        }
        else {
            List<RevenueTargetValue> lstTargetValue = targetValueRepository.getListTargetValueByObjectIdAndTargetId(objectId, targetId,
                target.getObjectType());
            // Cho đối tượng SPDV: lấy số lượng SPDV mà admin bị xóa còn phụ trách
            List<TargetValueAndAdminDetail> lstTargetValueAndAdminDetail = getListTargetValueDetailByTargetIdAndObjectId(target.getId(),
                objectId, objectType);
            // lấy tổng SPDV admin phụ trách
            Map<Long, Long> adminIdToAssignedServiceQuantity = new HashMap<>();
            for (TargetValueAndAdminDetail targetValue : lstTargetValueAndAdminDetail) {
                adminIdToAssignedServiceQuantity.put(targetValue.getAdminId(),
                    targetValueRepository.countByTargetIdAndAdminId(target.getId(), targetValue.getAdminId()));
            }

            targetValueRepository.deleteAll(lstTargetValue);

            // Đói tượng PV + NVKD: gửi mail xóa nếu PV/NVKD bị xóa
            Thread sendMailThread = new Thread(
                () -> sendMailDeleteRevenueTargetValue(target, lstTargetValueAndAdminDetail, objectType, adminIdToAssignedServiceQuantity));
            sendMailThread.start();

        }
        log.info("====================== end method deleteTargetValue ======================");
    }

    private void sendMailDeleteRevenueTargetValue(RevenueTarget target, List<TargetValueAndAdminDetail> lstTargetValueAndAdminDetail,
        TargetObjectTypeEnum objectType, Map<Long, Long> adminIdToAssignedServiceQuantity) {
        List<ActionNotificationTemplateBase> lstParamDTO = new ArrayList<>();
        log.info("====================== start thread sendMailDeleteRevenueTargetValue ======================");
        switch(objectType) {
            case ADMIN:
                lstParamDTO.add(new DT12(lstTargetValueAndAdminDetail, target));
                break;
            case PARTITION:
                lstParamDTO.add(new DT11(lstTargetValueAndAdminDetail, target));
                break;
            case PRODUCT:
                // list admin nhận thư chỉnh sửa
                List<TargetValueUpdateContent> lstEditedAdmin = new ArrayList<>();

                // list admin nhận thư xóa
                List<TargetValueAndAdminDetail> lstDeletedAdmin = new ArrayList<>();

                // trước khi xóa, check với mỗi admin:
                for (Map.Entry<Long, Long> entry : adminIdToAssignedServiceQuantity.entrySet()) {
                    // lấy tất cả targetValueDetail của admin hiện tại
                    List<TargetValueAndAdminDetail> lstCurrentAdminTargetValue = lstTargetValueAndAdminDetail.stream().filter(detail ->
                        Objects.equals(detail.getAdminId(), entry.getKey())).collect(Collectors.toList());

                    if (entry.getValue() > 1) { // nếu admin vẫn phụ trách tổng cộng nhiều hơn 1 SPDV --> gửi thư chỉnh sửa
                        // lưu các param của template nội dung chỉnh sửa
                        Map<String, String> mapContent = new HashMap<>();
                        DT06.prepareRemoveProductContent(target, lstCurrentAdminTargetValue, mapContent);
                        // lấy nội dung mail chỉnh sửa + cho vào danh sách admin nhận thư chỉnh sửa
                        DT04.getLstCurrentAdminUpdatedContent(lstEditedAdmin, lstCurrentAdminTargetValue.get(0), mapContent);

                    } else { // nếu admin chỉ phụ trách tổng cộng 1 SPDV --> gửi thư xóa
                        lstDeletedAdmin.addAll(lstCurrentAdminTargetValue);
                    }
                }
                // gủi thư chinh sửa
                lstParamDTO.add(new DT06(target, lstEditedAdmin, webHost));
                // gửi thư xóa
                lstParamDTO.add(new DT12(lstDeletedAdmin, target));
                break;
        }

        lstParamDTO.forEach(actionNotificationService::send);

        log.info("====================== end thread sendMailDeleteRevenueTargetValue ======================");
    }


    private void sendMailUpdateRevenueTarget(List<TargetValueAndAdminDetail> oldLstTargetValueDetail,
        List<TargetValueAndAdminDetail> newLstTargetValueDetail, TargetObjectTypeEnum objectType,
        RevenueTarget newTarget, RevenueTarget oldTarget) {
        log.info("====================== start thread sendMailUpdateRevenueTarget ======================");

        List<ActionNotificationTemplateBase> lstParamDTO = new ArrayList<>();
        List<TargetValueAndAdminDetail> lstAddedTargetValue = DT04.getLstEditedTargetValue(oldLstTargetValueDetail, newLstTargetValueDetail, objectType);
        List<TargetValueAndAdminDetail> lstRemovedTargetValue = DT04.getLstEditedTargetValue(newLstTargetValueDetail, oldLstTargetValueDetail, objectType);

        if (objectType == TargetObjectTypeEnum.PRODUCT) {
            sendMailUpdateProductTarget(oldLstTargetValueDetail, newLstTargetValueDetail, lstAddedTargetValue, lstRemovedTargetValue,
                newTarget, oldTarget, lstParamDTO);
        }
        else {
            sendMailUpdateAdminOrPartitionTarget(oldLstTargetValueDetail, newLstTargetValueDetail, lstAddedTargetValue, lstRemovedTargetValue,
                newTarget, oldTarget, lstParamDTO);
        }

        lstParamDTO.forEach(actionNotificationService::send);

        log.info("====================== end thread sendMailUpdateRevenueTarget ======================");
    }

    private void sendMailUpdateProductTarget(List<TargetValueAndAdminDetail> oldLstTargetValueDetail,
        List<TargetValueAndAdminDetail> newLstTargetValueDetail, List<TargetValueAndAdminDetail> lstAddedTargetValue,
        List<TargetValueAndAdminDetail> lstRemovedTargetValue, RevenueTarget newTarget, RevenueTarget oldTarget,
        List<ActionNotificationTemplateBase> lstParamDTO) {

        // Lay danh sach cac admin van phu trach DTMT
        newLstTargetValueDetail.removeIf(detail -> lstAddedTargetValue.contains(detail) || lstRemovedTargetValue.contains(detail));
        oldLstTargetValueDetail.removeIf(detail -> lstAddedTargetValue.contains(detail) || lstRemovedTargetValue.contains(detail));

        // lưu update content cho mỗi admin
        List<TargetValueUpdateContent> lstCurrentAdminUpdatedContent = new ArrayList<>();

        // lấy danh sách tất cả admin Id vẫn phụ trách DTMT
        Set<Long> lstAdminId = newLstTargetValueDetail.stream().map(TargetValueAndAdminDetail::getAdminId).collect(Collectors.toSet());

        // Với mỗi admin:
        for (Long adminId : lstAdminId) {
            // lấy tất cả targetValueDetail cũ và mới của admin hiện tại
            List<TargetValueAndAdminDetail> lstNewDetail = newLstTargetValueDetail.stream().filter(detail ->
                Objects.equals(detail.getAdminId(), adminId)).collect(Collectors.toList());
            List<TargetValueAndAdminDetail> lstOldDetail = oldLstTargetValueDetail.stream().filter(detail ->
                Objects.equals(detail.getAdminId(), adminId)).collect(Collectors.toList());

            // lưu thông tin của admin
            TargetValueAndAdminDetail currentAdminDetail = lstNewDetail.get(0);

            // lưu các param của template nội dung chỉnh sửa
            Map<String, String> mapContent = new HashMap<>();
            // Thêm nội dung chỉnh sửa chung của target
            DT04.prepareCommonUpdateContent(newTarget, oldTarget, mapContent);

            // bổ sung nội dung update mail
            DT06.prepareProductUpdateContent(newTarget, oldTarget, lstNewDetail, lstOldDetail, mapContent);

            // lấy ra danh sách nội dung chỉnh sửa
            DT04.getLstCurrentAdminUpdatedContent(lstCurrentAdminUpdatedContent, currentAdminDetail, mapContent);
        }
        // Gửi thư chỉnh sửa
        lstParamDTO.add(new DT06(newTarget, lstCurrentAdminUpdatedContent, webHost));

        // Gửi thư gán DTMT cho nhân sự mới đc thêm vào DTMT (chưa từng phụ trách SPDV nào trong DTMT)
        lstParamDTO.add(new DT03(lstAddedTargetValue, newTarget, webHost));

        // Gửi thư xóa cho NVKD ko còn phụ trách SPDV nào nữa
        lstParamDTO.add(new DT12(lstRemovedTargetValue, oldTarget));
    }

    private void sendMailUpdateAdminOrPartitionTarget(List<TargetValueAndAdminDetail> oldLstTargetValueDetail,
        List<TargetValueAndAdminDetail> newLstTargetValueDetail, List<TargetValueAndAdminDetail> lstAddedTargetValue,
        List<TargetValueAndAdminDetail> lstRemovedTargetValue, RevenueTarget newTarget, RevenueTarget oldTarget,
        List<ActionNotificationTemplateBase> lstParamDTO) {

        // Lay danh sach cac admin van phu trach DTMT
        newLstTargetValueDetail.removeIf(detail -> lstAddedTargetValue.contains(detail) || lstRemovedTargetValue.contains(detail));
        oldLstTargetValueDetail.removeIf(detail -> lstAddedTargetValue.contains(detail) || lstRemovedTargetValue.contains(detail));

        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(newTarget.getObjectType());

        sendMailUpdateTarget(oldLstTargetValueDetail, newLstTargetValueDetail, newTarget, oldTarget, lstParamDTO, objectType);

        // Gửi thư gán DTMT cho nhân sự / phân vùng mới đc thêm vào && Gửi thư xóa DTMT cho nhân sự / phân vùng bị xóa
        switch (objectType){
            case ADMIN:
                lstParamDTO.add(new DT02(lstAddedTargetValue, newTarget, webHost));
                lstParamDTO.add(new DT12(lstRemovedTargetValue, newTarget));
                break;
            case PARTITION:
                lstParamDTO.add(new DT01(lstAddedTargetValue, newTarget, webHost));
                lstParamDTO.add(new DT11(lstRemovedTargetValue, newTarget));
                break;
        }
    }

    // dùng cho đối tượng admin && phân vùng
    private void sendMailUpdateTarget(List<TargetValueAndAdminDetail> oldLstTargetValueDetail,
        List<TargetValueAndAdminDetail> newLstTargetValueDetail, RevenueTarget newTarget, RevenueTarget oldTarget,
        List<ActionNotificationTemplateBase> lstParamDTO, TargetObjectTypeEnum objectType) {

        // lưu update content cho mỗi admin
        List<TargetValueUpdateContent> lstCurrentAdminUpdatedContent = new ArrayList<>();

        // với mỗi admin (target value):
        for (TargetValueAndAdminDetail newTargetValueDetail : newLstTargetValueDetail) {
            // lấy bản cũ của targetValue tương ứng
            TargetValueAndAdminDetail oldTargetValueDetail = new TargetValueAndAdminDetail();
            switch (objectType) {
                case ADMIN:
                   oldTargetValueDetail = oldLstTargetValueDetail.stream().filter(oldDetail ->
                        Objects.equals(oldDetail.getAdminId(), newTargetValueDetail.getAdminId())).findFirst().orElse(null);
                    break;
                case PARTITION:
                    oldTargetValueDetail = oldLstTargetValueDetail.stream().filter(oldDetail ->
                        Objects.equals(oldDetail.getPartitionId(), newTargetValueDetail.getPartitionId())).findFirst().orElse(null);
                    break;
            }
            Map<String, String> mapContent = new HashMap<>();

            // bổ sung nội dung update mail
            if (Objects.nonNull(oldTargetValueDetail)){
                DT04.prepareMapContent(newTarget, oldTarget, newTargetValueDetail.getTargetValue(),
                    oldTargetValueDetail.getTargetValue(), mapContent);
                DT04.getLstCurrentAdminUpdatedContent(lstCurrentAdminUpdatedContent, newTargetValueDetail, mapContent);
            }
        }

        // gui notif cho nhan vien kinh doanh
        if (!lstCurrentAdminUpdatedContent.isEmpty()) {
            switch (objectType) {
                case ADMIN:
                    lstParamDTO.add(new DT05(newTarget, lstCurrentAdminUpdatedContent, webHost));
                    break;
                case PARTITION:
                    lstParamDTO.add(new DT04(newTarget, lstCurrentAdminUpdatedContent, webHost));
                    break;
            }
        }
    }

    private List<TargetValueAndAdminDetail> getListTargetValueDetailByTargetIdAndObjectId(Long targetId, Long objectId, TargetObjectTypeEnum objectType) {
        List<ITargetValueAndAdminDetail> lstTargetValueDetail = new ArrayList<>();
        switch (objectType) {
            case PARTITION:
                lstTargetValueDetail = targetValueRepository.getPartitionTargetValueDetailByTargetIdAndPartitionId(targetId, objectId);
                break;
            case PRODUCT:
                lstTargetValueDetail = targetValueRepository.getProductTargetValueDetailByTargetIdAndServiceId(targetId, objectId);
                break;
            case ADMIN:
                lstTargetValueDetail = targetValueRepository.getAdminTargetValueDetailByTargetIdAndAdminId(targetId, objectId);
                break;
        }

        return lstTargetValueDetail.stream().map(adminDetail ->
            {
                TargetValueAndAdminDetail targetValueAndAdminDetail = new TargetValueAndAdminDetail();
                BeanUtils.copyProperties(adminDetail, targetValueAndAdminDetail);
                return targetValueAndAdminDetail;
            }
        ).collect(Collectors.toList());
    }

    private List<TargetValueAndAdminDetail> getListTargetValueDetailByTargetId(Long targetId, TargetObjectTypeEnum objectType) {
        List<ITargetValueAndAdminDetail> lstTargetValueDetail = new ArrayList<>();
        switch (objectType) {
            case PARTITION:
                lstTargetValueDetail = targetValueRepository.getPartitionTargetValueDetailByTargetId(targetId);
                break;
            case PRODUCT:
                lstTargetValueDetail = targetValueRepository.getProductTargetValueDetailByTargetId(targetId);
                break;
            case ADMIN:
                lstTargetValueDetail = targetValueRepository.getAdminTargetValueDetailByTargetId(targetId);
                break;
        }

        return lstTargetValueDetail.stream().map(adminDetail ->
            {
                TargetValueAndAdminDetail targetValueAndAdminDetail = new TargetValueAndAdminDetail();
                BeanUtils.copyProperties(adminDetail, targetValueAndAdminDetail);
                return targetValueAndAdminDetail;
            }
        ).collect(Collectors.toList());
    }

    // So sánh bằng giữa hai biến Long
    private boolean isEqual(Long first, Long second) {
        if (first == null) {
            return second == null;
        } else {
            return Objects.equals(first, second);
        }
    }

    // Generate mã doanh thu mục tiêu
    private String generateTargetCode() {
        Random random = new Random();
        String code;
        do {
            code = String.format("DT%06d", random.nextInt(1000000));
        } while (targetRepository.existsByCode(code));
        return code;
    }

    @Override
    public Page<RevenueTargetListDTO> getListRevenueTarget(
            List<Long> lstTargetId, List<String> lstTargetCode, String targetName, IntervalTypeEnum intervalType,
            TargetObjectTypeEnum objectType, Long objectId, List<Long> lstPartitionId, Date startDate, Date endDate,
            Pageable pageable, String value, Integer isTargetCode, Integer isTargetName) {
        List<IGetTargetFilterDTO> lstFilteredTargetId = targetRepository.getListTargetId(lstTargetId, lstTargetCode, targetName,
                intervalType.getValue(), objectType.getValue(), objectId, startDate, endDate, value, isTargetCode, isTargetName);
        // Get ViewerDetail
        ViewerDetailDTO viewerDetail = adminPermissionUtil.getViewerDetail(AuthUtil.getCurrentUserId(), CrmPermissionEnum.VIEW.getValue());
        // Lọc theo phân vùng
        filterListTargetByPartition(lstFilteredTargetId, lstPartitionId);
        // Phân quyền DS DTMT
        filterListTargetByPermission(lstFilteredTargetId, viewerDetail);
        int start = Math.min((int) pageable.getOffset(), lstFilteredTargetId.size());
        int end = Math.min((start + pageable.getPageSize()), lstFilteredTargetId.size());
        List<Long> lstViewableTargetId = lstFilteredTargetId.subList(start, end).stream().map(IGetTargetFilterDTO::getTargetId)
            .collect(Collectors.toList());
        // Map dữ liệu DTMT sau khi phân quyền
        Map<Long, TargetValueDTO> mapTargetValue = getActualRevenueTarget(lstFilteredTargetId.subList(start, end), viewerDetail);
        List<RevenueTargetListDTO> response = targetRepository.getListRevenueTarget(lstViewableTargetId).stream().map(item -> {
            RevenueTargetListDTO target = new RevenueTargetListDTO(item);
            target.setTargetValue(mapTargetValue.get(item.getTargetId()).getTargetValue());
            target.setActualValue(mapTargetValue.get(item.getTargetId()).getActualValue());
            return target;
        }).collect(Collectors.toList());
        return new PageImpl<>(response, pageable, lstFilteredTargetId.size());
    }

    @Override
    public List<GetListObjectRevenueTarget> getListPartitionRevenueTarget(
            List<Long> lstPartitionId, List<Long> lstTargetId, List<String> lstTargetCode, String targetName, IntervalTypeEnum intervalType,
            Date startDate, Date endDate, String value, Integer isTargetCode, Integer isTargetName) {
        BigDecimal[] partitionIdsArr = lstPartitionId.stream().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);
        return getListActualValue(targetRepository.getListPartitionRevenueTarget(partitionIdsArr, lstTargetId, lstTargetCode,
                targetName, intervalType.getValue(), startDate, endDate, value, isTargetCode, isTargetName));
    }

    @Override
    public Page<GetListObjectRevenueTarget> getListAdminRevenueTarget(
            List<Long> lstTargetId, List<String> lstTargetCode, String targetName, List<Long> lstAdminId, List<String> lstAdminCode,
            List<String> lstAdminEmail, List<String> lstAdminPhone, List<Long> lstPartitionId, IntervalTypeEnum intervalType,
            Date startDate, Date endDate, Pageable pageable, String value, Integer isTargetName, Integer isTargetCode, Integer isAdminName, Integer isAdminCode) {
        BigDecimal[] partitionIdsArr = lstPartitionId.stream().map(BigDecimal::valueOf).distinct().toArray(BigDecimal[]::new);
        // Lấy danh sách DTMT của NVKD thỏa mãn các bộ lọc
        List<IGetListObjectRevenueTarget> lstAdminTarget = targetRepository.getListAdminRevenueTarget(partitionIdsArr, lstAdminId, lstAdminCode,
            lstAdminEmail, lstAdminPhone, lstTargetId, lstTargetCode, targetName, intervalType.getValue(), startDate, endDate,
                value, isTargetName, isTargetCode, isAdminName, isAdminCode);

        // Lọc các DTMT của các NVKD mà user đang đăng nhập có quyền VIEW
        Long currentUserId = AuthUtil.getCurrentUserId();
        ViewerDetailDTO viewerDetailDTO = adminPermissionUtil.getViewerDetail(currentUserId, CrmPermissionEnum.VIEW.getValue());
        List<IGetListObjectRevenueTarget> lstViewableAdminTarget = viewerDetailDTO.getIsSuperAdmin() ? lstAdminTarget : lstAdminTarget.stream()
            .filter(adminTarget -> adminPermissionUtil.hasPermission(currentUserId, adminTarget.getObjectId(), viewerDetailDTO))
            .collect(Collectors.toList());
        int start = Math.min((int) pageable.getOffset(), lstViewableAdminTarget.size());
        int end = Math.min((start + pageable.getPageSize()), lstViewableAdminTarget.size());
        return new PageImpl<>(getListActualValue(lstViewableAdminTarget.subList(start, end)), pageable, lstViewableAdminTarget.size());
    }

    @Override
    public Page<GetListObjectRevenueTarget> getListProductRevenueTarget(
            List<Long> lstTargetId, List<String> lstTargetCode, String targetName, List<Long> lstServiceId, List<Long> lstCategoryId,
            String serviceOwner, IntervalTypeEnum intervalType, Date startDate, Date endDate, Pageable pageable,
            String value, Integer isTargetName, Integer isTargetCode, Integer isServiceName) {
        // Lấy danh sách DTMT SPDV thỏa mãn bộ lọc
        List<IGetListObjectRevenueTarget> lstProductTarget = targetRepository.getListProductRevenueTarget(lstServiceId, lstCategoryId, serviceOwner,
            lstTargetId, lstTargetCode, targetName, intervalType.getValue(), startDate, endDate, value, isTargetName, isTargetCode, isServiceName);

        // Lọc các DTMT SPDV mà user đăng nhập có quyền VIEW ( phân quyền theo quan hệ user đăng nhập với NVPT SPDV - assignee)
        Long currentUserId = AuthUtil.getCurrentUserId();
        ViewerDetailDTO viewerDetailDTO = adminPermissionUtil.getViewerDetail(currentUserId, CrmPermissionEnum.VIEW.getValue());
        List<IGetListObjectRevenueTarget> lstViewableProductTarget = viewerDetailDTO.getIsSuperAdmin() ? lstProductTarget : lstProductTarget.stream()
            .filter(productTarget -> {
                List<Long> lstAssigneeId = StringUtil.convertStringArrToListLong(productTarget.getLstAssigneeId());
                return !lstAssigneeId.isEmpty()
                        && lstAssigneeId.stream().anyMatch(assigneeId -> adminPermissionUtil.hasPermission(currentUserId, assigneeId, viewerDetailDTO));
            }).collect(Collectors.toList());
        int start = Math.min((int) pageable.getOffset(), lstViewableProductTarget.size());
        int end = Math.min((start + pageable.getPageSize()), lstViewableProductTarget.size());

        // Lấy doanh thu thực tế DTMT của các SPDV (gộp các targetValue của SPDV ứng với NSPT)
        List<GetListObjectRevenueTarget> lstTargetResponse = lstViewableProductTarget.subList(start, end).stream().map(item -> {
            GetListObjectRevenueTarget productTarget = new GetListObjectRevenueTarget(item);
            List<Long> lstTargetValueId = StringUtil.convertStringArrToListLong(item.getLstTargetValueId());
            Long actualValue = targetRepository.getListActualRevenueObject(lstTargetValueId).stream()
                .map(IGetTargetIdDTO::getActualValue).reduce(0L, Long::sum);
            productTarget.setActualValue(actualValue);
            return productTarget;
        }).collect(Collectors.toList());
        return new PageImpl<>(lstTargetResponse, pageable, lstViewableProductTarget.size());
    }

    @Override
    public IRevenueTargetDetailDTO getDetailRevenueTarget(Long revenueId) {
        RevenueTarget target = getRevenueTargetById(revenueId);
        TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(target.getObjectType());

        switch(objectType) {
            case PARTITION:
                return targetRepository.getRevenueTargetDetailPartition(revenueId);
            case ADMIN:
                return targetRepository.getRevenueTargetDetailAdmin(revenueId);
            default:
                return targetRepository.getRevenueTargetDetailProduct(revenueId);
        }
    }

    @Override
    public ObjectDetailRevenueTarget getDetailPartitionRevenueTarget(Long partitionId) {
        IObjectDetailRevenueTarget partitionDetail =
            targetRepository.getDetailPartitionAndServiceRevenueTarget(TargetObjectTypeEnum.PARTITION.getValue(), partitionId);
        ObjectDetailRevenueTarget responseDTO = new ObjectDetailRevenueTarget();

        if (Objects.nonNull(partitionDetail)) {
            BeanUtils.copyProperties(partitionDetail, responseDTO);
            setTotalSubValueAndQuantity(TargetObjectTypeEnum.PARTITION, partitionId, responseDTO);
        }
        return responseDTO;
    }

    @Override
    public ObjectDetailRevenueTarget getDetailServiceRevenueTarget(Long serviceId) {
        IObjectDetailRevenueTarget productDetail =
            targetRepository.getDetailPartitionAndServiceRevenueTarget(TargetObjectTypeEnum.PRODUCT.getValue(), serviceId);
        ObjectDetailRevenueTarget responseDTO = new ObjectDetailRevenueTarget();

        if (Objects.nonNull(productDetail)) {
            BeanUtils.copyProperties(productDetail, responseDTO);
            setTotalSubValueAndQuantity(TargetObjectTypeEnum.PRODUCT, serviceId, responseDTO);
        }
        return responseDTO;
    }

    @Override
    public AdminDetailRevenueTarget getDetailAdminRevenueTarget(Long adminId) {
        IAdminRevenueDetail adminDetail = targetRepository.getDetailAdminRevenueTarget(adminId);
        AdminDetailRevenueTarget responseDTO = new AdminDetailRevenueTarget();

        if (Objects.nonNull(adminDetail)) {
            BeanUtils.copyProperties(adminDetail, responseDTO);

            responseDTO.setTotalTargetSubValue(
                targetRepository.getTotalTargetSubValueByTargetTypeAndObjectId(TargetObjectTypeEnum.ADMIN.getValue(),
                    TargetTypeEnum.VALUE.getValue(), adminId));

            responseDTO.setTotalActualSubValue(
                targetRepository.getTotalActualSubValueByTargetTypeAndObjectId(TargetObjectTypeEnum.ADMIN.getValue(),
                    TargetTypeEnum.VALUE.getValue(), adminId));

            responseDTO.setTotalTargetSubQuantity(
                targetRepository.getTotalTargetSubValueByTargetTypeAndObjectId(TargetObjectTypeEnum.ADMIN.getValue(),
                    TargetTypeEnum.QUANTITY.getValue(), adminId));

            responseDTO.setTotalActualSubQuantity(
                targetRepository.getTotalActualSubValueByTargetTypeAndObjectId(TargetObjectTypeEnum.ADMIN.getValue(),
                    TargetTypeEnum.QUANTITY.getValue(), adminId));
        }
        return responseDTO;
    }

    @Override
    public Page<DetailProductListDTO> getDetailProductList(Long targetId, String serviceName, Pageable pageable) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        List<IDetailProductListDTO> lstProductDetails = targetRepository.getDetailProductList(targetId, serviceName);
        ViewerDetailDTO viewerDetailDTO = adminPermissionUtil.getViewerDetail(currentUserId, CrmPermissionEnum.VIEW.getValue());
        // Loại bỏ những DTMT SPDV mà user đăng nhập không có quyền xem
        if (!viewerDetailDTO.getIsSuperAdmin()) {
            lstProductDetails.removeIf(item -> {
                List<Long> lstAssigneeId = StringUtil.convertStringArrToListLong(item.getLstAssigneeId());
                boolean hasPermission = !lstAssigneeId.isEmpty()
                    && lstAssigneeId.stream().anyMatch(assingeeId -> adminPermissionUtil.hasPermission(currentUserId, assingeeId, viewerDetailDTO));
                return !hasPermission;
            });
        }
        // Phân quyền chi tiết dữ liệu NSPT và doanh thu thực tế
        List<DetailProductListDTO> lstResponse = lstProductDetails.stream().map(item -> {
            // Lọc các targetValue DTMT của 1 sản phẩm dịch vụ có NSPT mà user đang đăng nhập có quyền xem
            List<RevenueTargetValue> lstViewableTargetValue = targetValueRepository.findAllByIdIn(StringUtil.convertStringArrToListLong(item.getLstTargetValueId()));
            if(!viewerDetailDTO.getIsSuperAdmin()) {
                lstViewableTargetValue.removeIf(targetValue -> !adminPermissionUtil.hasPermission(currentUserId, targetValue.getAdminId(), viewerDetailDTO));
            }
            List<Long> lstViewableTargetValueId = lstViewableTargetValue.stream().map(RevenueTargetValue::getId).collect(Collectors.toList());

            // Tính tổng doanh thu thực tế theo các NSPT mà user đang đăng nhập có quyền xem
            Long actualValue = targetRepository.getListActualRevenueObject(lstViewableTargetValueId).stream()
                .map(IGetTargetIdDTO::getActualValue).reduce(0L, Long::sum);
            // Lấy thông tin NSPT với doanh thu thực tế
            List<IListProductAdmin> lstProductAdmin = targetRepository.getListProductAdmin(lstViewableTargetValueId);
            DetailProductListDTO detailProductList = new DetailProductListDTO();
            BeanUtils.copyProperties(item, detailProductList);
            detailProductList.setActualValue(actualValue);
            detailProductList.setLstProductAdmin(lstProductAdmin);
            return detailProductList;
        }).collect(Collectors.toList());
        int start = Math.min((int) pageable.getOffset(), lstResponse.size());
        int end = Math.min((start + pageable.getPageSize()), lstResponse.size());
        return new PageImpl<>(lstResponse.subList(start, end), pageable, lstResponse.size());
    }

    @Override
    public Page<IListProductAdmin> getPageProductAdmin(Long targetId, Long serviceId, List<Long> lstAdminId, String adminName, String adminCode, String email, Pageable pageable) {
        return targetRepository.getPageProductAdmin(targetId, serviceId, lstAdminId, adminName, adminCode, email, pageable);
    }

    @Override
    public List<DetailPartitionListDTO> getDetailPartitionList(Long targetId, String partitionName) {
        ViewerDetailDTO viewerDetail = adminPermissionUtil.getViewerDetail(AuthUtil.getCurrentUserId(), CrmPermissionEnum.VIEW.getValue());
        List<IDetailPartitionListDTO> lstTargetPartition = targetRepository.getDetailPartitionList(targetId, partitionName);
        Long currentUserId = AuthUtil.getCurrentUserId();
        // Lọc nhữ targetValue có partition mà user đăng nhập không có quyền VIEW
        Set<Long> lstViewablePartitionId = dataPartitionService.getLstAdminViewablePartitionId(currentUserId);
        if (!viewerDetail.getIsSuperAdmin()) {
            lstTargetPartition.removeIf(item -> !lstViewablePartitionId.contains(item.getObjectId()) && !Objects.equals(currentUserId, item.getCreatedBy()));
        }
        // Map dữ liệu và tính toán doanh thu thực tế theo logic phân quyền sub
        Set<Long> allTargetValueId = lstTargetPartition.stream().map(IDetailPartitionListDTO::getTargetValueId).collect(Collectors.toSet());
        List<IGetListActualSubId> allActualSub = targetRepository.getListTargetActualSubId(allTargetValueId);
        return lstTargetPartition.stream().map(item -> {
            DetailPartitionListDTO partitionTarget = new DetailPartitionListDTO(item);
            List<IGetListActualSubId> lstActualSub = allActualSub.stream()
                .filter(sub -> Objects.equals(sub.getTargetValueId(), item.getTargetValueId())).collect(Collectors.toList());
            if (!viewerDetail.getIsSuperAdmin()) {
                lstActualSub.removeIf(sub -> !adminPermissionUtil.hasPermission(AuthUtil.getCurrentUserId(), sub.getAssigneeId(), viewerDetail));
            }
            Long actualValue = Objects.equals(TargetTypeEnum.valueOf(item.getTargetType()), TargetTypeEnum.VALUE) ? lstActualSub.stream()
                .map(IGetListActualSubId::getPreAmountTax).reduce(0L, Long::sum) : (long) lstActualSub.size();
            partitionTarget.setActualValue(actualValue);
            return partitionTarget;
        }).collect(Collectors.toList());
    }

    private void sendMailDeleteRevenueTarget(RevenueTarget target, List<TargetValueAndAdminDetail> lstTargetValueAndAdminDetail,
        TargetObjectTypeEnum objectType) {
        log.info("====================== start thread sendMailDeleteRevenueTarget ======================");

        List<ActionNotificationTemplateBase> lstParamDTO = new ArrayList<>();

        if (objectType == TargetObjectTypeEnum.PARTITION) {
            // gui notif cho quan ly phan vung
            lstParamDTO.add(new DT11(lstTargetValueAndAdminDetail, target));
        } else {
            // gui notif cho tung NVKD
            lstParamDTO.add(new DT12(lstTargetValueAndAdminDetail, target));
        }

        lstParamDTO.forEach(actionNotificationService::send);

        log.info("====================== start thread sendMailDeleteRevenueTarget ======================");
    }

    // Lấy cấu hình ngày gửi thông báo doanh thu mục tiêu
    private Integer getReminderDateMinus(IntervalTypeEnum type){
        SystemParam reminderConfig = systemParamService.findByParamType(SystemParamTypeEnum.REMINDER_DATE_TARGET_REVENUE_CONFIG.getValue());
        try {
            ReminderDateConfigDTO config = ObjectMapperUtil.mapping(reminderConfig.getParamTextValue(), ReminderDateConfigDTO.class);
            if (config == null) return 0;
            switch (type) {
                case WEEK:
                    return config.getWeekValue();
                case MONTH:
                    return config.getMonthValue();
                case QUARTER:
                    return config.getQuarterValue();
                case YEAR:
                    return config.getYearValue();
                default:
                    return 0;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return 0;
    }

    // Tính ngày thông báo dựa trên cấu hình ngày thông báo trong hệ thống
    private LocalDate calculateReminderDate(RevenueTarget target) {
        return target.getEndDate().minusDays(getReminderDateMinus(IntervalTypeEnum.fromValue(target.getIntervalType())));
    }

    // Cập nhật reminder_date khi thay đổi cấu hình
    public void updateReminderDate() {
        IntervalTypeEnum[] lstIntervalType = {IntervalTypeEnum.WEEK, IntervalTypeEnum.MONTH, IntervalTypeEnum.QUARTER, IntervalTypeEnum.YEAR};
        for (IntervalTypeEnum intervalType : lstIntervalType) {
            targetRepository.updateReminderDate(intervalType.getValue(), getReminderDateMinus(intervalType));
        }
    }

    @Override
    public void changeSubTypeConfig(List<TargetSubTypeEnum> lstTargetSubType) {
        SystemParam subTypeConfig = systemParamService.findByParamType(SystemParamTypeEnum.SUBSCRIPTION_TYPE_REVENUE_TARGET_CONFIG.getValue());
        List<String> lstSubTypeValue = lstTargetSubType.stream().map(subType -> String.valueOf(subType.getValue())).collect(Collectors.toList());
        subTypeConfig.setParamTextValue(String.join(",", lstSubTypeValue));
        systemParamService.save(subTypeConfig);
    }

    private void setTotalSubValueAndQuantity(TargetObjectTypeEnum objectType, Long objectId, ObjectDetailRevenueTarget responseDTO) {

        responseDTO.setTotalTargetSubValue(
            targetRepository.getTotalTargetSubValueByTargetTypeAndObjectId(objectType.getValue(), TargetTypeEnum.VALUE.getValue(), objectId));

        responseDTO.setTotalActualSubValue(
            targetRepository.getTotalActualSubValueByTargetTypeAndObjectId(objectType.getValue(), TargetTypeEnum.VALUE.getValue(), objectId));

        responseDTO.setTotalTargetSubQuantity(
            targetRepository.getTotalTargetSubValueByTargetTypeAndObjectId(objectType.getValue(), TargetTypeEnum.QUANTITY.getValue(), objectId));

        responseDTO.setTotalActualSubQuantity(
            targetRepository.getTotalActualSubValueByTargetTypeAndObjectId(objectType.getValue(), TargetTypeEnum.QUANTITY.getValue(), objectId));
    }

    private RevenueTarget getRevenueTargetById(Long targetId) {
        if (targetId == null) {
            throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.REVENUE_TARGET, ErrorKey.ID);
        }
        return targetRepository.findById(targetId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.REVENUE_TARGET, ErrorKey.ID, String.valueOf(targetId)));
    }

    private List<GetListObjectRevenueTarget> getListActualValue(List<IGetListObjectRevenueTarget> lstViewableProductTarget) {
        List<GetListObjectRevenueTarget> lstViewableProductTargetRes = lstViewableProductTarget.stream().map(GetListObjectRevenueTarget::new).collect(
            Collectors.toList());
        List<IGetTargetIdDTO> lstGetTargetIdDTOS = targetRepository.getListActualRevenueObject(lstViewableProductTarget.stream()
            .map(IGetListObjectRevenueTarget::getTargetValueId).collect(Collectors.toList()));

        Map<Long, IGetTargetIdDTO> targetIdMap = lstGetTargetIdDTOS.stream().collect(Collectors.toMap(IGetTargetIdDTO::getTargetValueId, Function.identity()));
        lstViewableProductTargetRes.forEach(value -> {
            IGetTargetIdDTO iGetTargetIdDTO = targetIdMap.get(value.getTargetValueId());
            Long actualValue = iGetTargetIdDTO == null ? 0 : iGetTargetIdDTO.getActualValue();
            value.setActualValue(actualValue);
        });
        return lstViewableProductTargetRes;
    }

    /**
     * Lọc danh sách doanh thu mục tiêu theo danh sách phân vùng
     */
    private void filterListTargetByPartition(List<IGetTargetFilterDTO> lstTarget, List<Long> lstPartitionId){
        if(lstPartitionId.contains(-1L)) return;
        // Lấy danh sách id của admin và am của tất cả các phân vùng trong danh sách lstPartitionId
        Set<Long> lstAdminAmId = dataPartitionRepository.getListAdminAmIdInPartitions(lstPartitionId); // list admin/am của các phân vùng lọc
        // xóa các target không thỏa mãn điều kiện lọc phân vùng
        lstTarget.removeIf(target -> {
            boolean isMatched = false; // cờ kiểm tra điều kiện lọc phân vùng với mỗi DTMT
            TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(target.getObjectType());
            Set<Long> lstPartitionObjectId = new HashSet<>(StringUtil.convertStringArrToListLong(target.getLstPartitionObjectId()));
            switch (objectType) {
                case PRODUCT:
                case ADMIN:
                    isMatched = !lstPartitionObjectId.isEmpty() && lstAdminAmId.containsAll(lstPartitionObjectId);
                    break;
                case PARTITION:
                    isMatched = !lstPartitionObjectId.isEmpty() && lstPartitionObjectId.containsAll(lstPartitionId);
                    break;
            }
            return !isMatched;
        });
    }

    /**
     * Phân quyền dữ liệu DS DTMT
     */
    private void filterListTargetByPermission(List<IGetTargetFilterDTO> lstTarget, ViewerDetailDTO viewerDetail){
        if (AuthUtil.isSuperAdmin()) {
            return;
        }
        Long currentUserId = AuthUtil.getCurrentUserId();
        // Lấy các phân vùng mà user đăng nhập có quyền view
        Set<Long> lstViewablePartitionId = dataPartitionService.getLstAdminViewablePartitionId(currentUserId);

        // xóa các DTMT mà user đăng nhập không có quyền VIEW với các loại đối tượng
        lstTarget.removeIf(target -> {
            boolean hasPermission = false; // cờ check quyền VIEW với DTMT
            TargetObjectTypeEnum objectType = TargetObjectTypeEnum.valueOf(target.getObjectType());
            Set<Long> lstPartitionObjectId = new HashSet<>(StringUtil.convertStringArrToListLong(target.getLstPartitionObjectId()));
            switch (objectType) {
                case PARTITION:
                    // Quyền VIEW PV = Người tạo hoặc DTMT có chứa ít nhất phân vùng mà user đăng nhập có quyền VIEW
                    hasPermission =  target.getCreatedBy().equals(currentUserId)
                        || lstPartitionObjectId.stream().anyMatch(lstViewablePartitionId::contains);
                    break;
                case PRODUCT:
                case ADMIN:
                    // Quyền VIEW = Người tạo hoặc DTMT được gán cho ít nhất 1 NVKD mà user đăng nhập có quyền VIEW
                    hasPermission = target.getCreatedBy().equals(currentUserId)
                        || lstPartitionObjectId.stream().anyMatch(adminId -> adminPermissionUtil.hasPermission(currentUserId, adminId, viewerDetail));
                    break;
            }
            return !hasPermission;
        });
    }

    @Override
    public Page<SubscriptionsReportResDTO> getListTargetActualRevenueSource(Integer isTarget, Set<Long> objectIds, Long provinceId,
        String customerName,
        String customerEmail, Pageable pageable) {
        String lstObjectId = Objects.nonNull(objectIds) && !objectIds.isEmpty()
            ? objectIds.toString().replace("[", "").replace("]", "").trim()
            : "ALL";
        // Lấy danh sách sub + ID NSPT của khách hàng đăng ký sub
        List<IGetListActualSubId> lstActualSubId = targetRepository.getListTargetActualRevenueSubId(isTarget, objectIds, provinceId,
            customerName, customerEmail);
        // Áp dụng phân quyền
        Long currentUserId = AuthUtil.getCurrentUserId();
        ViewerDetailDTO viewerDetailDTO = adminPermissionUtil.getViewerDetail(currentUserId, CrmPermissionEnum.VIEW.getValue());
        // Loại bỏ những sub mà khách hàng đăng ký có NSPT mà user đang đăng nhập không có quyền xem
        if (!viewerDetailDTO.getIsSuperAdmin()) {
            lstActualSubId.removeIf(sub -> !adminPermissionUtil.hasPermission(currentUserId, sub.getAssigneeId(), viewerDetailDTO));
        }
        List<Long> lstViewableActualSubId = lstActualSubId.stream().map(IGetListActualSubId::getSubId).collect(Collectors.toList());
        int start = Math.min((int) pageable.getOffset(), lstViewableActualSubId.size());
        int end = Math.min((start + pageable.getPageSize()), lstViewableActualSubId.size());
        List<SubscriptionsReportResDTO> lstViewableActualSubInfo = targetRepository.getListTargetActualRevenueSub(
            lstViewableActualSubId.subList(start, end));
        return new PageImpl<>(lstViewableActualSubInfo, pageable, lstViewableActualSubId.size());
    }


    /**
     * Lấy doanh thu mục tiêu và doanh thu thực tế của các doanh thu mục tiêu tổng (phục vụ logic phân quyền)
     */
    private Map<Long, TargetValueDTO> getActualRevenueTarget(List<IGetTargetFilterDTO> lstTarget, ViewerDetailDTO viewerDetail) {
        Map<Long, TargetValueDTO> mapTargetValue = new HashMap<>();
        Long currentUserId = AuthUtil.getCurrentUserId();
        // Lấy danh sách phân vùng và user đăng nhập có quyền view
        Set<Long> lstViewablePartitionId = dataPartitionService.getLstAdminViewablePartitionId(currentUserId);
        Set<Long> lstTargetId = lstTarget.stream().map(IGetTargetFilterDTO::getTargetId).collect(Collectors.toSet());
        Map<Long, List<IGetListActualSubId>> mapActualSubByTargetId = targetValueRepository.getListTargetActualSubByTargetIdIn(lstTargetId)
            .stream().collect(Collectors.groupingBy(IGetListActualSubId::getTargetId));
        List<RevenueTargetValue> lstAllTargetValue = targetValueRepository.findAllByTargetIdIn(lstTargetId);
        // Tính toán doanh thu mục tiêu/ thức tế
        for (IGetTargetFilterDTO target : lstTarget) {
            List<RevenueTargetValue> lstTargetValue = lstAllTargetValue.stream()
                .filter(targetValue -> Objects.equals(target.getTargetId(), targetValue.getTargetId()))
                .collect(Collectors.toList());
            lstTargetValue.removeIf(targetValue -> {
                switch (TargetObjectTypeEnum.valueOf(target.getObjectType())) {
                    case PARTITION:
                        return !viewerDetail.getIsSuperAdmin() && !lstViewablePartitionId.contains(targetValue.getPartitionId());
                    case PRODUCT:
                    case ADMIN:
                        return !viewerDetail.getIsSuperAdmin() &&
                            !adminPermissionUtil.hasPermission(currentUserId, targetValue.getAdminId(), viewerDetail);
                    default:
                        return false;
                }
            });
            // Tính toán doanh thu mục tiêu và doanh thu thực tế (có áp dụng phân quyền)
            Long targetValue = lstTargetValue.stream().map(item -> Objects.nonNull(item.getTargetValue()) ? item.getTargetValue() : 0)
                .reduce(0L, Long::sum);
            List<IGetListActualSubId> lstViewableSub = mapActualSubByTargetId.get(target.getTargetId());
            lstViewableSub.removeIf(item -> Objects.isNull(item.getSubId()));
            if (!viewerDetail.getIsSuperAdmin()) { // kiểm tra điều kiện super admin
                lstViewableSub.removeIf(sub -> !adminPermissionUtil.hasPermission(currentUserId, sub.getAssigneeId(), viewerDetail));
            }
            Long actualValue = Objects.equals(TargetTypeEnum.valueOf(target.getTargetType()), TargetTypeEnum.VALUE) ? lstViewableSub.stream()
                .map(IGetListActualSubId::getPreAmountTax).reduce(0L, Long::sum) : (long) lstViewableSub.size();
            mapTargetValue.put(target.getTargetId(), new TargetValueDTO(target.getTargetId(), targetValue, actualValue));
        }
        return mapTargetValue;
    }

}
