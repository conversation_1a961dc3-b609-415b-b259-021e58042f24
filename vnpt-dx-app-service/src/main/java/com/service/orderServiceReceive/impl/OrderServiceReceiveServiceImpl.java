package com.service.orderServiceReceive.impl;

import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.common.Constant;
import com.constant.ErrorConst;
import com.constant.SubscriptionConstant;
import com.constant.enums.StatusActivityLogEnum;
import com.constant.enums.orders.OrderCancelActionEnum;
import com.constant.enums.orders.SmeProgressEnum;
import com.dto.integrated.OrderServiceDHSXKDReqDTO;
import com.dto.integrated.OrderServiceDHSXKDResponseDTO;
import com.dto.integrated.request.GetListOrderServiceReqDTO;
import com.dto.integrated.response.DHSXKDResponseListOrderDTO;
import com.dto.integrated.response.DataGetListDHSXKDResponseDTO;
import com.dto.orders.CouponInfoDTO;
import com.dto.orders.request.OrderServiceListRequestDTO;
import com.dto.orders.response.InfoUserDTO;
import com.dto.orders.response.OrderServiceListResponseClassDTO;
import com.dto.orders.response.OrderServiceListResponseDTO;
import com.dto.subscriptions.combo.FilterReqDefault;
import com.dto.subscriptions.order_service.ComboOrderServiceDTO;
import com.dto.subscriptions.order_service.ComboServiceDTO;
import com.dto.subscriptions.order_service.IOrderServiceLogDTO;
import com.dto.subscriptions.order_service.OrderServiceLogDTO;
import com.dto.subscriptions.order_service.OrderServiceProcessDTO;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.Province.Province;
import com.entity.bills.Bills;
import com.entity.combo.Combo;
import com.entity.combo.ComboPlan;
import com.entity.department.Department;
import com.entity.file.attach.FileAttach;
import com.entity.historyDhsx.HistoryCallApiDHSX;
import com.entity.orderServiceReceive.OrderServiceCombo;
import com.entity.orderServiceReceive.OrderServiceReceive;
import com.entity.orderServiceReceive.OrderServiceReceiveLog;
import com.entity.orderServiceReceive.OrderServiceReceiveSendMail;
import com.entity.orderServiceReceive.SmeProgress;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.Subscription;
import com.entity.transaction_log.TransactionLog;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.model.dto.EmailTemplate;
import com.model.dto.UserInfoReciverDHSX;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.emails.ParamEmailEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.integration.PartnerCallAPI;
import com.onedx.common.constants.enums.integration.backend.AccessTradeStatusEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.integration.backend.MasOfferStatusEnum;
import com.onedx.common.constants.enums.integration.dhsxkd.DHSXKDOrderStatusEnum;
import com.onedx.common.constants.enums.integration.dhsxkd.DHSXKDPaymentStatusEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ProviderTypeEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant.ContentType;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.mail.IPricingNameServiceNameDTO;
import com.onedx.common.dto.subscriptions.orders.DHSXKDTrackingResDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceReqDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceResDTO;
import com.onedx.common.dto.users.INameAndEmailDTO;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.GsonMapperUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.Province.ProvinceRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.historyDhsx.HistoryCallApiDHSXRepository;
import com.repository.massoffer.MassofferReasonRepository;
import com.repository.orderService.OrderServiceComboRepository;
import com.repository.orderService.OrderServiceReceiveLogRepository;
import com.repository.orderService.OrderServiceReceiveRepository;
import com.repository.orderService.OrderServiceReceiveSendMailRepository;
import com.repository.orderService.SmeProgressRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.transactionLog.TransactionLogRepository;
import com.repository.users.UserRepository;
import com.service.bills.BillsService;
import com.service.email.EmailService;
import com.service.email.EmailTemplateService;
import com.service.file.attach.FileAttachService;
import com.service.integrate.IntegrationService;
import com.service.integrated.ExecutiveProducerService;
import com.service.orderServiceReceive.OrderServiceReceiveService;
import com.service.subscriptions.SubscriptionCalculateService;
import com.service.subscriptions.SubscriptionHistoryService;
import com.service.subscriptions.SubscriptionService;
import com.service.transactionLog.TransactionLogService;
import com.service.users.UserService;
import com.util.AuthUtil;
import static com.constant.integration.apigwkhcn.ApiGwKHCNConstant.SIM_SERVICE_CODE;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HiepNT
 * @version : 1.0 31/07/2021
 */
@Service
@Slf4j
public class OrderServiceReceiveServiceImpl implements OrderServiceReceiveService {

    public static final String STATUS_ID = "statusId";
    public static final String STATUS_NAME = "statusName";
    public static final int NOT_SET = -1;
    public static final Long DANG_DIEU_HANH_THI_CONG = 3L;
    public static final Long CANCEL_SUBSCRIPTION_DHSX = 7L;
    public static final String ORDER_STATUS_COMPLETED = "6";
    private static final String SERVICE = "SERVICE";

    @Autowired
    private TransactionLogService transactionLogService;
    @Autowired
    private FileAttachService fileAttachService;
    @Autowired
    private SubscriptionHistoryService subscriptionHistoryService;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private TransactionLogRepository transactionLogRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderServiceReceiveRepository orderServiceReceiveRepository;
    @Autowired
    private ExecutiveProducerService executiveProducerService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ProvinceRepository provinceRepository;
    @Autowired
    private OrderServiceReceiveLogRepository orderServiceReceiveLogRepository;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private SmeProgressRepository smeProgressRepository;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private HistoryCallApiDHSXRepository historyCallApiDHSXRepository;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private OrderServiceComboRepository orderServiceComboRepository;
    @Autowired
    private BillsService billsService;
    @Autowired
    private SubscriptionService subscriptionService;
    @Autowired
    private SubscriptionCalculateService subscriptionCalculateService;
    @Autowired
    private MassofferReasonRepository massofferReasonRepository;
    @Autowired
    private IntegrationService integrationService;
    @Autowired
    private OrderServiceReceiveSendMailRepository orderServiceReceiveSendMailRepository;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;

    @Value("${dhsxkd.url-api-get-sme-list-order}")
    private String dhsxkdApiGetListOrderService;

    private final static String CONFIG_MA_ND = "ws_smes";
    private final static Integer CONFIG_LOAI_NV_KY_THUAT = 1;// Nhân viên kỹ thuật tiếp nhận
    private final static String STR_KENH_ONESME = "Kenh:ONESME";
    private final static String STR_DA_THANH_TOAN = "1:Da thanh toan";
    private final static String STR_CHUA_THANH_TOAN = "0:Chua thanh toan";
    private final static String STR_NAME = "Tên gói: ";
    private final static String STR_PAY_STATUS = "Trạng thái thanh toán: ";
    private final static String STR_PAID = "Đã thanh toán";
    private final static String STR_UNPAID = "Chưa thanh toán";
    private final static String STR_COUPON = "Ưu đãi: ";
    private final static String STR_TOTAL_USE = "Tổng tháng sử dụng: ";
    private final static String STR_PRICE_IN_VAT = "Giá gói(có VAT): ";
    private final static String STR_PRICE_NOT_VAT = "Giá gói(không VAT): ";
    private final static String STR_TIN = "Mã số thuế của KH: ";
    private final static String STR_ADDRESS_SETUP = "Địa chỉ LĐ: ";
    private final static String STR_CUS_INTRODUCE_CODE = "Mã NVGT: ";

    private final static String STR_CUS_MANAGER_CODE = "Mã NVQL: ";
    private final static String MONTH = " tháng";
    private final String[] orderServiceReceiveMessage = {"order_service_receive"};
    private final String[] subscriptionMessage = {"subscription"};
    private final String[] orderStatusMessage = {"orderStatus"};
    private final Long VIET_NAM_ID = 1L;

    @Override
    public OrderServiceReceive createOrderServiceReceiveByApiDHSXKD(Subscription sub, Pricing pricing, ComboPlan comboPlan, CommonActivityLogInfoDTO activityLogInfoDTO) {
        try {
            boolean isPricing = true;
            OrderServiceDHSXKDReqDTO dto = new OrderServiceDHSXKDReqDTO();
            OrderServiceDHSXKDReqDTO infoServiceDHSX = new OrderServiceDHSXKDReqDTO();
            //Thêm thông tin số chu kỳ vào mota_hs
            String numberOfMonth;
            CycleTypeEnum cycleTypeEnum = null;
            Integer paymentCycle = null;
            String code = null;
            String name = null;
            boolean inVAT = true;
            String couponNote = CharacterConstant.BLANK;
            String couponDescription = CharacterConstant.BLANK;
            InfoUserDTO infoUserDTO = orderServiceReceiveRepository.getInforUser(sub.getUserId());
            PricingMultiPlan pricingMultiPlan = null;
            if (Objects.nonNull(sub.getPricingMultiPlanId())) {
                pricingMultiPlan = pricingMultiPlanRepository.findById(sub.getPricingMultiPlanId())
                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING_MULTI_PLAN, ErrorKey.ID,
                        String.valueOf(sub.getPricingMultiPlanId())));
            }
            String smeAddress = Objects.nonNull(infoUserDTO.getAddress()) ? infoUserDTO.getAddress() : CharacterConstant.BLANK;
            String smeAddressSetup = Objects.nonNull(sub.getAddress()) ? sub.getAddress() : smeAddress;
            boolean isPersonal = userService.isPersonal(sub.getUserId());
            if (Objects.nonNull(pricing)) {
                dto = orderServiceReceiveRepository
                        .getRequestOrderServiceReceive(sub.getUserId(), sub.getServiceId()).get();

                dto.setDiaChi(smeAddress);
                dto.setDiaChiLd(smeAddressSetup);

                if(isPersonal) {
                    UserInfoReciverDHSX userInfoReciverDHSX = userRepository.getUserInfoByIdOfService(sub.getUserId(), sub.getServiceId());
                    infoServiceDHSX.setTinhId(sub.getProvinceIdSetup() != null ? sub.getProvinceIdSetup() : null);
                    infoServiceDHSX.setTenKh(userInfoReciverDHSX.getUserName() != null ? userInfoReciverDHSX.getUserName() : null);
                    infoServiceDHSX.setDiaChi(sub.getAddress() != null ? sub.getAddress() : null);
                    infoServiceDHSX.setSoDt(userInfoReciverDHSX.getPhoneNumber() != null ? userInfoReciverDHSX.getPhoneNumber() : null);
                    infoServiceDHSX.setQuanId(sub.getDistrictIdSetup() != null ? sub.getDistrictIdSetup() : null);
                    infoServiceDHSX.setPhuongId(sub.getWardIdSetup() != null ? sub.getWardIdSetup().toString() : null);
                    infoServiceDHSX.setPhoId(sub.getStreetIdSetup() != null ? sub.getStreetIdSetup() : null);
                    infoServiceDHSX.setTenYc(userInfoReciverDHSX.getUserName() != null ? userInfoReciverDHSX.getUserName() : null);
                    infoServiceDHSX.setSoDtYc(userInfoReciverDHSX.getPhoneNumber() != null ? userInfoReciverDHSX.getPhoneNumber() : null);
                    infoServiceDHSX.setDichvuId(userInfoReciverDHSX.getServiceType() != null ?Long.valueOf(userInfoReciverDHSX.getServiceType()) : null);
                    infoServiceDHSX.setLoaitb_id(userInfoReciverDHSX.getServiceCode() != null ? Long.valueOf(userInfoReciverDHSX.getServiceCode()): null);
                    infoServiceDHSX.setDiaChi(smeAddress);
                    infoServiceDHSX.setDiaChiLd(smeAddressSetup);
                }
                paymentCycle = Math
                    .toIntExact(Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getPaymentCycle() : pricing.getPaymentCycle());
                cycleTypeEnum = Objects.nonNull(pricingMultiPlan) ? CycleTypeEnum.valueOf(pricingMultiPlan.getCircleType())
                    : CycleTypeEnum.valueOf(pricing.getCycleType());
                code = pricing.getPricingCode();
                name = pricing.getPricingName();
                inVAT = orderServiceReceiveRepository.checkPricingTaxVAT(pricing.getId());
                List<CouponInfoDTO> couponByPricing = orderServiceReceiveRepository.getCouponByPricing(pricing.getId());
                couponNote = couponByPricing.stream().map(CouponInfoDTO::getName).collect(Collectors.joining(CharacterConstant.SEMICOLON));
                couponDescription = couponByPricing.stream().map(c -> c.getCode() + CharacterConstant.COLON + c.getName()).collect(
                    Collectors.joining(CharacterConstant.SEMICOLON));
                log.info("==============createOrderServiceReceiveByApiDHSXKD with pricing============");
            } else if (Objects.nonNull(comboPlan)) {
                dto = orderServiceReceiveRepository.getRequestOrderServiceReceiveCombo(sub.getUserId()).get();

                if(isPersonal) {
                    UserInfoReciverDHSX userInfoReciverDHSX = userRepository.getUserInfoByIdOfCombo(sub.getUserId());
                    if(sub.getProvinceIdSetup() != null) {
                        infoServiceDHSX.setTinhId(sub.getProvinceIdSetup());
                    }
                    if(userInfoReciverDHSX.getUserName() != null) {
                        infoServiceDHSX.setTenKh(userInfoReciverDHSX.getUserName());
                    }
                    if(userInfoReciverDHSX.getPhoneNumber() != null) {
                        infoServiceDHSX.setSoDt(userInfoReciverDHSX.getPhoneNumber());
                    }
                    if(sub.getDistrictIdSetup() != null) {
                        infoServiceDHSX.setQuanId(sub.getDistrictIdSetup());
                    }
                    if(sub.getWardIdSetup() != null) {
                        infoServiceDHSX.setPhuongId(sub.getWardIdSetup().toString());
                    }
                    if(sub.getStreetIdSetup() != null) {
                        infoServiceDHSX.setPhoId(sub.getStreetIdSetup());
                    }
                    if(userInfoReciverDHSX.getUserName() != null) {
                        infoServiceDHSX.setTenYc(userInfoReciverDHSX.getUserName());
                    }
                    if(userInfoReciverDHSX.getPhoneNumber() != null) {
                        infoServiceDHSX.setSoDtYc(userInfoReciverDHSX.getPhoneNumber());
                    }
                    if(smeAddress != null) {
                        infoServiceDHSX.setDiaChi(smeAddress);
                    }
                    infoServiceDHSX.setDiaChiLd(smeAddressSetup);

                }

                dto.setDiaChi(smeAddress);
                dto.setDiaChiLd(smeAddressSetup);
                List<ServiceEntity> serviceEntities = orderServiceReceiveRepository.getServiceBySubCombo(sub.getComboPlanId(),
                    Collections.singletonList(3));
                ServiceEntity serviceEntitie = CollectionUtils.isEmpty(serviceEntities) ? null : serviceEntities.get(0);
                paymentCycle = comboPlan.getPaymentCycle();
                cycleTypeEnum = CycleTypeEnum.valueOf(comboPlan.getCycleType());
                code = comboPlan.getComboCode();
                name = comboPlan.getComboName();
                dto.setDichvuId(serviceEntitie == null ? null : Long.valueOf(serviceEntitie.getServiceType()));
                dto.setLoaitb_id(serviceEntitie == null ? null : Long.valueOf(serviceEntitie.getServiceCode()));
                if(isPersonal && infoServiceDHSX != null) {
                    infoServiceDHSX.setDichvuId(serviceEntitie == null ? null : Long.valueOf(serviceEntitie.getServiceType()));
                    infoServiceDHSX.setLoaitb_id(serviceEntitie == null ? null : Long.valueOf(serviceEntitie.getServiceCode()));
                }
                isPricing = false;
                inVAT = orderServiceReceiveRepository.checkComboTaxVAT(comboPlan.getId());
                List<CouponInfoDTO> couponBySubCombo = orderServiceReceiveRepository.getCouponBySubCombo(sub.getId());
                couponNote = couponBySubCombo.stream().map(CouponInfoDTO::getName).collect(Collectors.joining(CharacterConstant.SEMICOLON));
                couponDescription = couponBySubCombo.stream().map(c -> c.getCode() + CharacterConstant.COLON + c.getName()).collect(
                    Collectors.joining(CharacterConstant.SEMICOLON));
                log.info("==============createOrderServiceReceiveByApiDHSXKD with combo============");
                log.info("==============serviceEntitie============" + ((serviceEntitie == null) ? null : serviceEntitie.toString()));
            } else if (isPersonal) { // nếu là mua riêng Dịch vụ
                dto.setDiaChi(smeAddress);
                dto.setDiaChiLd(smeAddressSetup);
                UserInfoReciverDHSX userInfoReciverDHSX = userRepository.getUserInfoByIdOfService(sub.getUserId(), sub.getServiceId());
                infoServiceDHSX.setTinhId(sub.getProvinceIdSetup() != null ? sub.getProvinceIdSetup() : null);
                infoServiceDHSX.setTenKh(userInfoReciverDHSX.getUserName() != null ? userInfoReciverDHSX.getUserName() : null);
                infoServiceDHSX.setDiaChi(sub.getAddress() != null ? sub.getAddress() : null);
                infoServiceDHSX.setSoDt(userInfoReciverDHSX.getPhoneNumber() != null ? userInfoReciverDHSX.getPhoneNumber() : null);
                infoServiceDHSX.setQuanId(sub.getDistrictIdSetup() != null ? sub.getDistrictIdSetup() : null);
                infoServiceDHSX.setPhuongId(sub.getWardIdSetup() != null ? sub.getWardIdSetup().toString() : null);
                infoServiceDHSX.setPhoId(sub.getStreetIdSetup() != null ? sub.getStreetIdSetup() : null);
                infoServiceDHSX.setTenYc(userInfoReciverDHSX.getUserName() != null ? userInfoReciverDHSX.getUserName() : null);
                infoServiceDHSX.setSoDtYc(userInfoReciverDHSX.getPhoneNumber() != null ? userInfoReciverDHSX.getPhoneNumber() : null);
                infoServiceDHSX.setDichvuId(userInfoReciverDHSX.getServiceType() != null ?Long.valueOf(userInfoReciverDHSX.getServiceType()) : null);
                infoServiceDHSX.setLoaitb_id(userInfoReciverDHSX.getServiceCode() != null ? Long.valueOf(userInfoReciverDHSX.getServiceCode()): null);
                infoServiceDHSX.setDiaChi(smeAddress);
                infoServiceDHSX.setDiaChiLd(sub.getAddress() != null ? sub.getAddress() : null);

            }
            if (pricingMultiPlan !=null && pricingMultiPlan.getCycleCode() != null && !pricingMultiPlan.getCycleCode().isEmpty()) {
                code = pricingMultiPlan.getCycleCode();
            }

            numberOfMonth =
                Objects.nonNull(paymentCycle) ? (subscriptionCalculateService.getNumberMonthSub(paymentCycle, cycleTypeEnum)).toString() : "-1";
            dto.setNgayYc(new Date());
            dto.setMaNd(CONFIG_MA_ND);
            dto.setLoai(CONFIG_LOAI_NV_KY_THUAT);
            String totalAmount = (sub.getTotalAmount().setScale(2, RoundingMode.HALF_UP)).toPlainString();

            String mota_hs = STR_KENH_ONESME + "|"
                + code + ":" + name + "|" + couponDescription + "|"
                + (sub.getPaymentMethod() == 1 ? STR_DA_THANH_TOAN : STR_CHUA_THANH_TOAN) + "|"
                + totalAmount + "|"
                + numberOfMonth + MONTH;
            dto.setMoTaHs(mota_hs);
            String managerCode = subscriptionRepository.getCusManagerInClue(sub.getUserId());
            String ghi_chu =
                STR_NAME + name + "| " + STR_PAY_STATUS + (sub.getPaymentMethod() == 1 ? STR_PAID : STR_UNPAID) + "| "
                    + (inVAT ? STR_PRICE_IN_VAT : STR_PRICE_NOT_VAT) + totalAmount + "| " + STR_COUPON + couponNote + "| "
                    + STR_TOTAL_USE + numberOfMonth + MONTH + "| "
                    + STR_TIN + infoUserDTO.getTin() + "| "
                    + STR_ADDRESS_SETUP + smeAddressSetup + "| "
                    + STR_CUS_INTRODUCE_CODE + (sub.getEmployeeCode() != null ? sub.getEmployeeCode().toUpperCase() : CharacterConstant.BLANK) + "| "
                    + STR_CUS_MANAGER_CODE + (managerCode != null ? managerCode : CharacterConstant.BLANK);
            dto.setGhiChu(ghi_chu);
            if(isPersonal && infoServiceDHSX != null) {
                infoServiceDHSX.setNgayYc(new Date());
                infoServiceDHSX.setMaNd(CONFIG_MA_ND);
                infoServiceDHSX.setLoai(CONFIG_LOAI_NV_KY_THUAT);
                infoServiceDHSX.setMoTaHs(mota_hs);
                infoServiceDHSX.setGhiChu(ghi_chu);
            }
            infoServiceDHSX.setHdkhId(0L);
            dto.setHdkhId(0L);
            //Call API
            log.info("====================Request body callApiCreateOrderServiceDHSXKD: " + dto);
            log.info("====================Request body callApiCreateOrderServiceDHSXKD: " + infoServiceDHSX);
            OrderServiceDHSXKDResponseDTO resDTO = null;
            TransactionLog transactionLog = Objects.nonNull(activityLogInfoDTO.getTransactionLogDB()) ? activityLogInfoDTO.getTransactionLogDB() : null;
            if(isPersonal) {
               resDTO = executiveProducerService.callApiCreateOrderServiceDHSXKD(infoServiceDHSX, sub.getId(), transactionLog, activityLogInfoDTO);

            } else {
                resDTO = executiveProducerService.callApiCreateOrderServiceDHSXKD(dto, sub.getId(), transactionLog, activityLogInfoDTO);
            }

            // Update status transaction
            transactionLogService.updateTransactionLogCompleted(transactionLog, activityLogInfoDTO);

            log.info("====================Response callApiCreateOrderServiceDHSXKD: " + resDTO);
            if (Objects.isNull(resDTO) || !Objects.equals(ErrorConst.MESSAGE_SUCCESS, resDTO.getErrorCode())) {
                return null;
            }
            // Nếu có data trả về và tạo bảng ghi trong DB thành công thì bắt đầu call API tra cứu để update các trường status
            log.info("====================Start get status for table order_service_receive================");
            OrderServiceReceive orderServiceReceive = new OrderServiceReceive(sub.getId(), dto, resDTO);
            Long comboId = null;
            if (Objects.nonNull(resDTO.getData())) {
                // Khởi tạo thông tin lấy dữ liệu orderService bên ĐHSXKD
                TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(resDTO.getData().getMaGd(), dto.getTinhId());

                try {
                    log.info("====================Call api smeTrackingOrderDHSXKD================");
                    CommonActivityLogInfoDTO activityLogInfoTracking = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.SME_TRACKING_ORDER, transactionLog);
                    DHSXKDTrackingResDTO trackingOrderResponse = executiveProducerService.smeTrackingOrderDHSXKD(requestToDHSXKD, true,
                        sub.getId(), orderServiceReceive, transactionLog, activityLogInfoTracking);
                    // Update status transaction
                    transactionLogService.updateTransactionLogCompleted(transactionLog, activityLogInfoDTO);
                    List<TrackingOrderServiceResDTO> resDTOs = trackingOrderResponse.getData();

                    //Convert data DHSX tra ve
                    TrackingOrderServiceResDTO responseDTO = resDTOs.get(0);

                    List<TrackingOrderServiceResDTO> resNew = resDTOs.stream().filter(i -> Objects.nonNull(i.getSetupContractCompleteDate())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(resNew)) {
                        resNew.stream()
                            .sorted(Comparator.comparing(c -> DateUtil.toDate(c.getSetupContractCompleteDate(), DateUtil.FORMAT_DATE_YYYY_MM_DD_T_HH_MM_SS)))
                            .collect(Collectors.toList());
                        Collections.reverse(resNew);
                        responseDTO = resNew.get(0);
                    }


                     // Đồng bộ lại thông tin cho billing
                    billsService.updateStatusPaymentForOrderService(sub, responseDTO);

                    orderServiceReceive.setOrderStatus(Objects.nonNull(responseDTO.getOrderStatusId()) ?
                        String.valueOf(responseDTO.getOrderStatusId()) : null);
                    orderServiceReceive.setSetupStatus(Objects.nonNull(responseDTO.getSetupStatusId()) ?
                        String.valueOf(responseDTO.getSetupStatusId()) : null);
                    orderServiceReceive.setPaymentStatus(DHSXKDPaymentStatusEnum.getValueByKeyDHSXKD(responseDTO.getPaymentStatus()));

                    // Lưu giá trị serviceId hoặc comboId tương ứng với subscription này
                    if (Objects.nonNull(sub.getServiceId()) && Objects.nonNull(sub.getPricingId())) {
                        log.info("====================Get serviceId of subscription================" + sub.getServiceId());
                        orderServiceReceive.setServiceId(sub.getServiceId());
                    } else if (Objects.nonNull(sub.getComboPlanId())) {
                        comboId = comboRepository.getComboIdByComboPlanIdNoCheck(sub.getComboPlanId());
                        log.info("====================Get comboId of subscription================" + comboId);
                        orderServiceReceive.setComboId(comboId);
                    }

                    // call postback khi tracking dhsxkd
                    SmeProgress orderStatus = getSmeProgressOfOrderService(responseDTO.getOrderStatusId());
                    callPostbackMassofferWhenTrackingDHSXKD(orderServiceReceive, responseDTO, orderStatus, sub);

                } catch (Exception e) {
                    log.error(MessageKeyConstant.HAVE_ERROR_TRACKING_FROM_DHSXKD, e.getMessage(), e);
                    String msg = messageSource.getMessage(MessageKeyConstant.HAVE_ERROR_TRACKING_FROM_DHSXKD, new Long[]{sub.getId()},
                        LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Econtract.SUBSCRIPTION_ID,
                        MessageKeyConstant.HAVE_ERROR_TRACKING_FROM_DHSXKD);
                }
            } else {
                log.info("====================Can not get data response in method callApiCreateOrderServiceDHSXKD================");
            }
            log.info("====================Save record order_service_receive================");
            orderServiceReceive = orderServiceReceiveRepository.save(orderServiceReceive);

            if (!isPricing) {
                log.info("====================Start save data OrderServiceCombos================");
                List<ServiceEntity> serviceEntities = orderServiceReceiveRepository.getServiceBySubCombo(sub.getId());
                if (!CollectionUtils.isEmpty(serviceEntities)) {
                    List<OrderServiceCombo> orderServiceCombos = new ArrayList<>();
                    OrderServiceReceive finalOrderServiceReceive = orderServiceReceive;
                    Long finalComboId = comboId;
                    serviceEntities.forEach(s -> {
                        orderServiceCombos.add(
                            new OrderServiceCombo(finalOrderServiceReceive.getId(), finalComboId, s.getId(), finalOrderServiceReceive
                                .getOrderStatus(), finalOrderServiceReceive.getSetupStatus()));
                    });
                    log.info("====================List orderServiceCombos================" + orderServiceCombos);
                    orderServiceComboRepository.saveAll(orderServiceCombos);
                }
            }
            return orderServiceReceive;
        } catch (Exception e) {
            log.error(MessageKeyConstant.NOT_FOUND, e.getMessage(), e);
            String msg = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, new Long[] { sub.getId() },
                LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Econtract.SUBSCRIPTION_ID,
                MessageKeyConstant.NOT_FOUND);
        }

    }

    /**
     * lấy sme_progress theo order_status_id
     */
    private SmeProgress getSmeProgressOfOrderService(Long orderStatusId) {
        if (Objects.nonNull(orderStatusId)) {
            return smeProgressRepository.getStatusByStatusId(orderStatusId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.ORDER_SERVICE_STATUS, ErrorKey.ID,
                    String.valueOf(orderStatusId)));
        }
        return null;
    }

    /**
     * Throw orderService bad request bad request exception.
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     *
     * @return the bad request exception
     */
    private BadRequestException throwOrderServiceBadRequest(String messageKeyConstant, String errorKey) {
        String message = messageSource.getMessage(messageKeyConstant, new Object[]{errorKey},
            LocaleContextHolder.getLocale());
        return new BadRequestException(message, Resources.ORDER_SERVICE, Resources.RESPONSE, messageKeyConstant);
    }


    /**
     * SME Hủy đơn hàng order service
     */
    @Override
    public BaseResponseDTO cancelOrderService(Long id, OrderCancelActionEnum action, PortalType portalType) {
        // kiểm tra subscription id có phải của user đăng nhập không?
        Subscription subscriptionOrder = subscriptionRepository
            .findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED
                .getValue()).orElseThrow(() -> {
                String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, subscriptionMessage,
                    LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(messageNotFound, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });

        // Kiểm tra subscription theo portal
        validateSubscriptionByPortal(subscriptionOrder, portalType);

        OrderServiceReceive myOrder = orderServiceReceiveRepository.getOrderServiceReceiveBySubscriptionId(id).orElseThrow(() -> {
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, orderServiceReceiveMessage,
                LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });

        // lẩy ra department của user để lấy provinceId của chủ subscription
        User actor = userService.findByIdAndDeletedFlag(subscriptionOrder.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());

        // Khởi tạo thông tin lấy dữ liệu orderService bên ĐHSXKD

        // cập nhật lấy province_id_setup trong bảng sub (KHDN ko lay thong tin trong nay)
        Long provinceIdSetup = subscriptionOrder.getProvinceIdSetup();
        Long provinceId = Objects.equals(actor.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) && provinceIdSetup != null ? provinceIdSetup : actor.getProvinceId();
        TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(myOrder.getTransactionCode(),
                provinceId);
        Optional<TransactionLog> transactionLogCurrent = transactionLogRepository.findFirstBySubscriptionIdOrderByIdDesc(id);
        CommonActivityLogInfoDTO activityLogInfoTracking = null;
        if (transactionLogCurrent.isPresent()) {
            activityLogInfoTracking = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.SME_TRACKING_ORDER, transactionLogCurrent.get());
        }
        DHSXKDTrackingResDTO trackingOrderResponse = executiveProducerService
                .smeTrackingOrderDHSXKD(requestToDHSXKD, true, id,
                        myOrder, transactionLogCurrent.isPresent() ? transactionLogCurrent.get() : null, activityLogInfoTracking);
        // Update status transaction
        if (transactionLogCurrent.isPresent()) {
            transactionLogService.updateTransactionLogCompleted(transactionLogCurrent.get(), activityLogInfoTracking);

        }

        // lấy ra data response DHSXKD trả về
        TrackingOrderServiceResDTO orderDHSXKD = trackingOrderResponse.getData().get(0);

        // kiểm tra order service status trả về bên ĐHSXKD
        if (Objects.isNull(orderDHSXKD.getOrderStatus())) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.ORDER_STATUS);
        }

        Long orderDHSXKDStatus = orderServiceReceiveRepository.getProgressIdByStatusName(orderDHSXKD.getOrderStatus());

        if (Objects.equals(SmeProgressEnum.RECEIVED.getValue(), orderDHSXKDStatus)) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.ORDER_SERVICE_STATUS_MUST_BE_RECEIVED, ErrorKey.ORDER_STATUS);
        }

        // Hủy order service bên DHSXKD
        DHSXKDTrackingResDTO cancelOrderResponse = executiveProducerService.cancelOrderDHSXKD(requestToDHSXKD, subscriptionOrder.getId());

        validateDHSXKDErrorCode(cancelOrderResponse);

        // Lấy lại thông tin đơn hàng đã hủy để update vào db hệ thống
        DHSXKDTrackingResDTO trackingOrderCanceledResponse = executiveProducerService
                .smeTrackingOrderDHSXKD(requestToDHSXKD, true, id,
                        myOrder, transactionLogCurrent.isPresent() ? transactionLogCurrent.get() : null, activityLogInfoTracking);
        // Update status transaction
        if (transactionLogCurrent.isPresent()) {
            transactionLogService.updateTransactionLogCompleted(transactionLogCurrent.get(), activityLogInfoTracking);

        }

        // lấy ra data response DHSXKD trả về
        TrackingOrderServiceResDTO orderCanceledDHSXKD = trackingOrderCanceledResponse.getData().get(0);

        // update setup status
        Long orderDHSXKDSetupStatusId = orderDHSXKD.getSetupStatusId();

        // update dữ liệu order_service_receive và order_service_receive_log trong db
        String orderStatusIdStr =
            Objects.nonNull(orderCanceledDHSXKD.getOrderStatusId()) ? String.valueOf(orderCanceledDHSXKD.getOrderStatusId()) : null;
        String orderSetupStatusIdStr = Objects.nonNull(orderDHSXKDSetupStatusId) ? String.valueOf(orderDHSXKDSetupStatusId) : null;
        myOrder.setOrderStatus(orderStatusIdStr);
        myOrder.setSetupStatus(orderSetupStatusIdStr);

        // kiểm tra order service status trả về bên ĐHSXKD
        if (Objects.isNull(orderDHSXKD.getOrderStatus())) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.ORDER_STATUS);
        }

        orderServiceReceiveRepository.save(myOrder);
        saveOrderServiceReceiveLog(myOrder, orderStatusIdStr, orderSetupStatusIdStr);

        // Nếu action == DELETED thì cập nhật trạng thái xóa của subscription
        if (OrderCancelActionEnum.DELETE.equals(action)) {
            subscriptionOrder.setDeletedFlag(DeletedFlag.DELETED.getValue());
        } else if (OrderCancelActionEnum.CANCEL.equals(action)) {
            subscriptionOrder.setStatus(SubscriptionStatusEnum.CANCELED.value);
        }
        if (Objects.nonNull(subscriptionOrder.getPreOrder()) && org.apache.commons.lang3.StringUtils.isBlank(subscriptionOrder.getPreOrder().toString())) { // check TH lưu giá trị empty gây lỗi khi gọi sang dhsxkd
            subscriptionOrder.setPreOrder(null);
        }
        Subscription result = subscriptionRepository.save(subscriptionOrder);
        // Lưu lịch sử thay đổi trạng thái thuê bao
        subscriptionHistoryService.saveStatusHistory(result);

        // call tích hợp sang masoffer
        if (StringUtils.isNotBlank(subscriptionOrder.getTrafficId())) {
            if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscriptionOrder.getTrafficSource())) { // gửi sang access trade
                integrationService.sendStatusPostbackAccesstrade(subscriptionOrder, AccessTradeStatusEnum.CANCEL.value, null);
            } else {
                sendPostbackCancelToMassoffer(actor, subscriptionOrder);
            }

        }
        return new BaseResponseDTO(id);
    }

    /**
     * Gửi postback cancel sang massoffer
     */
    private void sendPostbackCancelToMassoffer(User actor, Subscription subscription) {
        String provinceCode = provinceRepository.getCodeOfProvince(actor.getProvinceId(), actor.getNationId());
        massofferReasonRepository
            .findByProvinceCodeAndCancelReasonId(provinceCode, Long.parseLong(DHSXKDOrderStatusEnum.STATUS_7.value)).ifPresent(e -> {
            integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(),
                MasOfferStatusEnum.CANCEL.value, e.getContent());
        });
    }

    /**
     * Validate subscription theo các portal
     */
    private void validateSubscriptionByPortal(Subscription subscription, PortalType portalType) {
        if (PortalType.SME.equals(portalType)) {
            // Kiểm tra subscription có phải của sme hay không?
            if (!subscription.getUserId().equals(AuthUtil.getCurrentUserId())) {
                String message = messageSource.getMessage(MessageKeyConstant.USER_ACCESS_DENIED, null,
                    LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.USER, ErrorKey.ID, MessageKeyConstant.USER_ACCESS_DENIED);
            }
        } else if (PortalType.DEV.equals(portalType)) {
            // kiểm tra dịch vụ của sub có phải do doanh nghiệp dev cung cấp hay không?
            Long userId =
                Objects.nonNull(subscription.getServiceId()) ? subscription.getService().getUserId() : subscription.getComboPlan().getId();
            if (!AuthUtil.getCurrentParentId().equals(userId)) {
                String message = messageSource.getMessage(MessageKeyConstant.USER_ACCESS_DENIED, null,
                    LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.USER, ErrorKey.ID, MessageKeyConstant.USER_ACCESS_DENIED);
            }
        } else if (PortalType.ADMIN.equals(portalType)) {
            Department actorAdminDepartment = departmentsRepository
                .findByIdAndDeletedFlag(AuthUtil.getCurrentUser().getDepartmentId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> {
                    String message = messageSource.getMessage(MessageKeyConstant.USER_ACCESS_DENIED, null,
                        LocaleContextHolder.getLocale());
                    return new BadRequestException(message, Resources.USER, ErrorKey.ID, MessageKeyConstant.USER_ACCESS_DENIED);
                });
            // Là admin tỉnh thành -> chỉ được xem subscription của các sme cùng tỉnh
            if (Objects.nonNull(actorAdminDepartment.getProvinceId())) {
                User smeSubSubscription = userService.findByIdAndDeletedFlag(subscription.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
                if (!smeSubSubscription.getProvinceId().equals(actorAdminDepartment.getProvinceId())) {
                    String message = messageSource.getMessage(MessageKeyConstant.USER_ACCESS_DENIED, null,
                        LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.USER, ErrorKey.ID, MessageKeyConstant.USER_ACCESS_DENIED);
                }
            }
        }
    }

    /**
     * Lưu log của order service receive
     */
    private void saveOrderServiceReceiveLog(OrderServiceReceive orderServiceReceive, String orderStatusId, String setupStatusId) {
        OrderServiceReceiveLog orderLog = new OrderServiceReceiveLog();
        orderLog.setOrderReceiveId(orderServiceReceive.getOrderReceiveId());
        orderLog.setOrderStatus(orderStatusId);
        orderLog.setPaymentStatus(orderServiceReceive.getPaymentStatus());
        orderLog.setPicCode(orderServiceReceive.getAmCode());
        orderLog.setPicName(orderServiceReceive.getAmName());
        orderLog.setPicPhoneNo(orderServiceReceive.getAmPhoneNo());
        orderLog.setPicEmail(orderServiceReceive.getAmEmail());
        orderLog.setPicDepartment(orderServiceReceive.getAmDeparment());
        orderLog.setDateCreateContract(orderServiceReceive.getDateSignContract());
        orderLog.setSetupStatus(setupStatusId);
        orderServiceReceiveLogRepository.save(orderLog);
    }

    /**
     * Validate mã lỗi của response bên DHSXKD
     */
    @Override
    public void validateDHSXKDErrorCode(DHSXKDTrackingResDTO response) {
        log.info("==============Error response of DHSXKD============");
        if (Objects.isNull(response)) {
            // Lỗi do api bên DHSXKD 500 hay không trả về kết quả
            throw exceptionFactory.badRequest(MessageKeyConstant.REMOTE_SYSTEM_NOT_RESPONSE, Resources.DHSXKD, ErrorKey.RESPONSE,
                Resources.DHSXKD);
        } else if (!Objects.equals(ErrorConst.MESSAGE_SUCCESS, response.getErrorCode())) {
            // Lỗi không tìm thấy thông tin
            throw exceptionFactory.badRequest(MessageKeyConstant.REMOTE_SYSTEM_RESPOND_FAILURE, Resources.DHSXKD, ErrorKey.RESPONSE,
                Resources.DHSXKD, GsonMapperUtil.toJson(response));
        }
    }

    /**
     * check sub
     *
     */
    @Override
    public OrderServiceReceive findBySubscriptionId(Long id) {
       return orderServiceReceiveRepository.findBySubscriptionId(id).stream().findFirst()
           .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.ORDER_SERVICE_RECEIVE, ErrorKey.SUBSCRIPTION_ID, String.valueOf(id)));
    }

    @Override
    public OrderServiceProcessDTO getProgress(Long id) {
        // check sub co ton tai
        Subscription subscription = subscriptionService.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());

        // SPC_ONEDXINTERNAL-10382: Các đơn hàng OS tới từ nhà phát triển bên thứ 3 không được xử lý trên DHSXKD
        // Tab tiến trình của các đơn hàng loại này được xử lý riêng
        ServiceEntity service = subscription.getService();
        if ((service.getOnOsTypeEnum() == OnOsTypeEnum.OS) && (service.getProviderTypeEnum() == ProviderTypeEnum.THIRD_PARTY)) {
            return getOs3rdProgress(subscription, service);
        } else if ((service.getOnOsTypeEnum() == OnOsTypeEnum.OS) &&
            (Objects.nonNull(service.getMigrationId()) || Objects.equals(service.getServiceCode(), SIM_SERVICE_CODE))) {
            // Tab tiến trình của các đơn hàng trên ban KHCN được xử lý riêng
            return getOsKhcnProgress(subscription, service);
        }

        // lấy thông tin service + pricing
        OrderServiceProcessDTO orderServiceProcessDTO = orderServiceReceiveRepository.getOrderServiceBySubId(id);

        // check orderServiceReceive có tồn tại
        OrderServiceReceive orderServiceReceive = findBySubscriptionId(id);

        // set thông tin am
        orderServiceProcessDTO.setAmCode(orderServiceReceive.getAmCode());
        orderServiceProcessDTO.setAmDepartment(orderServiceReceive.getAmDeparment());
        orderServiceProcessDTO.setAmName(orderServiceReceive.getAmName());
        orderServiceProcessDTO.setAmPhone(orderServiceReceive.getAmPhoneNo());
        orderServiceProcessDTO.setAmEmail(orderServiceReceive.getAmEmail());

        // lấy list log
        Set<IOrderServiceLogDTO> logDTOList = orderServiceReceiveRepository.getLogByOrderServiceId(orderServiceReceive.getId());
        Set<String> listStatus = new HashSet<>();
        if (!CollectionUtils.isEmpty(logDTOList)) {
            listStatus = logDTOList.stream().map(IOrderServiceLogDTO::getStatus).collect(Collectors.toSet());
        }

        // lấy provinceId user su dung sub
        User sme = userService.findByIdAndDeletedFlag(subscription.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
        Long provinceId = sme.getProvinceId();
        //Neu CreatedSourceMigration khong phai la DHSXKD thi call TrackingOrderServiceReqDTO
        TrackingOrderServiceResDTO resDTO = null;
        // cập nhật lấy province_id_setup trong bảng sub (KHDN ko lay thong tin trong nay)
        Long provinceIdSetup = subscription.getProvinceIdSetup();
        provinceId = Objects.equals(sme.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) && provinceIdSetup != null ? provinceIdSetup : provinceId;
        if (!Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            // lay du lieu ben DHSXKD
            TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(orderServiceReceive.getTransactionCode(), provinceId);

            Optional<TransactionLog> transactionLogCurrent = transactionLogRepository.findFirstBySubscriptionIdOrderByIdDesc(id);
            CommonActivityLogInfoDTO activityLogInfoTracking = null;
            if (transactionLogCurrent.isPresent()) {
                activityLogInfoTracking = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.SME_TRACKING_ORDER, transactionLogCurrent.get());
            }
            DHSXKDTrackingResDTO trackingOrderResponse = executiveProducerService
                    .smeTrackingOrderDHSXKD(requestToDHSXKD, true, id,
                            orderServiceReceive, transactionLogCurrent.isPresent() ? transactionLogCurrent.get() : null, activityLogInfoTracking);
            // Update status transaction
            if (transactionLogCurrent.isPresent()) {
                transactionLogService.updateTransactionLogCompleted(transactionLogCurrent.get(), activityLogInfoTracking);

            }

            //lay date DHSX tra ve
            resDTO = trackingOrderResponse.getData().get(0);
            List<TrackingOrderServiceResDTO> resNew = trackingOrderResponse.getData().stream().filter(i -> Objects.nonNull(i.getSetupContractCompleteDate())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(resNew)) {
                resNew.stream()
                    .sorted(Comparator.comparing(c -> DateUtil.toDate(c.getSetupContractCompleteDate(), DateUtil.FORMAT_DATE_YYYY_MM_DD_T_HH_MM_SS)))
                    .collect(Collectors.toList());
                Collections.reverse(resNew);
                resDTO = resNew.get(0);
            }
            log.info("====================Get data in response resDTO in method getProgress================".concat(Objects.nonNull(resDTO) ?
                resDTO.toString() : null));
        }

        Long orderStatusId = (Objects.nonNull(resDTO) && Objects.nonNull(resDTO.getOrderStatusId())) ? resDTO.getOrderStatusId()
            : (Objects.nonNull(orderServiceReceive.getOrderStatus()) ? Long.valueOf(orderServiceReceive.getOrderStatus()) : null);
        SmeProgress orderStatus =  getSmeProgressOfOrderService(orderStatusId);

        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        DateTimeFormatter dateFormatHHMMSS = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS);
        orderServiceProcessDTO.setOrderReceiveId(orderServiceReceive.getOrderReceiveId());
        orderServiceProcessDTO.setStatusOrderName(Objects.nonNull(orderStatus) ? orderStatus.getName() : null);
        orderServiceProcessDTO.setStatusOrderId(Objects.nonNull(orderStatus) ? orderStatus.getId() : SmeProgressEnum.PAID.getValue());
        orderServiceProcessDTO.setStartAt(Objects.nonNull(subscription.getStartedAt()) ? dateFormat.format(DateUtil.toLocalDate(subscription.getStartedAt())) : null);
        if (!Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            orderServiceProcessDTO.setCreateAt(Objects.nonNull(subscription.getCreatedAt()) ? dateFormat.format(DateUtil.toLocalDate(subscription.getCreatedAt())) : null);
        } else {
            orderServiceProcessDTO.setCreateAt(Objects.nonNull(subscription.getStartedAt()) ? dateFormat.format(DateUtil.toLocalDate(subscription.getStartedAt())) : null);
        }
        orderServiceProcessDTO.setSubscriptionCode(subscription.getSubCode());
        orderServiceProcessDTO.setTransactionCode(Objects.nonNull(resDTO) ? resDTO.getExchangeCode() : orderServiceReceive.getTransactionCode());
        orderServiceProcessDTO.setUpdateAt((Objects.nonNull(resDTO) && Objects.nonNull(resDTO.getReceiptContractUpdatedDate())) ? dateFormatHHMMSS.format(DateUtil.convertDateToLocalDateTime(resDTO.getReceiptContractUpdatedDate())) : dateFormatHHMMSS.format(DateUtil.convertDateToLocalDateTime(new Date())));

        //Check create source migration co phai DHSXKD
        if (!Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            // update orderServiceReceive
            OrderServiceReceive newOrderService = updateOrderServiceReceive(orderServiceReceive, resDTO, subscription.getServiceId(), null,
                Objects.nonNull(resDTO.getOrderStatusId()) ? String.valueOf(resDTO.getOrderStatusId()) : orderServiceReceive.getOrderStatus(), subscription);

            //insert log
            if (!listStatus.contains(Objects.nonNull(resDTO.getOrderStatusId()) ? resDTO.getOrderStatusId().toString() : NOT_SET)) {
                createLog(newOrderService.getId(), resDTO);
            }

            // update trạng thái thanh toán cho billing
            Bills bills = billsService.updateStatusPaymentForOrderService(subscription, resDTO);

            //Set lại trạng thái của đơn hàng theo bill
            orderServiceProcessDTO.setPaymentStatus(
                Objects.nonNull(bills) && Objects.equals(BillStatusEnum.PAID.value, bills.getStatus()) ? BillStatusEnum.PAID.name()
                    : BillStatusEnum.WAITING.name());

            callPostbackMassofferWhenTrackingDHSXKD(orderServiceReceive, resDTO, orderStatus, subscription);
        }

        Set<IOrderServiceLogDTO> logDTOListNew;
        if (Objects.equals(subscription.getCreatedSourceMigration(), CreatedSourceMigrationEnum.DHSXKD.getValue())) {
            logDTOListNew = orderServiceReceiveRepository.getLogByOrderServiceIdByCreateSourceDHSXKD(orderServiceReceive.getId());
        } else {
            logDTOListNew = orderServiceReceiveRepository.getLogByOrderServiceId(orderServiceReceive.getId());
        }
        if (!CollectionUtils.isEmpty(logDTOListNew)) {
            orderServiceProcessDTO.setTimeLine(logDTOListNew.stream().filter(x -> Objects.nonNull(x.getStatus()))
                .map(OrderServiceLogDTO::new).collect(Collectors.toSet()));
        }

        return orderServiceProcessDTO;
    }

    /**
     * Lấy thông tin tiến trình cho đơn hàng OS của third-party
     */
    private OrderServiceProcessDTO getOs3rdProgress(Subscription subscription, ServiceEntity service) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS);
        Long subscriptionId = subscription.getId();
        Pricing pricing = subscription.getPricing();
        Date startedAt = subscription.getStartedAt();
        List<FileAttach> lstAvatar = fileAttachService.findByServiceIdAndObjectType(service.getId(), FileAttachTypeEnum.AVATAR.value);
        List<SubscriptionHistory> lstHistory = subscriptionHistoryService.findBySubscriptionId(subscriptionId).stream()
            .filter(history ->
                history.getContentType() == ContentType.CHANGE_3RD_PARTY_ORDER_SERVICE_STATUS || // Thay đổi trạng thái đơn hàng
                history.getContentType() == ContentType.CREATE_ACTUAL_SUBSCRIPTION || // SME tạo đơn hàng
                history.getContentType() == ContentType.ADMIN_CREATE_SUB) // Admin tạo đơn hàng
            .collect(Collectors.toList());
        Set<Long> lstActor = lstHistory.stream().map(SubscriptionHistory::getCreatedBy).collect(Collectors.toSet());
        Map<Long, User> mapActor = userService.findByIdIn(lstActor).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Set<OrderServiceLogDTO> lstLogDTO = lstHistory.stream().map(item -> new OrderServiceLogDTO(item, mapActor)).collect(Collectors.toSet());
        Bills bill = billsService.getLatestFromSubscriptionId(subscriptionId);
        String billingStatus = Objects.nonNull(bill) && Objects.equals(BillStatusEnum.PAID.value, bill.getStatus()) ?
            BillStatusEnum.PAID.name() : BillStatusEnum.WAITING.name();

        OrderServiceProcessDTO responseDTO = new OrderServiceProcessDTO();
        responseDTO.setPricingName(Objects.nonNull(pricing) ? pricing.getPricingName() : null);
        responseDTO.setServiceName(service.getServiceName());
        responseDTO.setStatusOrderId(subscription.getOs3rdStatus());
        responseDTO.setCreateAt(Objects.nonNull(startedAt) ? dateFormat.format(startedAt) : null);
        responseDTO.setPaymentStatus(billingStatus);
        lstHistory.stream().map(SubscriptionHistory::getCreatedAt).max(LocalDateTime::compareTo)
            .ifPresent(localDateTime -> responseDTO.setUpdateAt(timeFormatter.format(localDateTime)));
        lstAvatar.stream().findFirst().ifPresent(fileAttach -> responseDTO.setIcon(fileAttach.getFilePath()));
        responseDTO.setTimeLine(lstLogDTO);
        return responseDTO;
    }

    /**
     * Lấy thông tin tiến trình cho đơn hàng OS của ban KHCN
     */
    private OrderServiceProcessDTO getOsKhcnProgress(Subscription subscription, ServiceEntity service) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS);
        Long subscriptionId = subscription.getId();
        Pricing pricing = subscription.getPricing();
        Date startedAt = subscription.getStartedAt();
        List<FileAttach> lstAvatar = fileAttachService.findByServiceIdAndObjectType(service.getId(), FileAttachTypeEnum.AVATAR.value);
        List<SubscriptionHistory> lstHistory = subscriptionHistoryService.findBySubscriptionId(subscriptionId).stream()
            .filter(history ->
                history.getContentType() == ContentType.CHANGE_KHCN_ORDER_SERVICE_STATUS || // Thay đổi trạng thái đơn hàng
                    history.getContentType() == ContentType.CREATE_ACTUAL_SUBSCRIPTION || // SME tạo đơn hàng
                    history.getContentType() == ContentType.ADMIN_CREATE_SUB) // Admin tạo đơn hàng
            .collect(Collectors.toList());
        Set<Long> lstActor = lstHistory.stream().map(SubscriptionHistory::getCreatedBy).collect(Collectors.toSet());
        Map<Long, User> mapActor = userService.findByIdIn(lstActor).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Set<OrderServiceLogDTO> lstLogDTO = lstHistory.stream().map(item -> new OrderServiceLogDTO(item, mapActor)).collect(Collectors.toSet());
        Bills bill = billsService.getLatestFromSubscriptionId(subscriptionId);
        String billingStatus = Objects.nonNull(bill) && Objects.equals(BillStatusEnum.PAID.value, bill.getStatus()) ?
            BillStatusEnum.PAID.name() : BillStatusEnum.WAITING.name();

        OrderServiceProcessDTO responseDTO = new OrderServiceProcessDTO();
        responseDTO.setPricingName(Objects.nonNull(pricing) ? pricing.getPricingName() : null);
        responseDTO.setServiceName(service.getServiceName());
        responseDTO.setStatusOrderId(subscription.getOs3rdStatus());
        responseDTO.setCreateAt(Objects.nonNull(startedAt) ? dateFormat.format(startedAt) : null);
        responseDTO.setPaymentStatus(billingStatus);
        lstHistory.stream().map(SubscriptionHistory::getCreatedAt).max(LocalDateTime::compareTo)
            .ifPresent(localDateTime -> responseDTO.setUpdateAt(timeFormatter.format(localDateTime)));
        lstAvatar.stream().findFirst().ifPresent(fileAttach -> responseDTO.setIcon(fileAttach.getFilePath()));
        responseDTO.setTimeLine(lstLogDTO);
        return responseDTO;
    }

    /**
     * call postback massoffer khi 2 trạng thái đơn hàng khác nhau
     */
    private void callPostbackMassofferWhenTrackingDHSXKD(OrderServiceReceive orderServiceReceive, TrackingOrderServiceResDTO resDTO,
        SmeProgress orderStatus, Subscription subscription) {
        if (!Objects.equals(Objects.nonNull(orderServiceReceive.getOrderStatus()) ? Long.parseLong(orderServiceReceive.getOrderStatus()) : null,
            resDTO.getOrderStatusId()) && StringUtils.isNotBlank(subscription.getTrafficId()) && Objects.nonNull(orderStatus)) {
            if (Objects.equals(SmeProgressEnum.FINISHING.getValue(), orderStatus.getId())) {
                if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) { // gửi sang access trade
                    integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.COMPLETED.value, null);
                } else {
                    integrationService.sendPostbackMasOffer(subscription, subscription.getTrafficId(),
                            MasOfferStatusEnum.COMPLETED.value, null);
                }
            } else if (Objects.equals(SmeProgressEnum.CANCELED.getValue(), orderStatus.getId())) {
                if (Objects.equals(SubscriptionConstant.ACCESS_TRADE, subscription.getTrafficSource())) { // gửi sang access trade
                    integrationService.sendStatusPostbackAccesstrade(subscription, AccessTradeStatusEnum.CANCEL.value, null);
                } else {
                    // lẩy ra department của user để lấy provinceId của chủ subscription
                    User userOfSubscription = userService.findByIdAndDeletedFlag(subscription.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
                    sendPostbackCancelToMassoffer(userOfSubscription, subscription);
                }

            }
        }
    }

    /**
     * Lấy thông tin orderServiceReceive bởi serviceId
     */
    @Override
    public List<OrderServiceCombo> getOrderServiceComboByServiceIdIn(Set<Long> services, Long orderServiceReceiveId) {
        List<OrderServiceCombo> comboReceives = orderServiceComboRepository.findByServiceIds(services, orderServiceReceiveId);
        if (comboReceives.isEmpty()) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.ORDER_RECEIVE_ID);
        }
        return comboReceives;
    }

    /**
     * Lấy provin_id của người tạo
     *
     */
    private Long getProvince(Long userId) {
        return userService.findByIdAndDeletedFlag(userId, DeletedFlag.NOT_YET_DELETED.getValue()).getProvinceId();
    }

    @SneakyThrows
    @Transactional
    @Override
    public ComboOrderServiceDTO getOrderServiceOfSubscriptionCombo(Long id) {
        ComboOrderServiceDTO orderSubDTO = new ComboOrderServiceDTO();
        Subscription sub = subscriptionService.findByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue());
        // check orderServiceReceive có tồn tại
        OrderServiceReceive orderServiceReceive = findBySubscriptionId(id);
        // lấy provinceId của user đăng nhập
        // lẩy ra department của user để lấy provinceId của chủ subscription
        User actor = userService.findByIdAndDeletedFlag(sub.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
        Long provinceId = actor.getProvinceId();
        // cập nhật lấy province_id_setup trong bảng sub (KHDN ko lay thong tin trong nay)
        Long provinceIdSetup = sub.getProvinceIdSetup();
        provinceId = Objects.equals(actor.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) && provinceIdSetup != null ? provinceIdSetup : provinceId;
        if (AuthUtil.checkUserRoles(Collections.singletonList(RoleType.SME.getValue()))) {
            getProvince(AuthUtil.getCurrentUserId());
        }
        // lay thông tin combo
        if (Objects.isNull(sub.getComboPlanId())) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.COMBOPLAN_ID);
        }
        Combo combo = comboPlanRepository.findByIdAndDeletedFlag(sub.getComboPlanId(), DeletedFlag.NOT_YET_DELETED.getValue()).map(ComboPlan::getCombo).orElseThrow(() ->
                throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.COMBO_ID)
        );
        // Admin có toàn quyền truy cập
        // Dev xem sub tạo hộ SME, SME xem tất cả sub được tạo
        if (!AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue()))){
            if (!sub.getUserId().equals(AuthUtil.getCurrentParentId()) && !combo.getUserId().equals(AuthUtil.getCurrentParentId())) {
                throw throwOrderServiceBadRequest(MessageKeyConstant.USER_ACCESS_DENIED, ErrorKey.USER_ID);
            }
        }
        // Lay thong tin dich vu thuoc combo
        Set<ComboServiceDTO> serviceOfCombo = pricingRepository.getPricingByComboPlan(sub.getComboPlanId());
        Set<Long> services = new HashSet<>();
        serviceOfCombo.forEach(x -> services.add(x.getServiceId()));
        if (CollectionUtils.isEmpty(services)) {
            throw throwOrderServiceBadRequest(MessageKeyConstant.SERVICE_INVALID, ErrorKey.SubscriptionPlan.SERVICE_ID);
        }
        // lay du lieu ben DHSXKD của subsription
        TrackingOrderServiceReqDTO subReq = new TrackingOrderServiceReqDTO(orderServiceReceive.getTransactionCode(), provinceId);
        Optional<TransactionLog> transactionLogCurrent = transactionLogRepository.findFirstBySubscriptionIdOrderByIdDesc(id);
        CommonActivityLogInfoDTO activityLogInfoTracking = null;
        if (transactionLogCurrent.isPresent()) {
            activityLogInfoTracking = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.SME_TRACKING_ORDER, transactionLogCurrent.get());
        }
        DHSXKDTrackingResDTO subTrackingOrderResponse = executiveProducerService
                .smeTrackingOrderDHSXKD(subReq, true, id,
                        orderServiceReceive, transactionLogCurrent.isPresent() ? transactionLogCurrent.get() : null, activityLogInfoTracking);
        // Update status transaction
        if (transactionLogCurrent.isPresent()) {
            transactionLogService.updateTransactionLogCompleted(transactionLogCurrent.get(), activityLogInfoTracking);

        }
        List<TrackingOrderServiceResDTO> subResDTOs = subTrackingOrderResponse.getData();
        Set<Long> orders = subResDTOs.stream().map(TrackingOrderServiceResDTO:: getOrderStatusId).collect(Collectors.toSet());
        TrackingOrderServiceResDTO resDTO = subResDTOs.get(0);
        List<TrackingOrderServiceResDTO> resNew = subResDTOs.stream().filter(i -> Objects.nonNull(i.getSetupContractCompleteDate())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(resNew)) {
            resNew.stream()
                .sorted(Comparator.comparing(c -> DateUtil.toDate(c.getSetupContractCompleteDate(), DateUtil.FORMAT_DATE_YYYY_MM_DD_T_HH_MM_SS)))
                .collect(Collectors.toList());
            Collections.reverse(resNew);
            resDTO = resNew.get(0);
        }
        TrackingOrderServiceResDTO subResDTO = resDTO;
        log.info("====================Get data in response subResDTO in method getOrderServiceOfSubscriptionCombo================"
                .concat(Objects.nonNull(subResDTO) ?
                subResDTO.toString() : null));
        // Lấy thông tin orderServiceCombo tương ứng với mỗi service
        List<OrderServiceCombo> orderOfServices = getOrderServiceComboByServiceIdIn(services, orderServiceReceive.getId());
        // lấy thông tin status, updateTime của từng dịch vụ
        orderOfServices.forEach((order) -> {
            log.info("==============Get orderStatus and updateTime by orderStatusInCombo============");
            ComboServiceDTO orderDHSXKD = serviceOfCombo.stream().filter(s -> s.getServiceId().equals(order.getServiceId())).findFirst().orElseThrow(
                    ()-> throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.Pricing.SERVICE_ID)
            );
            List<String> listStatus = new ArrayList<>();
            // lấy list log timeline của mỗi service
            log.info("==============Get time line by id of orderReceiveCombo============");
            Set<IOrderServiceLogDTO> logDTOList = orderServiceReceiveRepository.getLogByOrderServiceComId(order.getId());
            if (!CollectionUtils.isEmpty(logDTOList)) {
                listStatus = logDTOList.stream().map(IOrderServiceLogDTO::getStatus).collect(Collectors.toList());
            }
            // Save DB
            log.info("==============save log=============");
            if (subResDTOs.size() == 1) {
                // update thông tin bên ĐHSXKD vào bảng orderServiceReceive
                log.info("==============save orderServiceReceive =============");
                updateOrderServiceReceive(orderServiceReceive, subResDTO, null, sub.getComboPlan().getComboId(),
                    Objects.nonNull(subResDTO.getOrderStatusId()) ? subResDTO.getOrderStatusId().toString() : null, sub);
                // update thông tin bên ĐHSXKD vào bảng orderServiceCombo
                log.info("==============save orderServiceCombo 1 object=============");
                OrderServiceCombo updateOrderCombo = updateStatusOfOrderServiceCombo(order, subResDTO);
                if (!listStatus.contains(Objects.nonNull(subResDTO.getOrderStatusId()) ? String.valueOf(subResDTO.getOrderStatusId()) : NOT_SET)) {
                    log.info("==============save orderServiceReceive Log 1 object=============");
                    createOrderComboLog(updateOrderCombo.getId(), subResDTO);
                }
            } else if (subResDTOs.size() > 1 && orders.contains(DANG_DIEU_HANH_THI_CONG)) {
                List<String> finalListStatus = listStatus;
                subResDTOs.forEach((traking) -> {
                    // update thông tin bên ĐHSXKD vào bảng orderServiceReceive
                    log.info("==============save orderServiceReceive =============");
                    updateOrderServiceReceive(orderServiceReceive, subResDTO, null, sub.getComboPlan().getComboId(),
                        Objects.nonNull(traking.getOrderStatusId()) ? traking.getOrderStatusId().toString() : null, sub);
                    // update thông tin bên ĐHSXKD vào bảng orderServiceCombo
                    log.info("==============save orderServiceCombo > 1 object=============");
                    OrderServiceCombo newOrderSer = updateStatusOfOrderServiceCombo(order, traking);
                    if (!finalListStatus.contains(Objects.nonNull(traking.getOrderStatusId()) ? String.valueOf(traking.getOrderStatusId()) : NOT_SET)) {
                        // update thông tin bên ĐHSXKD vào bảng orderServiceReceiveLog
                        log.info("==============save orderServiceReceive Log > 1 object=============");
                        createOrderComboLog(newOrderSer.getId(), traking);
                    }
                });
            }

            Long orderStatusId = Long.parseLong(order.getOrderStatus());
            // lấy order status trong DB thông qua status của ĐHSXKD
            SmeProgress orderStatus = smeProgressRepository.getStatusByStatusId(orderStatusId).orElseThrow(() -> throwOrderServiceBadRequest(MessageKeyConstant.NOT_FOUND, ErrorKey.ORDER_STATUS));
            orderDHSXKD.setStatusOrderId(Objects.nonNull(orderStatus) ? orderStatus.getId() : null);
            orderDHSXKD.setStatusOrderName(Objects.nonNull(orderStatus) ? orderStatus.getName() : null);
            orderDHSXKD.setUpdateAt(Objects.nonNull(subResDTO.getReceiptContractUpdatedDate()) ? subResDTO.getReceiptContractUpdatedDate() : new Date());
            // lấy list log timeline của mỗi service
            log.info("==============Get time line by id of orderReceiveCombo============");
            logDTOList = orderServiceReceiveRepository.getLogByOrderServiceComId(order.getId());
            if (!CollectionUtils.isEmpty(logDTOList)) {
                orderDHSXKD.setTimeLine(logDTOList.stream().map(OrderServiceLogDTO::new).collect(Collectors.toSet()));
            }
        });

        //lay thông tin DHSXKD tra ve
        if (!serviceOfCombo.isEmpty()) {
            orderSubDTO.setListService(serviceOfCombo);
        }
        orderSubDTO.setComboOwner(Objects.nonNull(combo.getComboOwner()) ? ServiceOwnerEnum.valueOf(combo.getComboOwner()) : ServiceOwnerEnum.UNSET);
        orderSubDTO.setPaymentStatus(Objects.nonNull(subResDTO.getPaymentStatus()) ? DHSXKDPaymentStatusEnum.getNameByKeyDHSXKD(subResDTO.getPaymentStatus()) : null);
        orderSubDTO.setCreateAt(sub.getCreatedAt());
        orderSubDTO.setStartAt(sub.getStartedAt());
        orderSubDTO.setSubscriptionCode(sub.getSubCode());
        orderSubDTO.setTransactionCode(subResDTO.getExchangeCode());
        orderSubDTO.setOrderReceiveId(orderServiceReceive.getOrderReceiveId());

        // update trạng thái thanh toán cho billing
        billsService.updateStatusPaymentForOrderService(sub, subResDTO);
        return orderSubDTO;
    }

    /**
     * Lưu thông tin order combo khi trạng thái đơn hàng thay đổi
     *
     */
    private OrderServiceCombo updateStatusOfOrderServiceCombo(OrderServiceCombo saveDB, TrackingOrderServiceResDTO tracking) {
        saveDB.setSetupStatus(Objects.nonNull(tracking.getSetupStatusId()) ?
                String.valueOf(tracking.getSetupStatusId()) : "");
        saveDB.setOrderStatus(Objects.nonNull(tracking.getOrderStatusId()) ? String.valueOf(tracking.getOrderStatusId()) : "");
        orderServiceComboRepository.save(saveDB);
        return saveDB;
    }

    /**
     * Save receive log of combo order
     */
    private void createOrderComboLog(Long id, TrackingOrderServiceResDTO subResDTO) {
        orderServiceReceiveLogRepository.save(new OrderServiceReceiveLog(null, null,
                Objects.nonNull(subResDTO.getOrderStatusId()) ? subResDTO.getOrderStatusId().toString() : null,
                Objects.nonNull(subResDTO.getSetupStatusId()) ? subResDTO.getSetupStatusId().toString() : null,
                Objects.nonNull(subResDTO.getPaymentStatusId()) ? subResDTO.getPaymentStatusId().toString() : null,
                subResDTO.getEmployeeCode(),
                subResDTO.getEmployeeName(),
                subResDTO.getEmployeePhoneNumber(),
                subResDTO.getAmRoom(),
                subResDTO.getAmEmail(),
                subResDTO.getSigningDate(),
                subResDTO.getReceiptContractUpdatedDate(),
                subResDTO.getSetupContractUpdatedDate(), id));
    }

    /**
     * save log
     *
     */
    @Transactional
    public OrderServiceReceive updateOrderServiceReceive(OrderServiceReceive orderServiceReceive, TrackingOrderServiceResDTO resDTO,
        Long serviceId, Long comboId, String orderStatusId, Subscription sub) {

        if (orderStatusId != null && orderServiceReceive.getOrderStatus() != null && !orderStatusId.equals(orderServiceReceive.getOrderStatus()) && orderStatusId.equals(ORDER_STATUS_COMPLETED)) {
            OrderServiceReceiveSendMail orderService = orderServiceReceiveSendMailRepository
                .findFirstByTransactionCodeAndSubscriptionId(orderServiceReceive.getTransactionCode(), sub.getId()).orElse(null);
            if (ObjectUtils.isNotEmpty(orderService) && orderService.getIsSendEmail().equals(YesNoEnum.NO.value)) {
                // send mail
                sendMail(sub, orderServiceReceive, null);
                orderService.setIsSendEmail(YesNoEnum.YES.value);
                orderServiceReceiveSendMailRepository.save(orderService);
            } else if (ObjectUtils.isEmpty(orderService)) {
                sendMail(sub, orderServiceReceive, null);
                orderService =
                    OrderServiceReceiveSendMail.builder().subscriptionId(sub.getId())
                        .transactionCode(orderServiceReceive.getTransactionCode()).isSendEmail(YesNoEnum.YES.value).build();
                orderServiceReceiveSendMailRepository.save(orderService);
            }
        }

        orderServiceReceive.setPaymentStatus(Objects.nonNull(resDTO.getPaymentStatusId()) ?
            String.valueOf(resDTO.getPaymentStatusId()) : null);
        orderServiceReceive.setOrderStatus(orderStatusId);
        orderServiceReceive.setSetupStatus(Objects.nonNull(resDTO.getSetupStatusId()) ?
            String.valueOf(resDTO.getSetupStatusId()) : null);
        orderServiceReceive.setAmName(resDTO.getEmployeeName());
        orderServiceReceive.setAmCode(resDTO.getEmployeeCode());
        orderServiceReceive.setAmDeparment(resDTO.getAmRoom());
        orderServiceReceive.setAmPhoneNo(resDTO.getEmployeePhoneNumber());
        orderServiceReceive.setAmEmail(resDTO.getAmEmail());
        orderServiceReceive.setFkey(resDTO.getFKey());
        orderServiceReceive.setServiceId(serviceId);
        orderServiceReceive.setComboId(comboId);
        orderServiceReceiveRepository.save(orderServiceReceive);

        // Check update trạng thái subscription
        Set<Long> orders = new HashSet<>(Objects.nonNull(resDTO.getOrderStatusId()) ? Collections.singletonList(resDTO.getOrderStatusId()) :
            (Objects.nonNull(resDTO.getOrderStatusId()) ? Collections.singletonList(Long.valueOf(orderServiceReceive.getOrderStatus())) : new HashSet<>()));
        Set<Long> statusIds = smeProgressRepository.getStatusByStatusIds(orders).stream().map(SmeProgress::getId).collect(Collectors.toSet());
        Subscription result = sub;
        MigrationServiceTypeEnum migrationServiceType = MigrationServiceTypeEnum.valueOf(subscriptionRepository.getCategoriesMigrationIdBySubId(sub.getId()));
        // 03/10/2024: Nếu là sub của dv ban KHCN -> ko update subStatus
        // Nếu ko phải sub ban kHCN -> giữ nguyên logic cũ (2021)
        if (Objects.equals(migrationServiceType, MigrationServiceTypeEnum.UNSET)) {
            if (!Objects.equals(sub.getStatus(), SubscriptionStatusEnum.FUTURE.value) &&
                !Objects.equals(sub.getStatus(), SubscriptionStatusEnum.CANCELED.value)
                && statusIds.contains(SmeProgressEnum.DEPLOYED.getValue())) {
                // Update db nếu còn 1 service nào đó trong combo ở trạng thái deploy
                sub.setStatus(SubscriptionStatusEnum.FUTURE.value);
                result = subscriptionRepository.save(sub);
            } else if (Objects.equals(sub.getStatus(), SubscriptionStatusEnum.FUTURE.value) &&
                statusIds.stream().filter(x -> Objects.equals(x, SmeProgressEnum.FINISHING.getValue())).collect(Collectors.toList())
                    .stream().count() == statusIds.stream().count()) {
                // Update db nếu tất cả các service trong combo đã hoàn thành
                sub.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                result = subscriptionRepository.save(sub);
            } else if (orders.contains(CANCEL_SUBSCRIPTION_DHSX) && !Objects.equals(sub.getStatus(), SubscriptionStatusEnum.CANCELED.value)) {
                // update thông tin cho subscription nếu trạng thái của dhsx là thoái trả khác với dữ liệu DB
                sub.setStatus(SubscriptionStatusEnum.CANCELED.value);
                result = subscriptionRepository.save(sub);
            }
            // Lưu lịch sử thay đổi trạng thái thuê bao
            subscriptionHistoryService.saveStatusHistory(result);
        }

        return orderServiceReceive;
    }


    @Override
    public void sendMail(Subscription subscription, OrderServiceReceive orderServiceReceive, String actor) {
        Map<String, String> mapDefaultValue = new HashMap<>();
        mapDefaultValue.putIfAbsent(ParamEmailEnum.CODE_TRANSACTION.getValue(), orderServiceReceive.getTransactionCode());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.LINK_DETAIL.getValue(), StringUtils.EMPTY);
        INameAndEmailDTO userInfo = userRepository.findNameAndEmailById(subscription.getUserId());
        String userName = userInfo != null ? userInfo.getName() : StringUtils.EMPTY;
        String userEmail = userInfo != null ? userInfo.getEmail() : StringUtils.EMPTY;
        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(), StringUtils.isEmpty(userName) ? StringUtils.EMPTY : userName);
        EmailTemplate emailTemplate;
        if (Objects.nonNull(subscription.getPricingId())) {
            IPricingNameServiceNameDTO pricingInfo = pricingRepository.findProductNameById(subscription.getPricingId(),
                DeletedFlag.NOT_YET_DELETED.getValue());
            String serviceName = pricingInfo != null ? pricingInfo.getServiceName() : StringUtils.EMPTY;
            String pricingName = pricingInfo != null ? pricingInfo.getPricingName() : StringUtils.EMPTY;
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_SERVICE.getValue(),
                StringUtils.isEmpty(serviceName) ? StringUtils.EMPTY : serviceName);
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_PRICING.getValue(),
                StringUtils.isEmpty(pricingName) ? StringUtils.EMPTY : pricingName);
            emailTemplate = emailTemplateService.replaceParamEmailTemplate(EmailCodeEnum.SC42.getValue(), mapDefaultValue, actor).orElse(null);

        } else {
            IPricingNameServiceNameDTO comboPlanInfo = comboPlanRepository.findProductNameById(subscription.getComboPlanId(),
                DeletedFlag.NOT_YET_DELETED.getValue());
            String serviceName = comboPlanInfo != null ? comboPlanInfo.getServiceName() : StringUtils.EMPTY;
            String pricingName = comboPlanInfo != null ? comboPlanInfo.getPricingName() : StringUtils.EMPTY;
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO.getValue(),
                StringUtils.isEmpty(serviceName) ? StringUtils.EMPTY : serviceName);
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO_PRICING.getValue(),
                StringUtils.isEmpty(pricingName) ? StringUtils.EMPTY : pricingName);
            emailTemplate = emailTemplateService.replaceParamEmailTemplate(EmailCodeEnum.SCB42.getValue(), mapDefaultValue, actor).orElse(null);
        }
        if (Objects.nonNull(emailTemplate)) {
            emailService.save(StringUtils.isEmpty(userEmail) ? StringUtils.EMPTY : userEmail, emailTemplate);
        }
    }

    @Override
    public List<String> findAllTinSubscriptionOS(String tin, String portalType) {
        //khi PortalType là DEV thì trả về page subscription của cty đó.
        Long parentId = AuthUtil.getCurrentParentId();
        List<String> userRes = new ArrayList<>();
        if (PortalType.DEV.name().equals(portalType)) {
            userRes = orderServiceReceiveRepository
                .findAllTinOfSubOS(tin, PortalType.DEV.getType(), parentId, -1L);
        }
        //khi PortalType là ADMIN thi trả về tất cả subscription.
        else if (PortalType.ADMIN.name().equals(portalType)) {
            Long provinceId = -1L;
            if (Objects.nonNull(AuthUtil.getDepartment()) && Objects.nonNull(AuthUtil.getDepartment().getProvinceId())) {
                provinceId = AuthUtil.getDepartment().getProvinceId();
            }
            userRes = orderServiceReceiveRepository
                .findAllTinOfSubOS(tin, PortalType.ADMIN.getType(), -1L, provinceId);
        }
        return userRes;
    }

    /**
     * save log
     *
     */
    @Transactional
    public void createLog(Long id,TrackingOrderServiceResDTO resDTO) {
        orderServiceReceiveLogRepository.save(new OrderServiceReceiveLog(null, id,
            Objects.nonNull(resDTO.getOrderStatusId()) ? resDTO.getOrderStatusId().toString() : null,
            Objects.nonNull(resDTO.getSetupStatusId()) ? resDTO.getSetupStatusId().toString() : null,
            Objects.nonNull(resDTO.getPaymentStatusId()) ? resDTO.getPaymentStatusId().toString() : null,
            resDTO.getEmployeeName(),
            resDTO.getEmployeeCode(),
            resDTO.getAmRoom(),
            resDTO.getEmployeePhoneNumber(),
            resDTO.getSigningDate(),
            Objects.nonNull(resDTO.getReceiptContractUpdatedDate()) ? resDTO.getReceiptContractUpdatedDate() : new Date(),
            Objects.nonNull(resDTO.getSetupContractUpdatedDate()) ? resDTO.getSetupContractUpdatedDate() : new Date()));
    }

    @Override
    @Transactional
    public List<DataGetListDHSXKDResponseDTO> callApiDHSXKDGetListOrderService(boolean isSyncBatch, String startDate, String endDate) {
        // Lấy provinceId khi admin đồng bộ: SUPER_ADMIN -> đồng bộ 64 tỉnh thành, ADMIN tỉnh -> đồng bộ tỉnh thành của họ
        Long provinceId = AuthUtil.PARENT_ID;
        Long currentUserId = AuthUtil.getCurrentUserId();
        List<String> lstRole = Arrays.asList(RoleType.ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT.getValue(), RoleType.FULL_ADMIN.getValue());
        if (BooleanUtils.isFalse(isSyncBatch) && currentUserId != null && AuthUtil.checkUserRoles(lstRole)) {
            User actor = userService.findByIdAndDeletedFlag(currentUserId, DeletedFlag.NOT_YET_DELETED.getValue());
            if (Objects.nonNull(actor.getDepartmentId())) {
                Department department = departmentsRepository.findByIdAndDeletedFlag(actor.getDepartmentId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.DEPARTMENT, ErrorKey.ID, String.valueOf(actor.getDepartmentId())));
                provinceId = Objects.nonNull(department.getProvinceId()) ? department.getProvinceId() : AuthUtil.PARENT_ID;
            }
        }
        // Lấy danh sách 63 tỉnh thành của Việt Nam
        List<Province> provinceList = BooleanUtils.isTrue(isSyncBatch) ?
            provinceRepository.getAllProvinceInNature(VIET_NAM_ID) :
            provinceRepository.getAllProvinceForSubscription(provinceId);

        try {
            // Khởi tạo danh sách để lưu giá trị lịch sử call api tích hợp
            List<HistoryCallApiDHSX> historyCallApiDHSXList = new ArrayList<>();
            List<DataGetListDHSXKDResponseDTO> orderServiceList = new ArrayList<>();
            if (!provinceList.isEmpty()) {
                provinceList.forEach(province -> {
                    long start = System.currentTimeMillis();
                    // Lấy thông tin order service của bên DHSXKD với từng id tỉnh
                    GetListOrderServiceReqDTO req = GetListOrderServiceReqDTO.builder()
                        .provinceId(province.getId())
                        .startDate(startDate)
                        .endDate(endDate)
                        .build();
                    DHSXKDResponseListOrderDTO res = executiveProducerService.getListOrderServiceDHXSKD(req);

                    Integer apiStatus = Objects.equals(ErrorConst.MESSAGE_SUCCESS, res.getErrorCode())
                        ? StatusActivityLogEnum.SUCCESS.getStatus() : StatusActivityLogEnum.FAIL.getStatus();
                    // Nếu call thành công và có data trong list ở response thì add thêm

                    if (Objects.nonNull(res) && Objects.equals(apiStatus, StatusActivityLogEnum.SUCCESS.getStatus()) && !CollectionUtils.isEmpty(res.getData())) {
                        orderServiceList.addAll(res.getData());
                    }
                    // Thêm giá trị lịch sử call api
                    HistoryCallApiDHSX callApiDHSX = executiveProducerService.convertToHistoryCallApiDHSX(dhsxkdApiGetListOrderService,
                        req, res, apiStatus, PartnerCallAPI.DHSXKD, null);
                    if (Objects.nonNull(callApiDHSX)) {
                        historyCallApiDHSXList.add(callApiDHSX);
                    }
                    log.info("callApiDHSXKDGetListOrderService: get order service for province ({}, {}) ends after {} ms",
                        province.getId(), province.getName(), System.currentTimeMillis() - start);
                });
            }
            // Lưu lịch sử call api
            if (!CollectionUtils.isEmpty(historyCallApiDHSXList)) {
                historyCallApiDHSXRepository.saveAll(historyCallApiDHSXList);
            }
            return orderServiceList;

        } catch (Exception e) {
            throw exceptionFactory.badRequest(MessageKeyConstant.NO_HAVE_ACCESS_DHSXKD, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.ID);
        }
    }

    @Override
    public Page<OrderServiceListResponseDTO> getListOrderService(OrderServiceListRequestDTO requestDTO, Pageable pageable) {
        FilterReqDefault.setOrderRequestDefault(requestDTO);

        requestDTO.setServiceName(StringUtils.isEmpty(requestDTO.getServiceName()) ? StringUtils.EMPTY
                : SqlUtils.optimizeSearchLike(requestDTO.getServiceName()));
        requestDTO.setDevName(StringUtils.isEmpty(requestDTO.getDevName()) ? StringUtils.EMPTY
                : SqlUtils.optimizeSearchLike(requestDTO.getDevName()));

        Long userId = AuthUtil.getCurrentParentId();
        return orderServiceReceiveRepository.getListOrderService(requestDTO, userId, pageable);
    }

    @Override
    public Page<OrderServiceListResponseClassDTO> getListOrderServiceDev(OrderServiceListRequestDTO requestDTO, Pageable pageable) {
        FilterReqDefault.setOrderRequestDefault(requestDTO);
        Long userId = AuthUtil.getCurrentParentId();
        Long provinceId = -1L;
        Page<OrderServiceListResponseDTO> orderServiceList =  orderServiceReceiveRepository.getListOrderServiceDevAdmin(requestDTO, userId, provinceId, -1L, pageable);
        List<OrderServiceListResponseClassDTO> orderServiceDTOS = new ArrayList<>();
        orderServiceList.forEach(or ->{
            OrderServiceListResponseClassDTO orderServiceDTO = new OrderServiceListResponseClassDTO();
            BeanUtils.copyProperties(or, orderServiceDTO);
            orderServiceDTO.setStatusHD(Objects.nonNull(or.getStatusHD()) ?
                DHSXKDOrderStatusEnum.getNameByKeyDHSXKD(Integer.valueOf(or.getStatusHD())) : null);
            orderServiceDTOS.add(orderServiceDTO);
        });
        return new PageImpl<>(orderServiceDTOS, orderServiceList.getPageable(), orderServiceList.getTotalElements());
    }

    @Override
    public Page<OrderServiceListResponseClassDTO> getListOrderServiceAdmin(OrderServiceListRequestDTO requestDTO, Pageable pageable) {
        FilterReqDefault.setOrderRequestDefault(requestDTO);
        Long userId = -1L;
        Long provinceId = -1L;
        Long currentUserId = AuthUtil.getCurrentUserId();
        List<String> roles = userRepository.getRole(currentUserId);
        Long createBy = !CollectionUtils.isEmpty(roles) && roles.contains(RoleType.AM.name()) ? currentUserId : -1L;
        if (Objects.nonNull(AuthUtil.getDepartment())) {
            provinceId = Objects.nonNull(AuthUtil.getDepartment().getProvinceId()) ? AuthUtil.getDepartment().getProvinceId() : -1L;
        }
        Page<OrderServiceListResponseDTO> orderServiceList =  orderServiceReceiveRepository.getListOrderServiceDevAdmin(requestDTO, userId, provinceId, createBy, pageable);
        List<OrderServiceListResponseClassDTO> orderServiceDTOS = new ArrayList<>();

        orderServiceList.getContent().forEach(or -> {
            OrderServiceListResponseClassDTO orderServiceDTO = new OrderServiceListResponseClassDTO();
            BeanUtils.copyProperties(or, orderServiceDTO);
            orderServiceDTO.setStatusHD(Objects.nonNull(or.getStatusHD()) ?
                DHSXKDOrderStatusEnum.getNameByKeyDHSXKD(Integer.valueOf(or.getStatusHD())) : null);
            orderServiceDTOS.add(orderServiceDTO);
        });
        return new PageImpl<>(orderServiceDTOS, orderServiceList.getPageable(), orderServiceList.getTotalElements());
    }

    @Override
    public String getSortBySubOrder(String sortBy) {
        String property = sortBy.split(Constant.Word.COMMA)[0];
        String direction = sortBy.split(Constant.Word.COMMA)[1];
        if (Objects.equals(property, STATUS_ID) || Objects.equals(property, STATUS_NAME)){
            sortBy = "statusOrder".concat(Constant.Word.COMMA).concat(direction);
        }
        return sortBy;
    }
}
