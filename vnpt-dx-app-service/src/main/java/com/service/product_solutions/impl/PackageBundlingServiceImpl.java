package com.service.product_solutions.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.dto.events.ProductEventMetadata;
import com.entity.product_solutions.*;
import com.entity.product_solutions.Package;
import com.repository.product_solutions.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.common.Constant.SOLUTION_PACKAGE;
import com.constant.SeoTypeCodeConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.suggestions.SuggestionModeEnum;
import com.dto.pricing.PricingTaxRes;
import com.dto.product_solustions.ApprovedReqDTO;
import com.dto.product_solustions.CalculatePriceInfoRequestDTO;
import com.dto.product_solustions.FeatureDetailDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.AddonSPDVDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.MultiPlanSPDVDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.PricingSPDVDTO;
import com.dto.product_solustions.IGetCouponMcDTO;
import com.dto.product_solustions.IGetCouponPackageDTO;
import com.dto.product_solustions.IGetFeaturePackageDTO;
import com.dto.product_solustions.IGetListCreatedBy;
import com.dto.product_solustions.IGetListPackageDraftResDTO;
import com.dto.product_solustions.IGetListProduct;
import com.dto.product_solustions.IGetListServiceProductResDTO;
import com.dto.product_solustions.IGetPackItemDTO;
import com.dto.product_solustions.IGetPackageDetailDTO;
import com.dto.product_solustions.IGetSPDVBundlingDTO;
import com.dto.product_solustions.IGetSolutionInPackageDTO;
import com.dto.product_solustions.IProductSolutionSmeDTO;
import com.dto.product_solustions.MappingCalculate;
import com.dto.product_solustions.PackageBundlingCreateDTO;
import com.dto.product_solustions.PackageBundlingCreateDTO.PackagePlanCreateDTO;
import com.dto.product_solustions.PackageBundlingCreateDTO.PackagePlanCreateDTO.PackageAddonCreateDTO;
import com.dto.product_solustions.PackageDetailResDTO;
import com.dto.product_solustions.PackageDetailResDTO.TaxFeeDTO;
import com.dto.product_solustions.PackageDetailSmeResDTO;
import com.dto.product_solustions.PackageListResDTO;
import com.dto.product_solustions.PackageListResDTO.McCouponDTO;
import com.dto.product_solustions.PackageListResDTO.ProductAddonDTO;
import com.dto.product_solustions.PackageListResDTO.ProductItemDTO;
import com.dto.product_solustions.PackagePlanPriceInfoDTO;
import com.dto.product_solustions.PriceInfoDTO;
import com.dto.product_solustions.ProductSolutionSmeDTO;
import com.dto.product_solustions.SolutionByDomainPageDTO;
import com.dto.quotation.FeeDetail;
import com.dto.serviceGroup.TaxDetailDTO;
import com.dto.services.sugesstion.ServiceCommonDetailDTO;
import com.entity.coupons.Coupon;
import com.entity.coupons.CouponDraft;
import com.entity.pricing.PricingMultiPlan;
import com.entity.seo.Seo;
import com.enums.ApproveStatusEnum;
import com.enums.DisplayStatus;
import com.enums.ServiceViewEnum.ServiceViewTypeEnum;
import com.enums.product_solutions.ObjectTypeEnum;
import com.exception.ErrorKey;
import com.exception.ErrorKey.ProductSolutionError;
import com.exception.Resources;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.ProductSortEnum;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.services.ProductClassificationEnum;
import com.onedx.common.constants.values.ExceptionConstants;
import com.onedx.common.constants.values.JSONBConstant;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant.ProductSolutions;
import com.onedx.common.utils.ObjectMapperUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.addons.AddonRepository;
import com.repository.addons.AddonsTaxRepository;
import com.repository.coupons.CouponDraftRepository;
import com.repository.coupons.CouponRepository;
import com.repository.pricing.PricingDraftRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.pricing.PricingSetupFeeTaxRepository;
import com.repository.pricing.PricingTaxRepository;
import com.repository.subscriptions.SubscriptionMetadataRepository;
import com.service.coupon.CouponService;
import com.service.events.EventsService;
import com.service.faq.TopicFaqService;
import com.service.feature.impl.FeatureServiceImpl;
import com.service.product_solutions.FeatureMappingService;
import com.service.product_solutions.PackageBundlingService;
import com.service.product_solutions.PackageMappingService;
import com.service.product_solutions.ProductApprovalHistoryService;
import com.service.product_solutions.SolutionPackageService;
import com.service.rating.ServiceViewService;
import com.service.seo.SeoService;
import com.service.serviceSuggestion.ServiceSuggestionService;
import com.service.services.impl.ServicesServiceImpl;
import com.service.suggestions.SuggestionMappingService;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

@Service
@Slf4j
@RequiredArgsConstructor
public class PackageBundlingServiceImpl implements PackageBundlingService {

    private final PackageRepository packageRepository;
    private final PackageDraftRepository packageDraftRepository;
    private final SeoService seoService;
    private final FeatureServiceImpl featureService;
    private final SolutionPackageService solutionPackageService;
    private final TopicFaqService topicFaqService;
    private final SuggestionMappingService suggestionMappingService;
    private final PackageMappingService packageMappingService;
    private final FeatureMappingService featureMappingService;
    private final SubscriptionMetadataRepository subscriptionMetadataRepository;
    private final PricingTaxRepository pricingTaxRepository;
    private final AddonsTaxRepository addonsTaxRepository;
    private final PricingSetupFeeTaxRepository pricingSetupFeeTaxRepository;
    private final SolutionDomainRepository solutionDomainRepository;
    private final ServicesServiceImpl servicesService;
    private final CouponService couponService;
    private final ServiceViewService serviceViewService;
    private final ProductSolutionRepository productSolutionRepository;
    private final ServiceSuggestionService serviceSuggestionService;
    private final SolutionPackageRepository solutionPackageRepository;
    private final SystemParamService systemParamService;

    private final ExceptionFactory exceptionFactory;
    private final ProductApprovalHistoryService productApprovalHistoryService;

    @Autowired
    private PackageMappingRepository packageMappingRepository;

    @Autowired
    private EventsService eventsService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private PackageItemPromotionsRepository packageItemPromotionsRepository;

    @Autowired
    private PackageItemAddonPromotionsRepository packageItemAddonPromotionsRepository;

    @Autowired
    private PackageItemRepository packageItemRepository;

    @Autowired
    private PackageAddonRepository packageAddonRepository;

    @Autowired
    private AddonRepository addonRepository;

    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;

    @Autowired
    private CouponRepository couponRepository;

    @Autowired
    private CouponDraftRepository couponDraftRepository;

    @Autowired
    private PricingRepository pricingRepository;

    @Autowired
    private PricingDraftRepository pricingDraftRepository;

    @Autowired
    private PackageItemPriceRepository packageItemPricesRepository;

    @Value("${solution.list.per-domain.max-size:4}")
    private Integer rank;

    private static final String CAU_HINH_GOI_BUNDLING = "CAU_HINH_GOI_BUNDLING";

    @Override
    public Page<PackageListResDTO> listPackages(String value, Integer isName, Integer isCode, ApproveStatusEnum approveStatus,
                                                Integer displayStatus, CustomerTypeEnum customerType, List<Long> createdIds,
                                                List<Long> productIds, List<String> customerTypeCondition, Pageable pageable) {
        // TODO: Dev sẽ chỉ được xem và quản lý các Gói bundling do mình tạo
        if (Objects.isNull(createdIds) || createdIds.isEmpty()) {
            createdIds = new ArrayList<>(Collections.singletonList(-1L));
        }
        if (Objects.isNull(productIds) || productIds.isEmpty()) {
            productIds = new ArrayList<>(Collections.singletonList(-1L));
        }

        Long userId = AuthUtil.getCurrentParentId();
        userId = AuthUtil.isDev(userId) ? userId : -1L;

        Page<IGetListPackageDraftResDTO> page = packageDraftRepository.listPackageDraft(value, isName, isCode, approveStatus.value,
                displayStatus, customerType.name(), createdIds, productIds, userId, customerTypeCondition, pageable);
        Set<Long> ids = new HashSet<>();
        page.getContent().forEach(item -> ids.addAll(item.getProductIdsValue()));
        List<IGetListServiceProductResDTO> list = packageDraftRepository.listServiceProduct(ids);

        Map<Long, List<IGetListServiceProductResDTO>> map = list.stream()
                .collect(Collectors.groupingBy(IGetListServiceProductResDTO::getPackageDraftId));
        List<PackageListResDTO> listPackageResDTOS = page.stream().map(
                packageDTO -> {
                    PackageListResDTO packageListResDTO = new PackageListResDTO();
                    packageListResDTO.setId(packageDTO.getId());
                    packageListResDTO.setName(packageDTO.getPackageName());
                    packageListResDTO.setCreatedAt(packageDTO.getCreatedAt());
                    packageListResDTO.setRecommended(packageDTO.getRecommended());
                    packageListResDTO.setApprovalStatus(ApproveStatusEnum.valueOf(packageDTO.getState()));
                    packageListResDTO.setVisibility(DisplayStatus.valueOf(packageDTO.getVisibility()));
                    packageListResDTO.setProviderName(packageDTO.getProviderName());
                    packageListResDTO.setIconUrl(packageDTO.getIconUrl());
                    return packageListResDTO;
                }).collect(Collectors.toList());
        for (PackageListResDTO packageDTO : listPackageResDTOS) {
            List<IGetListServiceProductResDTO> productServices = map.get(packageDTO.getId());
            if (Objects.isNull(productServices)) {
                continue;
            }
            packageDTO.setPrice(productServices.stream().map(IGetListServiceProductResDTO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            packageDTO.setProducts(productServices);
        }
        return new PageImpl<>(listPackageResDTOS, page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<IGetListCreatedBy> listCreatedByInSearch() {
        return packageDraftRepository.getListCreatedBy();
    }

    @Override
    public List<IGetListProduct> listProductInSearch() {
        return packageDraftRepository.getListProduct();
    }

    @Override
    public Page<GetSPDVBundlingDTO> getSPDVBundling(String value, Integer isNameService, Integer isNamePricing, List<Long> categoryIds, Long providerId,
                                                    String manufactureName, List<String> customerTypes, String paymentCycle, ProductClassificationEnum classification,
                                                    Integer serviceType, Pageable pageable) {

        providerId = AuthUtil.isDev() ? AuthUtil.getCurrentParentId() : providerId;
        Page<IGetSPDVBundlingDTO> lstResult = packageRepository.getSPDVBundling(value, isNameService, isNamePricing, categoryIds, providerId, manufactureName,
                customerTypes.toString(), paymentCycle, classification.getValue(), serviceType, pageable);

        Set<Long> pricingIds = lstResult.getContent().stream().flatMap(item -> item.getLstPricing().stream().map(PricingSPDVDTO::getId)).collect(
                Collectors.toSet());
        Set<Long> addonIds = lstResult.getContent().stream().flatMap(item -> item.getLstPricing().stream().flatMap(addon -> addon.getLstAddon().stream().map(AddonSPDVDTO::getId))).collect(
                Collectors.toSet());
        // lấy thông tin tax
        List<PricingTaxRes> pricingTaxList = pricingTaxRepository.getPricingTaxByIds(pricingIds);
        List<PricingTaxRes> addonTaxList = addonsTaxRepository.getAddonTaxByIds(addonIds);

        // tạo map pricing và addon tax
        Map<Long, List<PricingTaxRes>> pricingTaxMap = pricingTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));
        Map<Long, List<PricingTaxRes>> addonTaxMap = addonTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));

        return new PageImpl<>(lstResult.getContent().stream().map(item -> {
            GetSPDVBundlingDTO dto = new GetSPDVBundlingDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setLstPricing(item.getLstPricing());
            dto.setLstVariant(item.getLstVariant());
            dto.setLstAttribute(item.getLstAttribute());

            // update lai giá tri tax
            dto.getLstPricing().forEach(pricing -> {
                List<PricingTaxRes> pricingTaxRes = pricingTaxMap.get(pricing.getId());

                if (hasTax(pricingTaxRes)) {
                    double totalTaxPercent = calculateTotalTax(pricingTaxRes);

                    pricing.getMultiPlans().forEach(plan -> {
                        PricingPlanEnum planEnum = PricingPlanEnum.valueOf(plan.getPricingPlan());
                        updatePrePlan(plan, planEnum, totalTaxPercent);
                    });
                }

                if (Objects.nonNull(pricing.getLstAddon())) {
                    pricing.getLstAddon().forEach(addon -> {
                        List<PricingTaxRes> addonTaxes = addonTaxMap.get(addon.getId());

                        if (hasTax(addonTaxes)) {
                            double totalTaxPercent = calculateTotalTax(addonTaxes);
                            PricingPlanEnum planEnum = PricingPlanEnum.valueOf(addon.getAddonPlan());
                            updatePreAddonPlan(addon, planEnum, totalTaxPercent);
                        }
                    });
                }
            });


            return dto;
        }).collect(Collectors.toList()), lstResult.getPageable(), lstResult.getTotalElements());
    }

    /**
     * Check thuế đã bao gồm chưa
     */
    private boolean hasTax(List<PricingTaxRes> taxList) {
        return taxList != null && !taxList.isEmpty() && Objects.equals(taxList.get(0).getHasTax(), 1);
    }

    /**
     * Tính tổng thuế
     */
    private double calculateTotalTax(List<PricingTaxRes> taxList) {
        return taxList.stream()
                .map(PricingTaxRes::getPercent)
                .reduce(0.0, Double::sum);
    }

    /**
     * Tính tiền theo chiến lược định giá
     */
    private void updatePrePlan(MultiPlanSPDVDTO plan, PricingPlanEnum planEnum, double totalTaxPercent) {
        switch (planEnum) {
            case FLAT_RATE:
            case UNIT:
                BigDecimal priceTax = plan.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                plan.setPrice(plan.getPrice().subtract(priceTax));
                break;

            case TIER:
            case VOLUME:
            case STAIR_STEP:
                if (Objects.nonNull(plan.getUnitLimiteds())) {
                    plan.getUnitLimiteds().forEach(unit -> {
                        BigDecimal unitPriceTax = unit.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                        unit.setPrice(unit.getPrice().subtract(unitPriceTax));
                    });
                }
                break;

            default:
                break;
        }
    }

    /**
     * Tính tiền theo chiến lược định giá
     */
    private void updatePreAddonPlan(AddonSPDVDTO addon, PricingPlanEnum planEnum, double totalTaxPercent) {
        switch (planEnum) {
            case FLAT_RATE:
            case UNIT:
                BigDecimal priceTax = addon.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                addon.setPrice(addon.getPrice().subtract(priceTax));
                break;

            case TIER:
            case VOLUME:
            case STAIR_STEP:
                if (Objects.nonNull(addon.getUnitLimiteds())) {
                    addon.getUnitLimiteds().forEach(unit -> {
                        BigDecimal unitPriceTax = unit.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                        unit.setPrice(unit.getPrice().subtract(unitPriceTax));
                    });
                }

                if (Objects.nonNull(addon.getMultiPlans())) {
                    addon.getMultiPlans().forEach(plan -> {
                        BigDecimal planPriceTax = plan.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                        plan.setPrice(plan.getPrice().subtract(planPriceTax));

                        if (Objects.nonNull(plan.getUnitLimiteds())) {
                            plan.getUnitLimiteds().forEach(unit -> {
                                BigDecimal unitPriceTax = unit.getPrice().divide(BigDecimal.ONE.add(BigDecimal.valueOf(totalTaxPercent)), RoundingMode.HALF_UP);
                                unit.setPrice(unit.getPrice().subtract(unitPriceTax));
                            });
                        }
                    });
                }
                break;

            default:
                break;
        }
    }


    @Override
    public PackageDetailResDTO getPackageById(Long packageDraftId, ApproveStatusEnum type) {
        var packageDraft = packageDraftRepository.findById(packageDraftId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PACKAGE, ErrorKey.ID, String.valueOf(packageDraftId)));
        // DEV chỉ được xem thông tin các package mà mình tạo
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        if (AuthUtil.isDev() &&
                !Objects.equals(packageDraft.getProviderId(), currentParentId) && // Không thuộc doanh nghiệp dev
                !Objects.equals(packageDraft.getCreatedBy(), currentUserId)) { // Không phải người tạo trực tiếp
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
        }
        PackageDetailResDTO response = new PackageDetailResDTO();
        IGetPackageDetailDTO packageDetail;
        if (ApproveStatusEnum.UNAPPROVED.equals(type)) {
            packageDetail = packageDraftRepository.getPackageDetailDraft(packageDraftId);

        } else {
            packageDetail = packageDraftRepository.getPackageDetail(packageDraftId);
        }

        if (Objects.isNull(packageDetail)) {
            throw exceptionFactory.resourceNotFound(Resources.PACKAGE, ErrorKey.DRAFT_ID, String.valueOf(packageDraftId));
        }

        BeanUtils.copyProperties(packageDetail, response);
        response.setApplyCondition(packageDetail.getApplyCondition());
        response.setGuidelines(packageDetail.getGuidelines());
        response.setLstPackageItem(packageDetail.getLstPackageItem());
        response.setLstFeature(packageDetail.getLstFeature());
        response.setLstSeo(packageDetail.getLstSeo());
        response.setBannerUrls(packageDetail.getLstBanner());

        // lấy danh sách topic
        response.setLstTopic(topicFaqService.getLstTopic(packageDraftId, ObjectTypeEnum.PACKAGE.value));

        // Danh sách SPDV gợi ý
        response.setSuggestionMode(packageDraft.getSuggestionMode());
        if (packageDraft.getSuggestionMode() == SuggestionModeEnum.MANUAL) {
            response.setSuggestedProducts(suggestionMappingService.getSuggestionsByDraftIdForDev(ObjectTypeEnum.PACKAGE, packageDraft.getId()));
        }

        // lĩnh vực áp dụng
        response.setLstDomain(packageDetail.getLstDomain());
        // danh mục
        response.setLstCategory(packageDetail.getLstCategory());

        // lấy danh sách giải pháp
        List<IGetSolutionInPackageDTO> lstSolution = packageDraftRepository.getLstSolutionInPackage(packageDetail.getLstSolutionId());
        response.setLstSolution(lstSolution.stream().map(solution -> PackageDetailResDTO.SolutionInPackageDTO.builder()
                .id(solution.getId())
                .name(solution.getName())
                .lstDomain(solution.getLstDomain())
                .lstCategory(solution.getLstCategory())
                .lstPackage(solution.getLstPackageInSolution())
                .build()).collect(Collectors.toList()));

        // Giá và khuyến mại
        getPackageItemPriceAndCoupon(response.getLstPackageItem());

        return response;
    }

    /**
     * Tính toán thông tin item trong gói
     */
    private void getPackageItemPriceAndCoupon(List<ProductItemDTO> lstItem) {
        // lấy thông tin thuế của pricing
        Set<Long> pricingIds = new HashSet<>();
        Set<Long> addonIds = new HashSet<>();
        Set<Long> couponPricingIds = new HashSet<>();
        Set<Long> couponAddonIds = new HashSet<>();

        Map<Long, List<IGetCouponMcDTO>> mapCouponPricingMc = new HashMap<>();
        Map<Long, List<IGetCouponMcDTO>> mapCouponAddonMc = new HashMap<>();
        lstItem.forEach(item -> {
            pricingIds.add(item.getId());
            if (Objects.nonNull(item.getCouponIds())) {
                couponPricingIds.addAll(item.getCouponIds());
            }
            if (Objects.nonNull(item.getCouponMcIds())) {
                mapCouponPricingMc.put(item.getId(), couponService.getCouponPricingAddonMcInPackage(item.getCouponMcIds()));
            }
            item.getAddons().forEach(addon -> {
                addonIds.add(addon.getId());
                if (Objects.nonNull(addon.getCouponIds())) {
                    couponAddonIds.addAll(addon.getCouponIds());
                }
                if (Objects.nonNull(addon.getCouponMcIds())) {
                    mapCouponAddonMc.put(addon.getId(), couponService.getCouponPricingAddonMcInPackage(addon.getCouponMcIds()));
                }
            });
        });

        // tạo map
        MappingCalculate mappingcalculate = getMappingCalculate(pricingIds, addonIds,
                couponPricingIds, couponAddonIds);

        mappingcalculate.setCouponPricingMcMap(mapCouponPricingMc);
        mappingcalculate.setCouponAddonMcMap(mapCouponAddonMc);

        lstItem.forEach(product -> {
            BigDecimal totalAmountAddon = product.getAddons().stream().map(ProductAddonDTO::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productPrice = ObjectUtil.getOrDefault(product.getTotalAmount(), BigDecimal.ZERO);
            product.setTotalAmount(productPrice.add(totalAmountAddon));
            // thêm thông tin gói
            calculatePricing(product, mappingcalculate);

            product.getAddons().forEach(addon -> calculateAddon(addon, mappingcalculate));
        });
    }

    /**
     * Tính toán thông tin addon
     */
    private void calculateAddon(ProductAddonDTO addon, MappingCalculate mappingcalculate) {
        // khuyến mại
        addon.setCouponList(mappingcalculate.getCouponAddonMap().get(addon.getId()));
        addon.setCouponListMc(mappingcalculate.getCouponAddonMcMap().get(addon.getId()));

        // tính tax
        BigDecimal priceAddonPreTax = addon.getOriginPrice();
        List<PricingTaxRes> addonTaxes = mappingcalculate.getAddonTaxMap().get(addon.getId());
        List<PricingTaxRes> addonSetupFeeTaxes = mappingcalculate.getAddonSetupFeeTaxMap().get(addon.getId());

        TaxFeeDTO taxFeeAddon = getTaxFee(addonTaxes, priceAddonPreTax, addonSetupFeeTaxes);

        addon.setTaxFeeDetail(taxFeeAddon);
    }

    /**
     * Tính toán thông tin gói
     */
    private void calculatePricing(ProductItemDTO product, MappingCalculate mappingcalculate) {
        product.setCouponList(mappingcalculate.getCouponPricingMap().get(product.getId()));

        List<McCouponDTO> mcCouponDTOS = groupMcActivity(product, mappingcalculate);

        product.setCouponListMc(mcCouponDTOS);

        // mặc định priceUpdate là giá trước thuế
        BigDecimal pricePreTax = product.getPriceUpdate();

        // giá trước thuế
        List<PricingTaxRes> pricingTaxes = mappingcalculate.getPricingTaxMap().get(product.getId());
        List<PricingTaxRes> pricingSetupFeeTaxes = mappingcalculate.getPricingSetupFeeTaxMap().get(product.getId());

        TaxFeeDTO taxFee = getTaxFee(pricingTaxes, pricePreTax, pricingSetupFeeTaxes);

        product.setTaxFeeDetail(taxFee);
    }

    /**
     * Nhóm các activity theo mc
     * Activity chỉ cần hển thi tên + số tiền áp dụng
     */
    private List<McCouponDTO> groupMcActivity(ProductItemDTO product, MappingCalculate mappingcalculate) {
        if (mappingcalculate.getCouponPricingMcMap().isEmpty()) {
            return new ArrayList<>();
        }
        return mappingcalculate.getCouponPricingMcMap().get(product.getId()).stream()
                .collect(Collectors.groupingBy(IGetCouponMcDTO::getObjectId))
                .entrySet().stream()
                .map(entry -> {
                    Long objectId = entry.getKey();
                    List<IGetCouponMcDTO> group = entry.getValue();
                    String name = group.get(0).getName();
                    BigDecimal totalDiscount = group.stream()
                            .map(dto -> dto.getDiscountType() == 0
                                    ? dto.getDiscountValue().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).multiply(product.getPriceUpdate())
                                    : dto.getDiscountValue())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return new McCouponDTO(objectId, name, totalDiscount);
                })
                .collect(Collectors.toList());
    }

    /**
     * Lấy thông tin thuế phí
     */
    private TaxFeeDTO getTaxFee(List<PricingTaxRes> pricingTaxes, BigDecimal pricePreTax, List<PricingTaxRes> pricingSetupFeeTaxes) {
        BigDecimal totalTax = BigDecimal.ZERO;
        if (Objects.isNull(pricePreTax)) {
            pricePreTax = BigDecimal.ZERO;
        }

        List<TaxDetailDTO> taxList = new ArrayList<>();
        if (Objects.nonNull(pricingTaxes)) {
            for (PricingTaxRes pricingTax : pricingTaxes) {
                TaxDetailDTO taxDetailDTO = new TaxDetailDTO();
                taxDetailDTO.setTaxName(pricingTax.getTaxName());
                taxDetailDTO.setTaxPercentage(pricingTax.getPercent());
                BigDecimal taxAmount = pricePreTax.multiply(BigDecimal.valueOf(pricingTax.getPercent()));
                taxDetailDTO.setTaxAmount(taxAmount);
                totalTax = totalTax.add(taxAmount);
                taxList.add(taxDetailDTO);
            }
        }

        // phí thiết lập
        BigDecimal totalFee = BigDecimal.ZERO;
        List<FeeDetail> feeList = new ArrayList<>();
        if (pricingSetupFeeTaxes != null) {
            for (PricingTaxRes setupFeeTax : pricingSetupFeeTaxes) {
                BigDecimal setupFee = setupFeeTax.getPrice();

                FeeDetail feeDetail = new FeeDetail();
                feeDetail.setName(setupFeeTax.getFeeName());
                feeDetail.setFeeAmount(setupFee);
                // thêm thuế của phí
                if (Objects.nonNull(setupFeeTax.getTaxId())) {
                    TaxDetailDTO taxDetailDTO = new TaxDetailDTO();
                    taxDetailDTO.setTaxName(setupFeeTax.getTaxName());
                    taxDetailDTO.setTaxPercentage(setupFeeTax.getPercent());
                    if (Objects.equals(setupFeeTax.getHasTax(), 0)) {
                        BigDecimal taxAmount = getTaxAmount(setupFee, setupFeeTax.getPercent());
                        setupFee = setupFee.add(taxAmount);
                        taxDetailDTO.setTaxAmount(taxAmount);
                        taxList.add(taxDetailDTO);
                    } else {
                        feeDetail.setFeeAmount(setupFee.divide(BigDecimal.ONE.add(BigDecimal.valueOf(setupFeeTax.getPercent())), RoundingMode.HALF_UP));
                    }
                }
                feeList.add(feeDetail);

                totalFee = totalFee.add(setupFee);
            }
        }

        return new TaxFeeDTO(totalTax, totalFee, pricePreTax, taxList, feeList);
    }

    private MappingCalculate getMappingCalculate(Set<Long> pricingIds, Set<Long> addonIds, Set<Long> couponPricingIds,
                                                 Set<Long> couponAddonIds) {
        // lấy thông tin tax
        List<PricingTaxRes> pricingTaxList = pricingTaxRepository.getPricingTaxByIds(pricingIds);
        List<PricingTaxRes> addonTaxList = addonsTaxRepository.getAddonTaxByIds(addonIds);

        // tạo map pricing và addon tax
        Map<Long, List<PricingTaxRes>> pricingTaxMap = pricingTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));
        Map<Long, List<PricingTaxRes>> addonTaxMap = addonTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));

        // lấy phí thiết lập gói và addon
        List<PricingTaxRes> pricingSetupFeeTaxList = pricingSetupFeeTaxRepository.getListPricingSetupFeeTax(pricingIds);
        List<PricingTaxRes> addonSetupFeeTaxList = pricingSetupFeeTaxRepository.getListAddonSetupFeeTax(addonIds);

        // tạo map phí thiết lập
        Map<Long, List<PricingTaxRes>> pricingSetupFeeTaxMap = pricingSetupFeeTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));
        Map<Long, List<PricingTaxRes>> addonSetupFeeTaxMap = addonSetupFeeTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getObjectId));

        // lấy danh sách CTKM
        List<IGetCouponPackageDTO> couponPricingList = couponService.getCouponPricingPackages(couponPricingIds);
        List<IGetCouponPackageDTO> couponAddonList = couponService.getCouponAddonPackages(couponAddonIds);

        // tạo ra map CTKM
        Map<Long, List<IGetCouponPackageDTO>> couponPricingMap = couponPricingList.stream().collect(Collectors.groupingBy(IGetCouponPackageDTO::getObjectId));
        Map<Long, List<IGetCouponPackageDTO>> listAddonPricingMap = couponAddonList.stream().collect(Collectors.groupingBy(IGetCouponPackageDTO::getObjectId));

        return new MappingCalculate(pricingTaxMap, addonTaxMap, pricingSetupFeeTaxMap, addonSetupFeeTaxMap, couponPricingMap,
                listAddonPricingMap);
    }

    @Override
    public PackageDetailSmeResDTO getPackageDetailSme(Long packageId) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();

        // Thông tin gói
        PackageDetailSmeResDTO result = new PackageDetailSmeResDTO();
        Package bundling = packageRepository.findById(packageId).orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PACKAGES,
                ErrorKey.ID, String.valueOf(packageId)));
        BeanUtils.copyProperties(bundling, result);

        // TODO Khuyến mại

        // Danh sách sản phẩm
        List<ProductItemDTO> listProduct = packageRepository.getListSmePackageProductByPackageId(packageId).stream()
                .map(item -> {
                    ProductItemDTO resItem = new ProductItemDTO();
                    BeanUtils.copyProperties(item, resItem);
                    resItem.setId(item.getPricingId());
                    resItem.setAddons(ObjectMapperUtil.listMapper(item.getLstAddonJson(), ProductAddonDTO.class));
                    return resItem;
                }).collect(Collectors.toList());

        // Thuế phí: theo thành phần của bundling
        getPackageItemPriceAndCoupon(listProduct);

        // Dịch vụ
        result.setLstService(listProduct.stream()
                .filter(p -> !Objects.equals(MigrationServiceTypeEnum.SIM_KEM_GOI.getValue(), p.getCategoriesIdMigration()))
                .collect(Collectors.toList()));

        // Sim số
        result.setLstSim(listProduct.stream()
                .filter(p -> Objects.equals(MigrationServiceTypeEnum.SIM_KEM_GOI.getValue(), p.getCategoriesIdMigration()))
                .collect(Collectors.toList()));

        // Thiết bị
        result.setLstDevice(listProduct.stream()
                .filter(p -> Objects.nonNull(p.getVariantId()))
                .collect(Collectors.toList()));

        // Tính năng
        IGetFeaturePackageDTO feature = packageRepository.getPackageFeatureByPackageId(packageId);
        if (Objects.nonNull(feature)) {
            result.setLstFeature(ObjectMapperUtil.listMapper(feature.getFeatureJson(), FeatureDetailDTO.class));
        }


        // SPDV gợi ý
        result.setSuggestionMode(bundling.getSuggestionMode());
        result.setSuggestedProducts(suggestionMappingService.getSuggestionsByIdForDev(ObjectTypeEnum.PACKAGE, packageId));

        // Đánh giá: TODO

        // Lưu lại lịch sử xem gói của người dùng
        // Check gói có thuộc giải pháp nào không để thực hiện ghi lich sử xem
        boolean isSinglePackage = solutionPackageRepository.existsSolutionPackageByPackageIdAndSolutionIdIsNull(packageId);
        if (Objects.nonNull(userLogin) && isSinglePackage) {
            serviceViewService.create(packageId, userLogin.getId(), ServiceViewTypeEnum.PACKAGE.getValue());
        }
        return result;
    }

    @Override
    @Transactional
    public Long createPackage(PackageBundlingCreateDTO request) {
        // Check trùng tên gói
        validatePackageName(request.getName(), -1L);

        // Validate package plan

        // validate Tax

        Long currentUserId = AuthUtil.getCurrentUserId();
        Long parentId = AuthUtil.getCurrentParentId();
        boolean isDev = AuthUtil.getPortalOfUserRoles().equals(PortalType.DEV);
        PackageDraft packageDraft = new PackageDraft();
        BeanUtils.copyProperties(request, packageDraft);

        if (isDev) {
            packageDraft.setProviderId(parentId);
        } else {
            packageDraft.setProviderId(request.getProviderId());
        }
        packageDraft.setCreatedBy(currentUserId);
        packageDraft.setModifiedBy(currentUserId);
        packageDraft.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        packageDraft.setState(ApproveStatusEnum.UNAPPROVED.value);

        // khuyến mại

        // tính tiền
        calculatePackage(request, packageDraft);

        PackageDraft save = packageDraftRepository.save(packageDraft);

        createOrUpdatePackageBundling(request, save, isDev);

        //Lưu log khi tạo gói
        if (isDev) {
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.PACKAGE, save.getName(), null, save.getId(), null,
                    ApproveStatusEnum.UNAPPROVED.value);
        }

        return save.getId();
    }

    /**
     * Cập nhật thông tin chi tiết gói bundling
     */
    private void createOrUpdatePackageBundling(PackageBundlingCreateDTO request, PackageDraft packageDraft, boolean isDev) {
        // seo
        Seo seo = saveSeo(request);
        packageDraft.setSeoId(seo.getId());

        Long draftId = packageDraft.getId();

        // thành phần
        if (request.getLstPackageItem() != null) {
            packageMappingService.createPackageMapping(packageDraft.getId(), request.getLstPackageItem());
        }

        // giải pháp
        if (Objects.nonNull(request.getSolutionIds())) {
            solutionPackageService.createSolutionPackages(draftId, SOLUTION_PACKAGE.PACKAGE, request.getSolutionIds(), null);
        }

        // tính năng
        featureService.saveFeatureMapping(draftId, request.getFeatures(), ObjectTypeEnum.PACKAGE);

        // topic
        if (request.getTopics() != null) {
            topicFaqService.updateTopicProductSolution(draftId, ObjectTypeEnum.PACKAGE.value, request.getTopics());
        }

        // sp liên quan
        if (Objects.equals(request.getSuggestionMode(), SuggestionModeEnum.MANUAL) && Objects.nonNull(request.getSuggestionDTOS())) {
            suggestionMappingService.createMapping(draftId, ObjectTypeEnum.PACKAGE, request.getSuggestionDTOS());
        }

        if (!isDev) {
            Set<Long> ids = new HashSet<>(Collections.singletonList(draftId));
            approvePackages(new ApprovedReqDTO(ids, ApproveStatusEnum.APPROVED, null));
        }
    }

    private void calculatePackage(PackageBundlingCreateDTO request, PackageDraft packageDraft) {
        // lấy thông tin thuế của pricing
        Set<Long> pricingIds = new HashSet<>();
        Set<Long> addonIds = new HashSet<>();
        Set<Long> couponPricingIds = new HashSet<>();
        Set<Long> couponAddonIds = new HashSet<>();

        Map<Long, List<IGetCouponMcDTO>> mapCouponPricingMc = new HashMap<>();
        Map<Long, List<IGetCouponMcDTO>> mapCouponAddonMc = new HashMap<>();
        request.getLstPackageItem().forEach(item -> {
            pricingIds.add(item.getPricingId());
            if (Objects.nonNull(item.getCouponIds())) {
                couponPricingIds.addAll(item.getCouponIds());
            }
            if (Objects.nonNull(item.getMcCouponList())) {
                Set<String> mcIds = item.getMcCouponList().stream().map(mc -> mc.getMcId().toString() + mc.getActivityIdx()).collect(Collectors.toSet());
                mapCouponPricingMc.put(item.getPricingId(), couponService.getCouponPricingAddonMcInPackage(mcIds));
            }
            item.getPackageAddon().forEach(addon -> {
                addonIds.add(addon.getAddonId());
                if (Objects.nonNull(addon.getCouponIds())) {
                    couponAddonIds.addAll(addon.getCouponIds());
                }
                if (Objects.nonNull(addon.getMcCouponList())) {
                    Set<String> mcIds = addon.getMcCouponList().stream().map(mc -> mc.getMcId().toString() + mc.getActivityIdx()).collect(Collectors.toSet());
                    mapCouponAddonMc.put(addon.getAddonId(), couponService.getCouponPricingAddonMcInPackage(mcIds));
                }
            });
        });

        // tạo map
        MappingCalculate mappingcalculate = getMappingCalculate(pricingIds, addonIds,
                couponPricingIds, couponAddonIds);

        mappingcalculate.setCouponPricingMcMap(mapCouponPricingMc);
        mappingcalculate.setCouponAddonMcMap(mapCouponAddonMc);

        BigDecimal price = request.getLstPackageItem().stream().map(item -> {
            // tính tiền trước thuế,sau km gói chính
            BigDecimal pricingPrice = calculatePricingPrice(item,
                    mappingcalculate.getCouponPricingMap(),
                    mappingcalculate.getCouponPricingMcMap(),
                    mappingcalculate.getPricingTaxMap(),
                    mappingcalculate.getPricingSetupFeeTaxMap());

            // tính tiền trước thuế,sau km addon
            BigDecimal addonTotalPrice = calculateAddonPrices(item,
                    mappingcalculate.getCouponAddonMap(),
                    mappingcalculate.getCouponAddonMcMap(),
                    mappingcalculate.getAddonTaxMap(),
                    mappingcalculate.getAddonSetupFeeTaxMap());

            return pricingPrice.add(addonTotalPrice);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (request.getDiscountType() != null) {
            if (request.getDiscountType().equals(DiscountTypeEnum.PRICE)) {
                price = price.subtract(request.getDiscountValue());
            } else {
                BigDecimal discount = price.multiply(request.getDiscountValue().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                price = price.subtract(discount);
            }
        }
        packageDraft.setPriceFrom(price);

        // tính tiền sau thuế/phí/km
        BigDecimal priceFinal = request.getLstPackageItem().stream().map(item -> {
            // tính tiền gói
            BigDecimal pricePricing = item.getTotalAmount();
            // tính tiền addon
            BigDecimal priceAddon = item.getPackageAddon().stream().map(PackageAddonCreateDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            return pricePricing.add(priceAddon);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        packageDraft.setPrice(priceFinal);

    }

    /**
     * Tính tiền khi trừ ctkm
     */
    private BigDecimal calculatePriceAfterCoupons(BigDecimal originalPrice, List<IGetCouponPackageDTO> couponList) {
        if (Objects.isNull(couponList) || couponList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return couponList.stream()
                .map(coupon -> calculateCouponDiscount(originalPrice, coupon))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Tính tiền khuyến mại áp dụng
     */
    private BigDecimal calculateCouponDiscount(BigDecimal price, IGetCouponPackageDTO coupon) {
        if (Objects.equals(DiscountTypeEnum.PERCENT.value, coupon.getDiscountType())) {
            return calculatePercentageDiscount(price, coupon);
        }
        return coupon.getDiscountValue();
    }

    /**
     * Tính tiền khuyến mại dạng %
     */
    private BigDecimal calculatePercentageDiscount(BigDecimal price, IGetCouponPackageDTO coupon) {
        BigDecimal percent = coupon.getDiscountValue()
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        BigDecimal discount = price.multiply(percent);

        // Kiểm tra nếu giảm giá vượt quá mức tối đa cho phép
        if (coupon.getDiscountAmount() != null && discount.compareTo(coupon.getDiscountAmount()) > 0) {
            return coupon.getDiscountAmount();
        }

        return discount;
    }

    /**
     * Tính tiền khi trừ CDQC
     */
    private BigDecimal calculatePriceAfterCouponsMc(BigDecimal originalPrice, List<IGetCouponMcDTO> couponMcList) {
        if (Objects.isNull(couponMcList) || couponMcList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return couponMcList.stream()
                .map(coupon -> calculateCouponMcDiscount(originalPrice, coupon))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Tính tiền khuyến mại mc áp dụng
     */
    private BigDecimal calculateCouponMcDiscount(BigDecimal price, IGetCouponMcDTO couponMc) {
        if (Objects.equals(DiscountTypeEnum.PERCENT.value, couponMc.getDiscountType())) {
            return calculatePercentageDiscountMc(price, couponMc);
        }
        return couponMc.getDiscountValue();
    }

    /**
     * Tính tiền khuyến mại mc %
     */
    private BigDecimal calculatePercentageDiscountMc(BigDecimal price, IGetCouponMcDTO coupon) {
        BigDecimal percent = coupon.getDiscountValue()
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        BigDecimal discount = price.multiply(percent);

        // Kiểm tra nếu giảm giá vượt quá mức tối đa cho phép
        if (coupon.getMaxAmount() != null && discount.compareTo(coupon.getMaxAmount()) > 0) {
            return coupon.getMaxAmount();
        }

        return discount;
    }

    /**
     * Tính tiền gói sau thuế phí
     */
    private BigDecimal calculatePricingPrice(PackagePlanCreateDTO item,
                                             Map<Long, List<IGetCouponPackageDTO>> couponPricingMap,
                                             Map<Long, List<IGetCouponMcDTO>> couponPricingMcMap,
                                             Map<Long, List<PricingTaxRes>> pricingTaxMap, Map<Long,
                    List<PricingTaxRes>> pricingSetupFeeTaxMap) {

        BigDecimal price = Objects.nonNull(item.getPriceUpdate()) ? item.getPriceUpdate() : item.getPricePreTax();

        // Tính tổng tiền dựa trên số lượng
        if (item.getQuantity() != null && item.getQuantity() > 0) {
            price = price.multiply(BigDecimal.valueOf(item.getQuantity()));
        }

        // tính tiền km áp dụng
        List<IGetCouponPackageDTO> couponList = couponPricingMap.get(item.getPricingId());
        price = price.subtract(calculatePriceAfterCoupons(price, couponList));

        // tính tiền km áp dụng
        List<IGetCouponMcDTO> couponMcList = couponPricingMcMap.get(item.getPricingId());
        price = price.subtract(calculatePriceAfterCouponsMc(price, couponMcList));

        BigDecimal pricePreTax = price;

        List<PricingTaxRes> taxList = pricingTaxMap.get(item.getPricingId());
        if (Objects.nonNull(taxList)) {
            // Calculate tax
            double totalTaxPercent = calculateTotalTax(taxList);

            price = price.add(getTaxAmount(price, totalTaxPercent));

            // Add setup fee
            price = addSetupFee(price, item.getPricingId(), pricingSetupFeeTaxMap);

        }

        item.setTotalAmount(price);
        item.setTotal(price); // Thêm tổng tiền của thành phần

        return pricePreTax;
    }

    /**
     * Tính tiền addon sau thuế
     */

    private BigDecimal calculateAddonPrices(PackagePlanCreateDTO item,
                                            Map<Long, List<IGetCouponPackageDTO>> couponAddonMap,
                                            Map<Long, List<IGetCouponMcDTO>> couponAddonMcMap,
                                            Map<Long, List<PricingTaxRes>> addonTaxMap,
                                            Map<Long, List<PricingTaxRes>> addonSetupFeeTaxMap) {
        return item.getPackageAddon().stream()
                .map(addon -> calculateSingleAddonPrice(addon, couponAddonMap, couponAddonMcMap, addonTaxMap, addonSetupFeeTaxMap)).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateSingleAddonPrice(PackageAddonCreateDTO addon,
                                                 Map<Long, List<IGetCouponPackageDTO>> couponAddonMap,
                                                 Map<Long, List<IGetCouponMcDTO>> couponAddonMcMap,
                                                 Map<Long, List<PricingTaxRes>> addonTaxMap,
                                                 Map<Long, List<PricingTaxRes>> addonSetupFeeTaxMap) {
        BigDecimal price = Objects.nonNull(addon.getPriceUpdate()) ? addon.getPriceUpdate() : addon.getPricePreTax();

        // Tính tổng tiền dựa trên số lượng
        if (addon.getQuantity() != null && addon.getQuantity() > 0) {
            price = price.multiply(BigDecimal.valueOf(addon.getQuantity()));
        }

        // tính tiền km áp dụng
        List<IGetCouponPackageDTO> couponList = couponAddonMap.get(addon.getAddonId());
        price = price.subtract(calculatePriceAfterCoupons(price, couponList));

        List<IGetCouponMcDTO> couponMcList = couponAddonMcMap.get(addon.getAddonId());
        price = price.subtract(calculatePriceAfterCouponsMc(price, couponMcList));

        BigDecimal pricePreTax = price;

        // Add tax
        List<PricingTaxRes> taxList = addonTaxMap.get(addon.getAddonId());
        if (Objects.nonNull(taxList)) {
            double totalTaxPercent = taxList.stream()
                    .map(PricingTaxRes::getPercent)
                    .reduce(0.0, Double::sum);
            price = price.add(getTaxAmount(price, totalTaxPercent));
        }

        // Add thành tiền
        BigDecimal totalAmount = addSetupFee(price, addon.getAddonId(), addonSetupFeeTaxMap);
        addon.setTotalAmount(totalAmount);
        addon.setTotal(totalAmount); // Thêm tổng tiền của addon

        return pricePreTax;
    }

    /**
     * Cộng tiền thuế
     */
    private BigDecimal getTaxAmount(BigDecimal price, Double taxPercent) {
        return price.multiply(BigDecimal.valueOf(taxPercent)
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
    }

    /**
     * tính tiền phí thiết lập
     */
    private BigDecimal addSetupFee(BigDecimal price, Long id,
                                   Map<Long, List<PricingTaxRes>> setupFeeTaxMap) {
        List<PricingTaxRes> setupFeeTaxList = setupFeeTaxMap.get(id);
        if (Objects.nonNull(setupFeeTaxList)) {
            for (PricingTaxRes setupFeeTax : setupFeeTaxList) {
                BigDecimal setupFee = setupFeeTax.getPrice();
                if (Objects.equals(setupFeeTax.getHasTax(), 0)) {
                    setupFee = setupFee.add(getTaxAmount(setupFee, setupFeeTax.getPercent()));
                }
                price = price.add(setupFee);
            }
        }
        return price;
    }

    /**
     * Lưu seo
     */
    private Seo saveSeo(PackageBundlingCreateDTO packageBundlingCreateDTO) {
        return Objects.nonNull(packageBundlingCreateDTO.getSeo()) ?
                seoService.saveSeoSolution(CAU_HINH_GOI_BUNDLING, packageBundlingCreateDTO.getSeo()) :
                seoService.saveSeoDefault(StringUtil.replace(packageBundlingCreateDTO.getName()),
                        packageBundlingCreateDTO.getName() + SeoTypeCodeConstant.TITLE_PAGE_PRICING, packageBundlingCreateDTO.getDescriptions(), null,
                        null, CAU_HINH_GOI_BUNDLING, true);
    }

    /**
     * Validate package name
     */
    private void validatePackageName(String packageName, Long id) {
        if (packageDraftRepository.existsByNameAndDeletedFlagAndIdNot(packageName, DeletedFlag.NOT_YET_DELETED.getValue(), id)) {
            throw exceptionFactory.badRequest(ProductSolutions.PACKAGE_NAME_EXIST, ErrorKey.PRODUCT_SOLUTION, ProductSolutionError.PACKAGE_NAME);
        }
    }

    @Override
    @Transactional
    public Long updatePackage(Long draftId, PackageBundlingCreateDTO request) {
        // Check trùng tên gói
        validatePackageName(request.getName(), draftId);

        // Validate package plan

        // validate Tax

        PackageDraft packageDraft = packageDraftRepository.findById(draftId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PACKAGE, ErrorKey.DRAFT_ID, String.valueOf(draftId)));
        // Chỉ được cập nhật các package do bản thân tạo
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        if (!Objects.equals(packageDraft.getProviderId(), currentParentId) && // Không thuộc cùng doanh nghiệp dev
                !Objects.equals(packageDraft.getCreatedBy(), currentUserId)) { // Không phải người tạo trực tiếp
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
        }
        BeanUtils.copyProperties(request, packageDraft);
        packageDraft.setModifiedBy(currentUserId);
        packageDraft.setState(ApproveStatusEnum.UNAPPROVED.value);

        // khuyến mại
        boolean isDev = AuthUtil.isDev();

        // tính tiền
        calculatePackage(request, packageDraft);

        PackageDraft save = packageDraftRepository.save(packageDraft);

        createOrUpdatePackageBundling(request, save, isDev);

        //Lưu log khi cập nhật gói
        if (isDev) {
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.PACKAGE,
                    save.getName(), null, save.getId(), null, ApproveStatusEnum.UNAPPROVED.value);
        }

        return save.getId();
    }

    @Override
    public void deletePackages(List<Long> draftIds) {
        // Không xóa các package đã được đăng ký thuê bao
        if (subscriptionMetadataRepository.existsByPackageDraftIdIn(draftIds)) {
            throw exceptionFactory.badRequest(ProductSolutions.PACKAGE_EXISTS_FOR_SUBSCRIPTION, Resources.PACKAGE,
                    ErrorKey.DRAFT_ID, draftIds.stream().map(String::valueOf).collect(Collectors.joining()));
        }
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();
        Date now = new Date();
        draftIds.forEach(draftId -> {
            var packageDraft = packageDraftRepository.findById(draftId)
                    .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PACKAGE, ErrorKey.ID, String.valueOf(draftId)));
            // Chỉ được xóa các packager do bản thân tạo
            if (!Objects.equals(packageDraft.getProviderId(), currentParentId) && // Không thuộc cùng doanh nghiệp dev
                    !Objects.equals(packageDraft.getCreatedBy(), currentUserId)) { // Không phải người tạo trực tiếp
                throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
            }
            // Xóa package draft
            packageDraft.setDeletedFlag(DeletedFlag.DELETED.getValue());
            packageDraft.setModifiedBy(currentUserId);
            packageDraft.setModifiedAt(now);
            packageDraftRepository.save(packageDraft);
            // Xóa các package
            packageRepository.findByDraftId(draftId).forEach(packageBundling -> {
                packageBundling.setDeletedFlag(DeletedFlag.DELETED.getValue());
                packageBundling.setModifiedBy(currentUserId);
                packageBundling.setModifiedAt(now);
                packageRepository.save(packageBundling);
            });
        });
    }

    @Override
    public void approvePackages(ApprovedReqDTO reqDTO) {
        List<PackageDraft> packageDrafts = packageDraftRepository.findAllById(reqDTO.getIds());
        if (Objects.equals(ApproveStatusEnum.APPROVED, reqDTO.getApprovedStatus())) {
            packageDrafts.forEach(packageDraft -> {
                packageDraft.setState(ApproveStatusEnum.APPROVED.value);
                packageDraftRepository.save(packageDraft);

                Long draftId = packageDraft.getId();
                // tạo phiên bản chính
                Package packageBundling = new Package();
                BeanUtils.copyProperties(packageDraft, packageBundling);
                packageBundling.setId(null);
                packageBundling.setDraftId(draftId);
                Package save = packageRepository.save(packageBundling);

                Long id = save.getId();
                // Giải pháp
                solutionPackageService.approvedMappingSolutionPackages(draftId, SOLUTION_PACKAGE.PACKAGE, id);

                // Thành phần
                packageMappingService.approvePackageMapping(draftId, id);

                // Tính năng
                featureMappingService.approvedFeatureMapping(draftId, id, ObjectTypeEnum.PACKAGE);

                // Topic
                topicFaqService.approvedTopicProductSolution(draftId, id, ObjectTypeEnum.PACKAGE.value);

                // khuyến mại

                // Sp liên quan
                suggestionMappingService.approveMapping(draftId, ObjectTypeEnum.PACKAGE, id);

                //Lưu log khi phê duyệt gói
                if (reqDTO.isCreate()) {
                    int maxVersion = productApprovalHistoryService.findMaxVersionByDraftId(draftId, ObjectTypeEnum.PACKAGE.name());
                    productApprovalHistoryService.addLog(maxVersion + 1, ObjectTypeEnum.PACKAGE, packageDraft.getName(),
                            save.getId(), packageDraft.getId(), reqDTO.getComment(), ApproveStatusEnum.APPROVED.value);
                }
            });
        } else {
            packageDrafts.forEach(packageDraft -> {
                if (StringUtils.isEmpty(reqDTO.getComment())) {
                    throw exceptionFactory.badRequest(ProductSolutions.PACKAGE_REJECT_COMMENT_MUST_NOT_EMPTY, ErrorKey.PRODUCT_SOLUTION,
                            ProductSolutionError.PACKAGE, ErrorKey.APPROVE_COMMENT);
                }
                packageDraft.setState(reqDTO.getApprovedStatus().value);
                packageDraft.setApproveReason(reqDTO.getComment());
                packageDraftRepository.save(packageDraft);
                //Lưu log khi từ chối
                productApprovalHistoryService.addLog(null, ObjectTypeEnum.PACKAGE, packageDraft.getName(),
                        null, packageDraft.getId(), reqDTO.getComment(), ApproveStatusEnum.REJECTED.value);
            });

        }

    }

    @Override
    public void submitPackage(List<Long> draftIds) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentId = AuthUtil.getCurrentParentId();

        // admin gui ycpd sẽ duyệt luôn
        if (!AuthUtil.isDev()) {
            approvePackages(new ApprovedReqDTO(new HashSet<>(draftIds), ApproveStatusEnum.APPROVED, null));
            return;
        }
        // Chỉ được yêu cầu phê duyệt cho package của cùng nhà cung cấp
        if (packageDraftRepository.existsByProviderIdNotAndIdIn(currentParentId, draftIds)) {
            throw exceptionFactory.badRequest(ProductSolutions.PACKAGE_NOT_ALLOWED, Resources.PACKAGE, ErrorKey.DRAFT_ID,
                    draftIds.stream().map(String::valueOf).collect(Collectors.joining()));
        }
        // Cập nhật trạng thái phê duyệt
        packageDraftRepository.updateStateByIds(draftIds, ApproveStatusEnum.AWAITING_APPROVAL.value, currentUserId);
        // TODO: Gửi thông báo yêu cầu phê duyệt
        //Lưu log khi yêu cầu phê duyệt gói
        List<PackageDraft> packageDrafts = packageDraftRepository.findAllById(draftIds);
        packageDrafts.forEach(packageDraft -> {
            productApprovalHistoryService.addLog(null, ObjectTypeEnum.PACKAGE, packageDraft.getName(),
                    null, packageDraft.getId(), null, ApproveStatusEnum.AWAITING_APPROVAL.value);
        });
    }

    @Override
    public Page<PackageListResDTO> getListSolutionPackage(Long solutionId, Pageable pageable) {
        Page<IGetPackItemDTO> listPackage = productSolutionRepository.getPagePackageBySolutionId(solutionId, pageable);
        return listPackage.map(this::convertPagePackages);
    }

    @Override
    public List<SolutionByDomainPageDTO> getListSolutionByDomain(String search, ProductSortEnum sort, CustomerTypeEnum customerType, List<Long> domainIds) {
        // Ds lĩnh vực
        List<SolutionDomain> domains = solutionDomainRepository.getSolutionDomainByIdInListId(domainIds);
        Map<Long, String> mapDomainName = domains.stream()
                .collect(Collectors.toMap(SolutionDomain::getId, SolutionDomain::getName));

        String domainIdStr = domainIds.stream().map(String::valueOf).collect(Collectors.joining(","));

        List<IProductSolutionSmeDTO> lstSolution;
        switch (sort) {
            case NEWEST:
                lstSolution = productSolutionRepository.getListLatestProductSolutionByDomainNewest(
                        customerType.name(), SqlUtils.optimizeSearchLike(search), domainIdStr, rank);
                break;
            case TRENDING:
                lstSolution = productSolutionRepository.getListLatestProductSolutionByDomainTrending(
                        customerType.name(), SqlUtils.optimizeSearchLike(search), domainIdStr, rank);
                break;
            case TOP_SELLING:
                lstSolution = productSolutionRepository.getListLatestProductSolutionByDomainTopSelling(
                        customerType.name(), SqlUtils.optimizeSearchLike(search), domainIdStr, rank);
                break;
            default:
                throw new IllegalArgumentException("Unsupported sort order: " + sort);
        }

        // Loại bỏ solution không có domainId
        lstSolution.removeIf(item -> Objects.equals(item.getDomainIds(), "") || Objects.equals(item.getDomainIds(), "[]"));
        // Set cac Item vao package
        List<ProductSolutionSmeDTO> lstSolutionView = servicesService.getProductSolutionSmeDTO(lstSolution);
        // Map id với solution
        Map<Long, List<ProductSolutionSmeDTO>> mapSolutionByDomain = lstSolutionView.stream()
                .filter(s -> Objects.nonNull(s.getDomainId()))
                .collect(Collectors.groupingBy(ProductSolutionSmeDTO::getDomainId));

        // Tạo danh sách kết quả
        List<SolutionByDomainPageDTO> result = new ArrayList<>();
        for (SolutionDomain domain : domains) {
            Long domainId = domain.getId();
            List<ProductSolutionSmeDTO> solutions = mapSolutionByDomain.getOrDefault(domainId, new ArrayList<>());

            SolutionByDomainPageDTO dto = SolutionByDomainPageDTO.fromList(domainId, domain.getName(), solutions);

            result.add(dto);
        }

        return result;

    }

    @Override
    public SolutionByDomainPageDTO getListSolutionByDomainId(String search, CustomerTypeEnum customerType, Long domainId, ProductSortEnum order, Pageable pageable) {
        SolutionDomain domain = solutionDomainRepository.findById(domainId)
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SOLUTION_DOMAIN, ErrorKey.ID, String.valueOf(domainId)));
        // Ds các solution
        Page<IProductSolutionSmeDTO> lstSolution = productSolutionRepository.getListLatestProductSolutionByDomainId(customerType.name(),
                SqlUtils.optimizeSearchLike(search), domainId.toString(), pageable);
        // Fill package item
        List<ProductSolutionSmeDTO> mappedList = servicesService.getProductSolutionSmeDTO(lstSolution.getContent());

        // Tạo Page mới với dữ liệu đã map
        Page<ProductSolutionSmeDTO> pageSolutionView = new PageImpl<>(mappedList, pageable, lstSolution.getTotalElements());

        return SolutionByDomainPageDTO.fromPage(domainId, domain.getName(), pageSolutionView);
    }

    /**
     * Hàm convert từ IGetPackItemDTO -> PackageListResDTO
     */
    private PackageListResDTO convertPagePackages(IGetPackItemDTO packageItem) {
        PackageListResDTO responseItem = PackageListResDTO.builder()
                .packageId(packageItem.getId())
                .packageDraftId(packageItem.getDraftId())
                .name(packageItem.getPackageName())
                .descriptions(packageItem.getDescriptions())
                .providerName(packageItem.getProviderName())
                .visibility(DisplayStatus.VISIBLE)
                .approvalStatus(ApproveStatusEnum.APPROVED)
                .recommended(packageItem.getRecommended())
                .price(packageItem.getPrice())
                .createdAt(packageItem.getCreatedAt())
                .build();

        // Danh sách tính năng của package bundle
        responseItem.setLstFeature(ObjectMapperUtil.listMapper(packageItem.getLstFeatureJson(), FeatureDetailDTO.class));

        // Danh sách các sản phẩm trong package bundle
        List<ProductItemDTO> listProduct = packageRepository.getListPackageProduct(packageItem.getId()).stream().map(item -> {
            ProductItemDTO resItem = new ProductItemDTO();
            BeanUtils.copyProperties(item, resItem);
            resItem.setAddons(ObjectMapperUtil.listMapper(item.getLstAddonJson(), ProductAddonDTO.class));
            return resItem;
        }).collect(Collectors.toList());
        responseItem.setLstProduct(listProduct);
        // thông tin về số lượng sp loại thiết bị và dịch vụ (nếu có variantId -> sản phẩm thiết bị kèm plan, không -> saas)
        long numOfDeviceItem = listProduct.stream().filter(item -> Objects.nonNull(item.getVariantId())).count();
        responseItem.setNumOfDevice(numOfDeviceItem);
        responseItem.setNumOfService(listProduct.size() - numOfDeviceItem);

        // TODO: Thông tin khuyến mại áp dụng

        return responseItem;
    }

    @Override
    public List<ServiceCommonDetailDTO> getListPackageSuggestion(Long packageId, PortalType portalType,
                                                                 PricingDetailInputEnum pricingDetailInputEnum) {
        var packages = findPackageById(packageId);
        if (Objects.equals(packages.getSuggestionMode(), SuggestionModeEnum.MANUAL)) {
            return suggestionMappingService.getSuggestionsById(ObjectTypeEnum.PACKAGE, packageId);
        } else if (Objects.equals(packages.getSuggestionMode(), SuggestionModeEnum.AUTO)) {
            SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.SERVICE_SUGGESTION);
            BigDecimal[] categoryIdArr = Arrays.stream(packages.getCategoryIds()).map(BigDecimal::valueOf).toArray(BigDecimal[]::new);
            Integer numSuggest = ObjectUtil.getOrDefault(systemParam.getServiceSuggestionsNumber(), 10);
            return suggestionMappingService.getAutoSuggestions(numSuggest, categoryIdArr);
        }
        return null;
    }

    private Package findPackageById(Long packageId) {
        return packageRepository.findById(packageId).orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PACKAGES,
                ErrorKey.ID, String.valueOf(packageId)));
    }


    @Override
    public PriceInfoDTO calculatePriceInfo(CalculatePriceInfoRequestDTO request) {
        PriceInfoDTO priceInfo = new PriceInfoDTO();
        BigDecimal totalPricePreTax = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalPriceAfterDiscount = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalDiscount = BigDecimal.ZERO;
        List<PackagePlanPriceInfoDTO> packagePlanPriceInfos = new ArrayList<>();

        // lấy thông tin thuế của pricing
        Set<Long> pricingIds = new HashSet<>();
        Set<Long> addonIds = new HashSet<>();
        Set<Long> couponPricingIds = new HashSet<>();
        Set<Long> couponAddonIds = new HashSet<>();

        Map<Long, List<IGetCouponMcDTO>> mapCouponPricingMc = new HashMap<>();
        Map<Long, List<IGetCouponMcDTO>> mapCouponAddonMc = new HashMap<>();
        request.getLstPackageItem().forEach(item -> {
            pricingIds.add(item.getPricingId());
            if (Objects.nonNull(item.getCouponIds())) {
                couponPricingIds.addAll(item.getCouponIds());
            }
            if (Objects.nonNull(item.getMcCouponList())) {
                Set<String> mcIds = item.getMcCouponList().stream().map(mc -> mc.getMcId().toString() + mc.getActivityIdx()).collect(Collectors.toSet());
                mapCouponPricingMc.put(item.getPricingId(), couponService.getCouponPricingAddonMcInPackage(mcIds));
            }
            item.getPackageAddon().forEach(addon -> {
                addonIds.add(addon.getAddonId());
                if (Objects.nonNull(addon.getCouponIds())) {
                    couponAddonIds.addAll(addon.getCouponIds());
                }
                if (Objects.nonNull(addon.getMcCouponList())) {
                    Set<String> mcIds = addon.getMcCouponList().stream().map(mc -> mc.getMcId().toString() + mc.getActivityIdx()).collect(Collectors.toSet());
                    mapCouponAddonMc.put(addon.getAddonId(), couponService.getCouponPricingAddonMcInPackage(mcIds));
                }
            });
        });

        // tạo map
        MappingCalculate mappingcalculate = getMappingCalculate(pricingIds, addonIds,
                couponPricingIds, couponAddonIds);

        mappingcalculate.setCouponPricingMcMap(mapCouponPricingMc);
        mappingcalculate.setCouponAddonMcMap(mapCouponAddonMc);

        // Tính toán thông tin giá cho từng gói
        for (PackagePlanCreateDTO item : request.getLstPackageItem()) {
            PackagePlanPriceInfoDTO packagePlanPriceInfo = new PackagePlanPriceInfoDTO();
            packagePlanPriceInfo.setPricingId(item.getPricingId());

            // Lấy tên gói từ repository (nếu cần)
            // packagePlanPriceInfo.setPricingName(pricingRepository.findById(item.getPricingId()).map(Pricing::getName).orElse(""));

            // Lấy giá gốc
            BigDecimal originPrice = Objects.nonNull(item.getPriceUpdate()) ? item.getPriceUpdate() : item.getPricePreTax();
            if (item.getQuantity() != null && item.getQuantity() > 0) {
                originPrice = originPrice.multiply(BigDecimal.valueOf(item.getQuantity()));
            }
            packagePlanPriceInfo.setOriginPrice(originPrice);

            // Tính tiền trước thuế, sau km gói chính
            BigDecimal pricingPrice = calculatePricingPrice(item,
                    mappingcalculate.getCouponPricingMap(),
                    mappingcalculate.getCouponPricingMcMap(),
                    mappingcalculate.getPricingTaxMap(),
                    mappingcalculate.getPricingSetupFeeTaxMap());

            // Tính tiền trước thuế, sau km addon
            BigDecimal addonTotalPrice = calculateAddonPrices(item,
                    mappingcalculate.getCouponAddonMap(),
                    mappingcalculate.getCouponAddonMcMap(),
                    mappingcalculate.getAddonTaxMap(),
                    mappingcalculate.getAddonSetupFeeTaxMap());

            // Tính thuế và phí
            TaxFeeDTO taxFee = getTaxFee(mappingcalculate.getPricingTaxMap().get(item.getPricingId()), pricingPrice, mappingcalculate.getPricingSetupFeeTaxMap().get(item.getPricingId()));

            // Cập nhật thông tin giá cho gói
            packagePlanPriceInfo.setPricePreTax(taxFee.getPricePreTax());
            packagePlanPriceInfo.setTotalTax(taxFee.getTotalTax());
            packagePlanPriceInfo.setTotalFee(taxFee.getTotalFee());
            packagePlanPriceInfo.setPriceAfterDiscount(pricingPrice);

            // Tính tổng tiền của gói (bao gồm cả addon)
            BigDecimal packageTotal = item.getTotal() != null ? item.getTotal() : BigDecimal.ZERO;

            // Tính toán thông tin giá cho từng addon
            List<PackagePlanPriceInfoDTO.AddonPriceInfoDTO> addonPriceInfos = new ArrayList<>();
            BigDecimal totalAddonPrice = BigDecimal.ZERO;

            for (PackageAddonCreateDTO addon : item.getPackageAddon()) {
                PackagePlanPriceInfoDTO.AddonPriceInfoDTO addonPriceInfo = new PackagePlanPriceInfoDTO.AddonPriceInfoDTO();
                addonPriceInfo.setAddonId(addon.getAddonId());

                // Lấy tên addon từ repository (nếu cần)
                // addonPriceInfo.setAddonName(addonRepository.findById(addon.getAddonId()).map(Addon::getName).orElse(""));

                // Lấy giá gốc
                BigDecimal addonOriginPrice = addon.getPriceUpdate();
                if (addon.getQuantity() != null && addon.getQuantity() > 0) {
                    addonOriginPrice = addonOriginPrice.multiply(BigDecimal.valueOf(addon.getQuantity()));
                }
                addonPriceInfo.setOriginPrice(addonOriginPrice);

                // Tính thuế và phí cho addon
                BigDecimal addonPrice = addon.getPriceUpdate();
                if (addon.getQuantity() != null && addon.getQuantity() > 0) {
                    addonPrice = addonPrice.multiply(BigDecimal.valueOf(addon.getQuantity()));
                }

                List<IGetCouponPackageDTO> couponList = mappingcalculate.getCouponAddonMap().get(addon.getAddonId());
                addonPrice = addonPrice.subtract(calculatePriceAfterCoupons(addonPrice, couponList));

                TaxFeeDTO addonTaxFee = getTaxFee(
                        mappingcalculate.getAddonTaxMap().get(addon.getAddonId()),
                        addonPrice,
                        mappingcalculate.getAddonSetupFeeTaxMap().get(addon.getAddonId())
                );

                // Tính tổng tiền của addon
                BigDecimal addonTotal = addon.getTotal() != null ? addon.getTotal() : BigDecimal.ZERO;

                // Cập nhật thông tin giá cho addon
                addonPriceInfo.setPricePreTax(addonTaxFee.getPricePreTax());
                addonPriceInfo.setTotalTax(addonTaxFee.getTotalTax());
                addonPriceInfo.setTotalFee(addonTaxFee.getTotalFee());
                addonPriceInfo.setPriceAfterDiscount(addonPrice);
                addonPriceInfo.setTotal(addonTotal);

                addonPriceInfos.add(addonPriceInfo);

                // Cộng dồn tổng tiền addon
                totalAddonPrice = totalAddonPrice.add(addonTotal);
            }

            // Cập nhật tổng tiền của gói bao gồm cả addon
            packageTotal = packageTotal.add(totalAddonPrice);
            packagePlanPriceInfo.setTotal(packageTotal);
            packagePlanPriceInfo.setAddonPriceInfos(addonPriceInfos);
            packagePlanPriceInfos.add(packagePlanPriceInfo);

            // Cộng dồn vào tổng
            totalPricePreTax = totalPricePreTax.add(taxFee.getPricePreTax());
            totalTax = totalTax.add(taxFee.getTotalTax());
            totalFee = totalFee.add(taxFee.getTotalFee());
            totalPriceAfterDiscount = totalPriceAfterDiscount.add(pricingPrice);
            totalPrice = totalPrice.add(packageTotal);
        }

        // Cập nhật thông tin tổng
        priceInfo.setPricePreTax(totalPricePreTax);
        priceInfo.setTotalTax(totalTax);
        priceInfo.setTotalFee(totalFee);
        priceInfo.setPriceAfterDiscount(totalPriceAfterDiscount);
        priceInfo.setDiscount(totalDiscount);
        priceInfo.setTotal(totalPrice);
        priceInfo.setPackagePlanPriceInfos(packagePlanPriceInfos);

        return priceInfo;
    }

    @Override
    @Transactional
    public void handleComponentChanged(Long componentId, String type, String eventType, Object metadata) {
        try {
            log.info("Bắt đầu xử lý component thay đổi - componentId: {}, type: {}, eventType: {}", componentId, type, eventType);

            // Lấy referenceId từ metadata nếu có
            Long referenceId = null;
            if (metadata instanceof ProductEventMetadata) {
                referenceId = ((ProductEventMetadata) metadata).getReferenceId();
                log.info("Nhận được referenceId: {} từ metadata", referenceId);
            }

            // Tìm tất cả packages có chứa component này
            List<Long> packageDraftIds = findPackagesByComponent(componentId, type, referenceId);
            if (packageDraftIds.isEmpty()) {
                log.info("Không tìm thấy package nào chứa component {} với type {}", componentId, type);
                return;
            }

            // Xử lý từng package
            for (Long draftId : packageDraftIds) {
                try {
                    processPackageForComponentChange(draftId, componentId, type, eventType, metadata);
                    log.info("Đã xử lý thành công package draft ID: {}", draftId);
                } catch (Exception e) {
                    log.error("Lỗi khi xử lý package draft ID {}: {}", draftId, e.getMessage(), e);
                    // Tiếp tục xử lý các package khác
                }
            }
            log.info("Hoàn thành xử lý component thay đổi cho {} packages", packageDraftIds.size());
        } catch (Exception e) {
            log.error("Lỗi khi xử lý component thay đổi - componentId: {}, type: {}, eventType: {}: {}",
                    componentId, type, eventType, e.getMessage(), e);
            throw new RuntimeException("Không thể xử lý component thay đổi: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm packages theo component ID và type, có thể sử dụng referenceId
     */
    private List<Long> findPackagesByComponent(Long componentId, String type, Long referenceId) {
        switch (type.toUpperCase()) {
            case "COUPON":
                return packageDraftRepository.findPackageDraftIdsByCouponId(componentId);
            case "ADDON":
                return findPackagesByAddonId(componentId);
            case "PRICING":
                return findPackagesByPricingId(referenceId);
            case "VARIANT":
                return findPackagesByVariantId(referenceId);
            default:
                log.warn("Loại component không được hỗ trợ: {}", type);
                return Collections.emptyList();
        }
    }

    /**
     * Tìm packages theo addon ID
     */
    private List<Long> findPackagesByAddonId(Long addonId) {
        // TODO: Implement SQL query để tìm packages có chứa addon cụ thể
        // Cần tạo SQL query tương tự như FIND_PACKAGE_DRAFTS_BY_COUPON_ID
        log.warn("Tìm packages theo ADDON ID {} chưa được implement", addonId);
        return Collections.emptyList();
    }

    /**
     * Tìm packages theo pricing ID
     */
    private List<Long> findPackagesByPricingId(Long pricingId) {
        try {
            log.info("Tìm packages theo PRICING ID {}", pricingId);
            return packageDraftRepository.findPackageDraftIdsByPricingId(pricingId);
        } catch (Exception e) {
            log.error("Lỗi khi tìm packages theo PRICING ID {}: {}", pricingId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Tìm packages theo variant ID
     */
    private List<Long> findPackagesByVariantId(Long variantId) {
        try {
            log.info("Tìm packages theo VARIANT ID {}", variantId);
            return packageDraftRepository.findPackageDraftIdsByVariantId(variantId);
        } catch (Exception e) {
            log.error("Lỗi khi tìm packages theo VARIANT ID {}: {}", variantId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Xử lý package theo loại event
     */
    private void processPackageForComponentChange(Long packageDraftId, Long componentId, String type, String eventType, Object metadata) {
        switch (eventType.toUpperCase()) {
            case "EXPIRED":
            case "APPLY_EXCEED":
                // Coupon hết hạn hoặc hết lượt -> xóa coupon khỏi package
                removeCouponFromPackage(packageDraftId, componentId, type, eventType, metadata);
                break;

            case "UPGRADED":
            case "STATUS_CHANGED":
                // Component được cập nhật hoặc thay đổi trạng thái -> tính toán lại giá
                recalculatePackagePrice(packageDraftId, componentId, type, eventType, metadata);
                break;

            default:
                log.warn("Loại event không được hỗ trợ: {}", eventType);
                // Mặc định tính toán lại giá
                recalculatePackagePrice(packageDraftId, componentId, type, eventType, metadata);
                break;
        }
    }

    /**
     * Xóa coupon khỏi package khi coupon hết hạn hoặc hết lượt
     */
    private void removeCouponFromPackage(Long packageDraftId, Long componentId, String type, String eventType, Object metadata) {
        try {
            if ("COUPON".equals(type.toUpperCase())) {
                // Xóa coupon khỏi package items và addon items
                removeCouponFromPackageItems(packageDraftId, componentId);
                removeCouponFromAddonItems(packageDraftId, componentId);
                // Tính toán lại giá sau khi xóa coupon
                recalculatePackagePrice(packageDraftId, componentId, type, eventType, metadata);
            } else {
                log.warn("Chỉ hỗ trợ xóa COUPON, không hỗ trợ xóa {}", type);
                // Với các component khác, chỉ tính toán lại giá
                recalculatePackagePrice(packageDraftId, componentId, type, eventType, metadata);
            }

        } catch (Exception e) {
            log.error("Lỗi khi xóa {} {} khỏi package {}: {}", type, componentId, packageDraftId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xóa coupon khỏi package items
     */
    private void removeCouponFromPackageItems(Long packageDraftId, Long couponId) {
        try {
            packageItemPromotionsRepository.deleteCouponFromPackageItems(packageDraftId, couponId);
        } catch (Exception e) {
            log.error("Lỗi khi xóa coupon {} khỏi package items của package draft {}: {}",
                    couponId, packageDraftId, e.getMessage(), e);
            throw new RuntimeException("Lỗi khi xóa coupon khỏi package items: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa coupon khỏi addon items
     */
    private void removeCouponFromAddonItems(Long packageDraftId, Long couponId) {
        try {
            packageItemAddonPromotionsRepository.deleteCouponFromAddonItems(packageDraftId, couponId);
        } catch (Exception e) {
            log.error("Lỗi khi xóa coupon {} khỏi addon items của package draft {}: {}",
                    couponId, packageDraftId, e.getMessage(), e);
            throw new RuntimeException("Lỗi khi xóa coupon khỏi addon items: " + e.getMessage(), e);
        }
    }

    /**
     * Tính toán lại giá cho package khi component thay đổi
     */
    private void recalculatePackagePrice(Long packageDraftId, Long componentId, String type, String eventType, Object metadata) {
        // Lấy thông tin package draft
        PackageDraft packageDraft = packageDraftRepository.findById(packageDraftId)
                .orElseThrow(() -> new RuntimeException("Không tìm thấy package draft với ID: " + packageDraftId));
        // Lấy thông tin package items
        List<PackageMappings> packageMappings = packageMappingRepository.findAllByPackageDraftId(packageDraftId);
        if (packageMappings.isEmpty()) {
            log.warn("Package draft {} không có items nào", packageDraftId);
            return;
        }
        // Xóa các package mappings cũ
//        deletePackageMappings(packageMappings);

        // Tạo request để tính toán lại giá với thông tin component thay đổi
        PackageBundlingCreateDTO request = buildPackageRequestForRecalculation(packageDraft, packageMappings, componentId, type, eventType);
        // Tính toán lại giá
        calculatePackage(request, packageDraft);
        // Lưu package draft đã được cập nhật
        PackageDraft save = packageDraftRepository.save(packageDraft);
        createOrUpdatePackageBundling(request, save, false);
        log.info("Đã tính toán lại giá cho package draft {} do {} {}, giá mới: {}",
            packageDraftId, eventType, type, packageDraft.getPrice());
    }

    /**
     * Tạo PackageBundlingCreateDTO từ thông tin hiện tại của package để tính toán lại Với trường hợp UPGRADED và STATUS_CHANGED, lấy thông tin
     * mới nhất của component được thay đổi
     */
    private PackageBundlingCreateDTO buildPackageRequestForRecalculation(PackageDraft packageDraft, List<PackageMappings> packageMappings,
                                                                         Long componentId, String type, String eventType) {
        try {
            log.info("Bắt đầu build PackageBundlingCreateDTO cho package draft {}", packageDraft.getId());
            PackageBundlingCreateDTO request = new PackageBundlingCreateDTO();
            // Copy thông tin cơ bản từ package draft
            BeanUtils.copyProperties(packageDraft, request);
            // Lấy danh sách package item IDs
            List<Long> packageItemIds = packageMappings.stream()
                    .map(PackageMappings::getPackageItemId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (packageItemIds.isEmpty()) {
                request.setLstPackageItem(new ArrayList<>());
                return request;
            }
            // Build danh sách package items với thông tin component thay đổi
            List<PackagePlanCreateDTO> lstPackageItem = buildPackageItems(packageItemIds, componentId, type, eventType);
            request.setLstPackageItem(lstPackageItem);
            log.info("Đã build thành công PackageBundlingCreateDTO với {} package items", lstPackageItem.size());
            return request;
        } catch (Exception e) {
            log.error("Lỗi khi build PackageBundlingCreateDTO cho package draft {}: {}",
                    packageDraft.getId(), e.getMessage(), e);
            throw new RuntimeException("Lỗi khi build package request: " + e.getMessage(), e);
        }
    }

    /**
     * Build danh sách package items từ package item IDs theo logic chuẩn của createPackageMapping - multiPlanId (plan_id) là bắt buộc -
     * pricingId từ metadata là optional Với trường hợp UPGRADED và STATUS_CHANGED, cập nhật thông tin mới nhất của component
     */
    private List<PackagePlanCreateDTO> buildPackageItems(List<Long> packageItemIds, Long componentId, String type, String eventType) {
        List<PackagePlanCreateDTO> result = new ArrayList<>();
        for (Long packageItemId : packageItemIds) {
            try {
                // Lấy thông tin package item
                PackageItems packageItem = packageItemRepository.findById(packageItemId)
                        .orElse(null);
                if (packageItem == null) {
                    continue;
                }
                PackagePlanCreateDTO planCreateDTO = new PackagePlanCreateDTO();
                Long multiPlanId = packageItem.getPlanId();
                if (multiPlanId == null) {
                    log.warn("Không tìm thấy multiPlanId (plan_id) trong package item {}", packageItemId);
                    continue;
                }
                // Lấy thông tin từ metadata để xác định pricing_id và các thông tin khác
                Long pricingId = extractPricingIdFromMetadata(packageItem);
                Long variantId = extractVariantIdFromMetadata(packageItem);
                Long serviceId = extractServiceIdFromMetadata(packageItem);
                Long serviceDraftId = extractServiceDraftIdFromMetadata(packageItem);

                // Thiết lập các thông tin cơ bản
                planCreateDTO.setPricingId(pricingId);
                planCreateDTO.setMultiPlanId(multiPlanId);
                planCreateDTO.setVariantId(variantId);
                planCreateDTO.setQuantity(packageItem.getQuantity());
                planCreateDTO.setTotalAmount(packageItem.getTotalAmount());

                // Lấy thông tin giá từ package_item_prices nếu có
                List<PackageItemPrices> itemPrice = packageItemPricesRepository.findByPackageItemId(packageItemId);
                if (!itemPrice.isEmpty()) {
                    planCreateDTO.setPriceUpdate(itemPrice.get(0).getPrice());
                    planCreateDTO.setPricePreTax(itemPrice.get(0).getPrice());
                }

                // Lấy danh sách coupon IDs cho package item với thông tin mới nhất
                List<Long> couponIds = getCouponIdsForPackageItemWithLatestInfo(packageItemId, componentId, type, eventType);
                planCreateDTO.setCouponIds(couponIds);

                // Lấy danh sách addon cho package item
                List<PackageItemAddons> addons = packageAddonRepository.findByPackageItemId(packageItemId);
                if (!addons.isEmpty()) {
                    List<PackageAddonCreateDTO> addonList = new ArrayList<>();
                    for (PackageItemAddons addon : addons) {
                        PackageAddonCreateDTO addonDTO = new PackageAddonCreateDTO();
                        addonDTO.setAddonId(addon.getAddonId());
                        addonDTO.setAddonPlanId(addon.getAddonPlanId());
                        addonDTO.setQuantity(addon.getQuantity());
                        addonDTO.setTotalAmount(addon.getTotalAmount());

                        // Lấy danh sách coupon cho addon
                        List<Long> addonCouponIds = getCouponIdsForAddonItemWithLatestInfo(addon.getId(), componentId, type, eventType);
                        addonDTO.setCouponIds(addonCouponIds);

                        addonList.add(addonDTO);
                    }
                    planCreateDTO.setPackageAddon(addonList);
                }

                result.add(planCreateDTO);
            } catch (Exception e) {
                log.error("Lỗi khi build package item {}: {}", packageItemId, e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * Lấy danh sách coupon IDs cho package item với thông tin mới nhất của component
     */
    private List<Long> getCouponIdsForPackageItemWithLatestInfo(Long packageItemId, Long componentId, String type, String eventType) {
        try {
            List<PackageItemPromotions> promotions = packageItemPromotionsRepository.findByPackageItemId(packageItemId);
            List<Long> couponIds = promotions.stream()
                    .map(PackageItemPromotions::getCouponId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // Với trường hợp UPGRADED và STATUS_CHANGED, cập nhật thông tin mới nhất
            if (("UPGRADED".equals(eventType) || "STATUS_CHANGED".equals(eventType)) && "COUPON".equals(type)) {
                couponIds = updateCouponListWithLatestInfo(couponIds, componentId, eventType);
            }
            return couponIds;
        } catch (Exception e) {
            log.error("Lỗi khi lấy coupon IDs cho package item {}: {}", packageItemId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Lấy danh sách coupon IDs cho addon item với thông tin mới nhất
     */
    private List<Long> getCouponIdsForAddonItemWithLatestInfo(Long addonItemId, Long componentId, String type, String eventType) {
        try {
            List<PackageItemAddonPromotions> promotions = packageItemAddonPromotionsRepository.findByPackageItemAddonId(addonItemId);
            List<Long> couponIds = promotions.stream()
                    .map(PackageItemAddonPromotions::getCouponId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // Với trường hợp UPGRADED và STATUS_CHANGED, cập nhật thông tin mới nhất
            if (("UPGRADED".equals(eventType) || "STATUS_CHANGED".equals(eventType)) && "COUPON".equals(type)) {
                couponIds = updateCouponListWithLatestInfo(couponIds, componentId, eventType);
            }
            return couponIds;
        } catch (Exception e) {
            log.error("Lỗi khi lấy coupon IDs cho addon item {}: {}", addonItemId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Cập nhật danh sách coupon với thông tin mới nhất
     */
    private List<Long> updateCouponListWithLatestInfo(List<Long> currentCouponIds, Long componentId, String eventType) {
        try {
            List<Long> updatedCouponIds = new ArrayList<>(currentCouponIds);
            if ("UPGRADED".equals(eventType)) {
                // Với UPGRADED, thay thế coupon cũ bằng coupon mới (draft -> published)
                // Giả sử componentId là coupon_draft_id, cần lấy coupon_id mới
                Long newCouponId = getPublishedCouponIdFromDraft(componentId);
                if (newCouponId != null) {
                    // Tìm và thay thế coupon cũ
                    for (int i = 0; i < updatedCouponIds.size(); i++) {
                        if (isCouponRelatedToDraft(updatedCouponIds.get(i), componentId)) {
                            updatedCouponIds.set(i, newCouponId);
                            break;
                        }
                    }
                }
            } else if ("STATUS_CHANGED".equals(eventType)) {
                // Với STATUS_CHANGED, kiểm tra trạng thái mới của coupon
                if (isCouponActive(componentId)) {
                    // Coupon vẫn active, giữ nguyên
                    log.debug("Coupon {} vẫn active, giữ nguyên trong package", componentId);
                } else {
                    // Coupon không active, có thể cần xóa (nhưng logic này đã được xử lý ở removeCouponFromPackage)
                    log.debug("Coupon {} không active, cần xem xét xóa khỏi package", componentId);
                }
            }

            return updatedCouponIds;
        } catch (Exception e) {
            log.error("Lỗi khi cập nhật coupon list với thông tin mới nhất: {}", e.getMessage(), e);
            return currentCouponIds; // Trả về danh sách cũ nếu có lỗi
        }
    }

    /**
     * Lấy coupon ID đã được publish từ coupon draft ID
     */
    private Long getPublishedCouponIdFromDraft(Long couponDraftId) {
        try {
            // Lấy thông tin coupon draft
            Optional<CouponDraft> couponDraftOpt = couponDraftRepository.findById(couponDraftId);
            if (couponDraftOpt.isPresent()) {
                CouponDraft couponDraft = couponDraftOpt.get();
                Long couponId = couponDraft.getCouponId();
                if (couponId != null) {
                    // Kiểm tra coupon có tồn tại và active không
                    Optional<Coupon> couponOpt = couponRepository.findCouponByIdAndDeletedFlag(couponId, 1);
                    if (couponOpt.isPresent()) {
                        return couponId;
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Lỗi khi lấy published coupon ID từ draft {}: {}", couponDraftId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Kiểm tra coupon có liên quan đến draft không
     */
    private boolean isCouponRelatedToDraft(Long couponId, Long couponDraftId) {
        try {
            log.debug("Kiểm tra coupon {} có liên quan đến draft {} không", couponId, couponDraftId);
            // Lấy thông tin coupon draft
            Optional<CouponDraft> couponDraftOpt = couponDraftRepository.findById(couponDraftId);
            if (couponDraftOpt.isPresent()) {
                CouponDraft couponDraft = couponDraftOpt.get();
                Long draftCouponId = couponDraft.getCouponId();
                // Kiểm tra xem coupon_id trong draft có khớp với couponId không
                boolean isRelated = Objects.equals(couponId, draftCouponId);
                return isRelated;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra mối quan hệ coupon-draft: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Kiểm tra coupon có đang active không
     */
    private boolean isCouponActive(Long couponId) {
        try {
            // Lấy thông tin coupon
            Optional<Coupon> couponOpt = couponRepository.findCouponByIdAndDeletedFlag(couponId, 1);
            if (couponOpt.isPresent()) {
                Coupon coupon = couponOpt.get();
                boolean isActive = coupon.getStatus() != null && coupon.getStatus() == 1 &&
                        coupon.getApprove() != null && coupon.getApprove() == 1 &&
                        (coupon.getEndDate() == null || !coupon.getEndDate().isBefore(LocalDate.now()));
                return isActive;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra trạng thái coupon {}: {}", couponId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Lấy addon ID đã được publish từ addon draft ID
     */
    private Long getPublishedAddonIdFromDraft(Long addonDraftId) {
        try {
            Long addonId = addonRepository.getAddonIdByAddonDraftId(addonDraftId);
            if (addonId != null) {
                return addonId;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Lỗi khi lấy published addon ID từ draft {}: {}", addonDraftId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Lấy addon plan ID mới nhất
     */
    private Long getLatestAddonPlanId(Long addonId) {
        try {
            log.debug("Lấy addon plan ID mới nhất cho addon: {}", addonId);
            // Lấy danh sách pricing multi plan cho addon
            List<PricingMultiPlan> pricingMultiPlans = pricingMultiPlanRepository.findByAddonIdAndDeletedFlag(addonId, 1);
            // Sắp xếp theo created_at DESC để lấy plan mới nhất
            pricingMultiPlans.sort((p1, p2) -> p2.getCreatedAt().compareTo(p1.getCreatedAt()));
            if (!pricingMultiPlans.isEmpty()) {
                Long latestPlanId = pricingMultiPlans.get(0).getId();
                return latestPlanId;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Lỗi khi lấy addon plan ID mới nhất cho addon {}: {}", addonId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Lấy pricing_id từ metadata của package item
     */
    private Long extractPricingIdFromMetadata(PackageItems packageItem) {
        try {
            if (packageItem.getMetadata() != null && packageItem.getMetadata().containsKey(JSONBConstant.PRICING_ID)) {
                Object pricingIdObj = packageItem.getMetadata().get(JSONBConstant.PRICING_ID);
                if (pricingIdObj instanceof Number) {
                    return ((Number) pricingIdObj).longValue();
                } else if (pricingIdObj instanceof String) {
                    return Long.parseLong((String) pricingIdObj);
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Lấy variant_id từ metadata của package item
     */
    private Long extractVariantIdFromMetadata(PackageItems packageItem) {
        try {
            if (packageItem.getMetadata() != null && packageItem.getMetadata().containsKey(JSONBConstant.VARIANT_ID)) {
                Object variantIdObj = packageItem.getMetadata().get(JSONBConstant.VARIANT_ID);
                if (variantIdObj instanceof Number) {
                    return ((Number) variantIdObj).longValue();
                } else if (variantIdObj instanceof String) {
                    return Long.parseLong((String) variantIdObj);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Lỗi khi extract variant_id từ metadata: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Lấy service_id từ metadata của package item
     */
    private Long extractServiceIdFromMetadata(PackageItems packageItem) {
        try {
            if (packageItem.getMetadata() != null && packageItem.getMetadata().containsKey(JSONBConstant.SERVICE_ID)) {
                Object serviceIdObj = packageItem.getMetadata().get(JSONBConstant.SERVICE_ID);
                if (serviceIdObj instanceof Number) {
                    return ((Number) serviceIdObj).longValue();
                } else if (serviceIdObj instanceof String) {
                    return Long.parseLong((String) serviceIdObj);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Lỗi khi extract service_id từ metadata: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Lấy service_draft_id từ metadata của package item
     */
    private Long extractServiceDraftIdFromMetadata(PackageItems packageItem) {
        try {
            if (packageItem.getMetadata() != null && packageItem.getMetadata().containsKey(JSONBConstant.SERVICE_DRAFT_ID)) {
                Object serviceDraftIdObj = packageItem.getMetadata().get(JSONBConstant.SERVICE_DRAFT_ID);
                if (serviceDraftIdObj instanceof Number) {
                    return ((Number) serviceDraftIdObj).longValue();
                } else if (serviceDraftIdObj instanceof String) {
                    return Long.parseLong((String) serviceDraftIdObj);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Lỗi khi extract service_draft_id từ metadata: {}", e.getMessage());
            return null;
        }
    }

    private void deletePackageMappings(List<PackageMappings> packageMappings) {
        packageMappings.forEach(item -> {
            item.setDeleteFlag(DeletedFlag.DELETED.getValue());
            packageMappingRepository.save(item);
        });
    }

}
