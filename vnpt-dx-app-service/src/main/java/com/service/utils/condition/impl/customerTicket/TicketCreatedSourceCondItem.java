package com.service.utils.condition.impl.customerTicket;

import java.util.Objects;
import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.constants.OperatorConstant.OperatorEnum;
import com.service.utils.jsonObject.McIfConditionDTO;

public class TicketCreatedSource<PERSON>ondI<PERSON> extends CondItemBase {

    public TicketCreatedSourceCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        if (Objects.equals(OperatorEnum.ALTER, operator)) {
            String sql = "id in (select object_id from vnpt_dev.data_field_log where %s and object_type = 'CUSTOMER_TICKET')";
            return String.format(sql, super.getConditionByListIDColumn("created_source"));
        }
        return super.getConditionByListIDColumn("created_source");
    }
}
