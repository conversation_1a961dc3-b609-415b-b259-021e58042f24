package com.service.utils.condition.impl.cutomerContact;

import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.jsonObject.McIfConditionDTO;

public class ContactOrganizationCondItem extends CondItemBase {

    public ContactOrganizationCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
    }

    @Override
    public String getCondition(Integer objectType) {
        return super.getConditionByFixStringColumn("contact_organization");
    }
}
