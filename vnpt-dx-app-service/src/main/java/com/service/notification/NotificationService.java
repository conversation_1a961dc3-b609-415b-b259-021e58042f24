/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @version    : 1.0
 * 17/03/2021
 */

package com.service.notification;

import com.dto.notification.IFCMTokenDTO;
import com.onedx.common.dto.mobile.notification.CountNotifyUnReadDTO;
import com.onedx.common.dto.mobile.notification.MobileNotifyUnReadDetailDTO;
import com.onedx.common.dto.notification.NotificationByUserDTO;
import com.dto.notification.NotificationCountResDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.dto.notification.NotificationResponseDTO;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.emails.MobileNotificationTypeEnum;
import com.onedx.common.dto.mobile.notification.IMobileNotificationDetailDTO;
import com.onedx.common.entity.notification.Notification;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;


public interface NotificationService {

    /**
     * cap nhap trang thai da doc
     */
    void updateStatus(Long notificationId);
    
    /**
     * get list notification by userId
     */
    List<NotificationResponseDTO> getNotificationByUserId(Long id, Pageable pageable, PortalType portal);

    /**
     * danh sach notif theo loai thong bao
     */
    Page<IMobileNotificationDetailDTO> getMobileNotificationByUserId(Long currentUserId, MobileNotificationTypeEnum type, String search,
        Integer isTitle, Integer isContent, Pageable pageable);

    CountNotifyUnReadDTO countNotifMobileByUserId(Long currentUserId, Integer portalType, Integer status);

    MobileNotifyUnReadDetailDTO getMobileNotifyUnReadByUserId(Long currentUserId, String search,
        Integer isTitle, Integer isContent, Integer status, Pageable pageable);

    /**
     * get list notification by userId with search
     *
     * @return page notification by userId with search
     */
    Page<NotificationDTO> findNotificationByUser(Pageable pageable, String searchTitle, String searchBody, PortalType portal);

    Page<NotificationByUserDTO> findAllNotificationByUser(Pageable pageable, String searchInfo, PortalType portal);

    /**
     * gui notify
     *
     * @param id action id
     */
    void pushNotify(NotificationDTO notificationDTO, Long id, String actionCode);

    Notification pushNotify(NotificationDTO notificationDTO, Long actionId, String actionCode, Set<IFCMTokenDTO> setUserToken);

    List<Notification> saveListNewNotification(List<Notification> notifications);

    /**
     * thong ke so luong notification moi theo user
     */
    NotificationCountResDTO countNewNotify(Integer portalType);

    /**
     * update so luong thong bao tren chuong
     */
    void updateCountByUserIdAndPortalType(String portalType);
    
    /**
     * xoa thong bao theo danh sach id
     * 
     * @param ids danh sach id can xoa
     */
    void deleteById(List<String> ids);

    /**
     * tim kiem thong bao theo id
     * 
     * @param id id thong bao
     * @return Optional thong tin chi tiet cua thong bao
     */
    Optional<NotificationDTO> findById(Long id);
}
