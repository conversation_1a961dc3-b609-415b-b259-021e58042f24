package com.service.notification.template;

import java.util.HashMap;
import java.util.List;
import com.constant.SubscriptionConstant;
import com.dto.actionNotification.EmailParamDTO;
import com.enums.ActionNotificationEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.service.notification.impl.ActionNotificationTemplateBase;

public class SB02 extends ActionNotificationTemplateBase {

    public SB02(List<ICommonIdNameEmail> lstReceiver, String webHost) {
        // Type
        this.actionNotificationEnum = ActionNotificationEnum.SB01;

        // Email
        for (ICommonIdNameEmail receiver : lstReceiver) {
            EmailParamDTO emailParamDTO = new EmailParamDTO();
            emailParamDTO.setEmail(receiver.getAssigneeEmail());
            HashMap<String, String> lstParam = emailParamDTO.getLstParam();
            lstParam.put("$USER", receiver.getAssigneeName());
            lstParam.put("$NAME_SERVICE", receiver.getServiceName());
            lstParam.put("$NAME_PRICING", receiver.getPricingName());
            lstParam.put("$NAME_COMPANY", receiver.getSmeName());
            lstParam.put("$REMAIN_DAYS", String.valueOf(receiver.getDaysLeft()));
            lstParam.put("$SUB_CODE", receiver.getSubCode());
            lstParam.put("$TOTAL_AMOUNT", SB01.formatMoneyVietnamese(receiver.getTotalAmount()));
            lstParam.put("$NOTE", SB01.getNote(receiver));

            String typeLinkRenewing = SubscriptionConstant.LINK_SME_EXTEND_SERVICE;
            if (receiver.getPortalName() == PortalType.DEV) {
                typeLinkRenewing = SubscriptionConstant.LINK_DEV_EXTEND_SERVICE;
            } else if (receiver.getPortalName() == PortalType.ADMIN) {
                typeLinkRenewing = SubscriptionConstant.LINK_ADMIN_EXTEND_SERVICE;
            }
            String linkRenewing = String.format(typeLinkRenewing, webHost, receiver.getSubId());
            lstParam.put("$LINK_RENEWING", linkRenewing);

            this.lstEmailParam.add(emailParamDTO);
        }
    }
}
