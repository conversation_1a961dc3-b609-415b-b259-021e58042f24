package com.service.subscriptions.impl;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.constant.SubscriptionConstant;
import com.constant.SubscriptionConstant.MailParam;
import com.constant.SubscriptionConstant.MailParams;
import com.constant.SubscriptionConstant.UpdateContentTemplate;
import com.constant.enums.combo.ComboChangeEnum;
import com.constant.enums.pricing.CancelTimeTypeEnum;
import com.constant.enums.subscription.ChangeSubActionEnum;
import com.constant.enums.subscription.InstallationStatusEnum;
import com.constant.enums.subscription.SubTypeEnum;
import com.dto.addons.AddonsResDTO;
import com.dto.bills.caculate.BillCostIncurred;
import com.dto.bills.caculate.BillItemDTO;
import com.dto.bills.caculate.BillPopUpCostIncurred;
import com.dto.combo.ComboTransactionDTO;
import com.dto.coupons.mailParam.MailSendParamDTO;
import com.onedx.common.dto.integration.backend.IntegrationKafkaDTO;
import com.onedx.common.dto.integration.backend.IntegrationOneSmeDTO;
import com.onedx.common.dto.integration.backend.KafkaMessageResponseDTO;
import com.onedx.common.dto.integration.backend.KafkaParamResDTO;
import com.onedx.common.dto.integration.backend.subscription.QuantityResDTO;
import com.dto.pricing.PricingProcessDTO;
import com.dto.pricing.PricingTransactionDTO;
import com.dto.subscriptions.MultiSubscriptionCancelDTO;
import com.dto.subscriptions.SubscriptionEditorDTO;
import com.dto.subscriptions.SubscriptionSwapComboDevDTO;
import com.dto.subscriptions.SubscriptionTransactionDTO;
import com.dto.subscriptions.SubscriptionUpdateUserDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.users.UserTransactionDTO;
import com.entity.addons.Addon;
import com.entity.address.Address;
import com.entity.bills.BillItem;
import com.entity.bills.Bills;
import com.entity.combo.ChangeSubscriptionComboCoupon;
import com.entity.combo.ComboPlan;
import com.entity.combo.SubscriptionComboAddon;
import com.entity.department.Department;
import com.entity.enterprise.Enterprise;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.ChangeCustomFee;
import com.entity.subscriptions.ChangeSubscription;
import com.entity.subscriptions.ChangeSubscriptionCoupons;
import com.entity.subscriptions.ChangeSubscriptionPricingCoupon;
import com.entity.subscriptions.ChangeSubscriptionSetupFee;
import com.entity.subscriptions.ChangeSubscriptionUnitLimited;
import com.entity.subscriptions.Subscription;
import com.entity.subscriptions.SubscriptionAddons;
import com.entity.transaction_log.TransactionLog;
import com.enums.ActionChangeSubEnum;
import com.enums.ActionNotificationEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.StatusEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.integration.producer.subscription.SubscriptionProducer;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.emails.ParamEmailEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.services.ServiceTypeEnum;
import com.onedx.common.constants.enums.subscriptions.BillItemType;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.entity.subscriptions.SubscriptionSetupFee;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.DataInvalidException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.contact.ContactProvinceRepository;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.PhoneUtils;
import com.repository.addons.AddonRepository;
import com.repository.address.AddressRepository;
import com.repository.bills.BillItemRepository;
import com.repository.bills.BillsRepository;
import com.repository.combo.ChangeSubscriptionComboCouponRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.enterprise.EnterpriseRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.ChangeCustomFeeRepository;
import com.repository.subscriptions.ChangeSubscriptionCouponsRepository;
import com.repository.subscriptions.ChangeSubscriptionPricingCouponRepository;
import com.repository.subscriptions.ChangeSubscriptionRepository;
import com.repository.subscriptions.ChangeSubscriptionSetupFeeRepository;
import com.repository.subscriptions.ChangeSubscriptionUnitLimitedRepository;
import com.repository.subscriptions.SubscriptionAddonsRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.users.UserRepository;
import com.service.combo.ComboPlanService;
import com.service.coupon.CouponService;
import com.service.email.EmailService;
import com.service.email.EmailTemplateService;
import com.service.multiplePeriod.SubMultiplePeriod;
import com.service.notification.ActionNotificationService;
import com.service.notification.template.SCD03;
import com.service.pricing.PricingService;
import com.service.report.dashboardSme.ReportService;
import com.service.subscriptions.SubscriptionDetailService;
import com.service.subscriptions.SubscriptionHelperService;
import com.service.subscriptions.SubscriptionService;
import com.service.transactionLog.TransactionLogService;
import com.service.users.UserService;
import com.util.AuthUtil;
import com.util.NotifyUtil;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.3
 * @created 05/10/2021
 */
@Service
@Slf4j
public class SubscriptionHelperServiceImpl implements SubscriptionHelperService {

    private static final Integer DEFAULT_USED_QUANTITY = 1;
    private static final Integer TYPE_DEFAULT = 1;
    private static final String BATCH = "batch";
    private static final int ADMIN = 1;
    private static final int DEV = 2;
    private static final int SME = 3;
    private static final String[] serviceMessage = {"service"};
    private static final String[] subscriptionMessage = {"subscription"};
    private static final Object[] userMessage = {"user"};
    private static final String[] errorTaxCode = {"taxCode"};
    private static final String[] errorPhoneNumber = {"phoneNumber"};
    private static final String[] errorTin = {"tin"};

    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private ContactProvinceRepository contactProvinceRepository;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private EnterpriseRepository enterpriseRepository;
    @Autowired
    private SubscriptionProducer subscriptionProducer;
    @Autowired
    private ParamEmailRepository paramEmailRepository;
    @Autowired
    private SubscriptionAddonsRepository subscriptionAddonsRepository;
    @Autowired
    private AddonRepository addonRepository;
    @Autowired
    private EmailService emailService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private ChangeSubscriptionSetupFeeRepository changeSubscriptionSetupFeeRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ChangeSubscriptionRepository changeSubscriptionRepository;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private BillItemRepository billItemRepository;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private CouponService couponService;
    @Autowired
    private SubscriptionService subscriptionService;
    @Autowired
    private ChangeSubscriptionCouponsRepository changeSubscriptionCouponsRepository;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private ChangeSubscriptionPricingCouponRepository changeSubscriptionPricingCouponRepository;
    @Autowired
    private  ChangeSubscriptionUnitLimitedRepository changeSubscriptionUnitLimitedRepository;
    @Autowired
    private ChangeSubscriptionComboCouponRepository changeSubscriptionComboCouponRepository;
    @Autowired
    private ChangeCustomFeeRepository changeCustomFeeRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private SubMultiplePeriod subMultiplePeriod;
    @Autowired
    private ComboPlanService comboPlanService;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private TransactionLogService transactionLogService;
    @Autowired
    private ActionNotificationService actionNotificationService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private SubscriptionDetailService subscriptionDetailService;

    @Value("${web.host}")
    private String webHost;

    @Override
    public Optional<Integer> getInstallationStatusOfSubscription(Subscription subscription) {
        Optional<ServiceEntity> serviceEntityOpt = serviceRepository
            .findByIdAndDeletedFlag(subscription.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
        if (!serviceEntityOpt.isPresent()) {
            String messageNotFound = messageSource
                .getMessage(MessageKeyConstant.NOT_FOUND, serviceMessage, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(messageNotFound, Resources.SERVICES, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        }

        if (ServiceTypeEnum.VNPT.getValue().equals(serviceEntityOpt.get().getServiceOwner())
            || ServiceTypeEnum.SAAS.getValue().equals(serviceEntityOpt.get().getServiceOwner())) {
            return Optional.of(InstallationStatusEnum.of(subscription.getInstalled()).getValue());
        }
        return Optional.empty();
    }

    @Override
    public void validateInstallationStatusOfSubscription(Subscription subscription) {
        InstallationStatusEnum installationStatusEnum = InstallationStatusEnum.of(subscription.getInstalled());
        if (InstallationStatusEnum.INSTALLING.equals(installationStatusEnum) || InstallationStatusEnum.FAIL.equals(installationStatusEnum)) {
            String message = messageSource.getMessage(MessageKeyConstant.SUBSCRIPTION_CAN_NOT_UPDATE, new Object[]{ErrorKey.ID},
                LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SUBSCRIPTION, ErrorKey.ID, MessageKeyConstant.SUBSCRIPTION_CAN_NOT_UPDATE);
        }
    }

    @Override
    @Transactional
    public BaseResponseDTO updateInfoUser(SubscriptionUpdateUserDTO dto) {
        User user = userRepository.findByIdAndDeletedFlag(AuthUtil.getCurrentParentId(), DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> {
                String messageNotFound = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, userMessage, null);
                return new ResourceNotFoundException(messageNotFound, Resources.USER, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });
        validateInputForCustomerType(dto);

        // check trùng mã số thuế
        if (!StringUtils.isEmpty(dto.getTaxCode())) {
            Optional<User> checkTaxCode = userRepository
                .findFirstByCorporateTaxCodeOrderByIdDesc(dto.getTaxCode());
            if (checkTaxCode.isPresent() && !StringUtils.equals(dto.getTaxCode(), user.getCorporateTaxCode())) {
                String messageTaxCode = messageSource
                    .getMessage(MessageKeyConstant.DATA_EXISTS, errorTaxCode, null);
                throw new DataInvalidException(messageTaxCode, Resources.USER,
                    ErrorKey.User.TAX_CODE, MessageKeyConstant.DATA_EXISTS);
            }
        }

        // check trùng số điện thoại
        if (!StringUtils.isEmpty(dto.getPhoneNumber())) {
            List<User> checkPhoneNumber = userRepository
                .findByPhoneNumber(dto.getPhoneNumber());
            if (!CollectionUtils.isEmpty(checkPhoneNumber) && !StringUtils.equals(dto.getPhoneNumber(), user.getPhoneNumber())) {
                String messageTaxCode = messageSource
                    .getMessage(MessageKeyConstant.DATA_EXISTS, errorPhoneNumber, null);
                throw new DataInvalidException(messageTaxCode, Resources.USER,
                    ErrorKey.User.PHONE_NUMBER, MessageKeyConstant.DATA_EXISTS);
            }
        }
        user.setCorporateTaxCode(dto.getTaxCode());
        user.setName(dto.getName());
        user.setAddress(dto.getAddress());
        user.setPhoneNumber(dto.getPhoneNumber());
        user.setPhoneSmsGateway(PhoneUtils.convertIgnoreFormatPhoneNumber(dto.getPhoneNumber()));
        user.setRepPersonalCertNumber(dto.getIdentityNo());
        
        //Lưu setup address
        if (Objects.nonNull(dto.getSetupAddress())) {
            Address address = addressRepository.findFirstByUserIdAndTypeAndDefaultLocation(
                    user.getId(), TYPE_DEFAULT, DEFAULT_USED_QUANTITY);
            if (Objects.nonNull(address) && !dto.getSetupAddress().equals(address.getAddress())) {
                address.setProvinceId(dto.getProvinceId());
                address.setProvinceCode(dto.getProvinceCode());
                address.setProvinceName(dto.getProvinceName());

                address.setDistrictId(dto.getDistrictId());
                address.setDistrictCode(dto.getDistrictCode());
                address.setDistrictName(dto.getDistrictName());

                address.setWardId(dto.getWardId());
                address.setWardCode(dto.getWardCode());
                address.setWardName(dto.getWardName());

                address.setStreetId(dto.getStreetId());
                address.setStreetName(dto.getStreetName());
                address.setAddress(dto.getSetupAddress());
                addressRepository.save(address);
            }
        }

        // Lưu enterprise
        Optional<Enterprise> enterpriseOpt = enterpriseRepository.findOneByEmailAndCustomerCode(
            AuthUtil.getCurrentUser().getEmail(), CharacterConstant.BLANK, // khi cập nhật màn thanh toán, cho phép sửa số điện thoại
            AuthUtil.getCurrentUser().getCustomerType());

        // Nếu là KH cá nhân, cập nhật trường số chứng thực cá nhân
        if (enterpriseOpt.isPresent()) {
            Enterprise enterprise = enterpriseOpt.get();
            // Check trùng mã số thuế của enterprise
            if (!Objects.equals(CustomerTypeEnum.PERSONAL.getValue(), AuthUtil.getCurrentUser().getCustomerType()) &&
                enterpriseRepository.existEnterpriseByTin(dto.getTaxCode(), enterprise.getId())) {
                String messageTaxCode = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS, errorTin, null);
                throw new DataInvalidException(messageTaxCode, Resources.ENTERPRISE, ErrorKey.Enterprise.TIN, MessageKeyConstant.DATA_EXISTS);
            }
            enterprise.setName(dto.getName());
            enterprise.setAddress(dto.getAddress());
            enterprise.setPhone(dto.getPhoneNumber());
            enterprise.setTin(dto.getTaxCode());
            enterprise.setSetupAddress(dto.getSetupAddress());
            if (CustomerTypeEnum.PERSONAL.getValue().equals(AuthUtil.getCurrentUser().getCustomerType())) {
                user.setFirstName(dto.getFirstName());
                user.setLastName(dto.getLastName());
                enterprise.setRepIdentityNo(dto.getIdentityNo());
                enterprise.setName(dto.getLastName().concat(CharacterConstant.SPACE).concat(dto.getLastName()));
            }
            //save enterprise
            enterpriseRepository.save(enterprise);
        }
        userRepository.save(user);
        return new BaseResponseDTO(user.getId());
    }

    /**
     * Validate du lieu dau vao theo customerType
     */
    private void validateInputForCustomerType(SubscriptionUpdateUserDTO dto) {
        CustomerTypeEnum customerType = CustomerTypeEnum.getValueOf(AuthUtil.getCurrentUser().getCustomerType());
        if (CustomerTypeEnum.PERSONAL.equals(customerType)) {
            if (dto.getLastName() == null) {
                String m = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, new String[] {"lastName"}, null);
                throw new BadRequestException(m, Resources.USER,
                    ErrorKey.User.LAST_NAME, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
            }
            if (dto.getFirstName() == null) {
                String m = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, new String[] {"firstName"}, null);
                throw new BadRequestException(m, Resources.USER,
                    ErrorKey.User.FIRST_NAME, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
            }
            if (dto.getIdentityNo() == null) {
                String m = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, new String[] {"identityNo"}, null);
                throw new BadRequestException(m, Resources.USER,
                    ErrorKey.User.IDENTITY_NO, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
            }
        } else {
            if (StringUtils.isBlank(dto.getTaxCode())) {
                String m = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, new String[] {"taxCode"}, null);
                throw new BadRequestException(m, Resources.USER,
                    ErrorKey.User.TAX_CODE, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
            }
            if (StringUtils.isBlank(dto.getName())) {
                String m = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, new String[] {"name"}, null);
                throw new BadRequestException(m, Resources.USER,
                    ErrorKey.User.NAME, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
            }
        }
    }

    /**
     * Xóa subscription
     */
    @Override
    public void deletedSubscription(List<MultiSubscriptionCancelDTO> subsLst) {
        Set<Long> subIds = subsLst.stream().map(MultiSubscriptionCancelDTO::getId).collect(Collectors.toSet());
        validateSubscriptionDeleted(subIds);
        subscriptionRepository.deleteAllSubscriptionByIds(subIds);
    }

    @Override
    public Boolean changePricingQuantityKafka(KafkaParamResDTO params, Subscription subscription, Pricing pricing,
        IntegrationActionTypeEnum actionType, IntegrationOneSmeDTO paramDTO, KafkaMessageResponseDTO kafkaMessageResponseDTO) {
        log.info("============START CHANGE PRICING QUANTITY KAFKA================");
        if (Objects.isNull(subscription)) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("USER_NOT_OWN_SUBSCRIPTION");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }

        if (Objects.nonNull(paramDTO)) {
            if (Objects.nonNull(params.getPricing())) {
                if (!validateDHSXKDPricingQuantity(params.getPricing(), subscription.getQuantity(), paramDTO, actionType, kafkaMessageResponseDTO)) {
                    return false;
                }
                subscriptionRepository.updateUsedQuantityBySubscriptionIdAndPricingId(subscription.getId(), params.getPricing().getId(),
                    params.getPricing().getQuantity());
            }
            if (!CollectionUtils.isEmpty(params.getAddons())) {
                List<SubscriptionAddons> subscriptionAddons = subscriptionAddonsRepository
                    .findBySubscriptionIdAndAddonsIdIn(subscription.getId(), params.getAddons().stream().map(
                        QuantityResDTO::getId).collect(Collectors.toList()));
                if (!validateDHSXKDAddonQuantity(params.getAddons(), subscriptionAddons, paramDTO, actionType, kafkaMessageResponseDTO)) {
                    return false;
                }
                subscriptionAddons.forEach(subscriptionAddon -> {
                    params.getAddons().stream().filter(e -> Objects.equals(subscriptionAddon.getAddonsId(), e.getId())).findFirst()
                        .ifPresent(e -> {
                            subscriptionAddon.setDhsxkdUsedQuantity(Objects.nonNull(e.getQuantity()) ? e.getQuantity().longValue() : 0L);
                        });
                });
                subscriptionAddonsRepository.saveAll(subscriptionAddons);
            }
        }
        return true;
    }

    /**
     * Validate số lượng pricing khi Kafka truyền
     */
    private boolean validateDHSXKDPricingQuantity(QuantityResDTO dhsxkdPricingQuantity, Long quantity, IntegrationOneSmeDTO paramDTO,
        IntegrationActionTypeEnum actionType, KafkaMessageResponseDTO kafkaMessageResponseDTO) {
        boolean result = true;
        if (quantity != -1 && dhsxkdPricingQuantity.getQuantity() > quantity) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("Số lượng sử dụng vượt quá số lượng đăng ký");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            result = false;
        }
        return result;
    }

    /**
     * Validate số lượng addons khi Kafka truyền
     */
    private boolean validateDHSXKDAddonQuantity(List<QuantityResDTO> dhsxkdAddonQuantity, List<SubscriptionAddons> subscriptionAddons,
        IntegrationOneSmeDTO paramDTO, IntegrationActionTypeEnum actionType, KafkaMessageResponseDTO kafkaMessageResponseDTO) {
        boolean result = true;
        for (SubscriptionAddons subscriptionAddon : subscriptionAddons) {
            Optional<QuantityResDTO> dhsxAddonOptional = dhsxkdAddonQuantity.stream()
                .filter(e -> Objects.equals(subscriptionAddon.getAddonsId(), e.getId())).findFirst();
            if (dhsxAddonOptional.isPresent() && subscriptionAddon.getQuantity() != -1
                && dhsxAddonOptional.get().getQuantity() > subscriptionAddon.getQuantity()) {
                paramDTO.setActionType(actionType.value);
                paramDTO.setStatus(400);
                paramDTO.setMessage("Số lượng sử dụng vượt quá số lượng đăng ký");
                sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
                result = false;
                break;
            }
        }
        return result;
    }

    /**
     * send message kafka
     */
    private void sendMessageKafka(KafkaMessageResponseDTO kafkaMessageResponseDTO,
        IntegrationOneSmeDTO paramDTO) {
        IntegrationKafkaDTO integrationKafkaDTO = IntegrationKafkaDTO.builder()
            .kafkaMessageId(kafkaMessageResponseDTO.getKafkaMessageId())
            .body(paramDTO).build();
        subscriptionProducer.transactionSubscription(integrationKafkaDTO,
            kafkaMessageResponseDTO.getBody().getTopic(), null);
    }

    /**
     * validate những subscription có thể được xóa
     */
    private void validateSubscriptionDeleted(Set<Long> subIds) {
        Set<Long> subIdsDB = subscriptionRepository.getAllIdsOfSubscriptionsDeleted(subIds);
        if (subIdsDB.size() != subIds.size()) {
            String message = messageSource.getMessage(MessageKeyConstant.SUBSCRIPTION_INVALID,
                new String[]{Arrays.toString(
                    subIds.stream().filter(e -> !subIdsDB.contains(e)).map(Object::toString).distinct().toArray(String[]::new))},
                LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SUBSCRIPTION, ErrorKey.ID,
                MessageKeyConstant.SUBSCRIPTION_INVALID);
        }
    }

    /**
     * Lấy ra danh sách list param của mail template db
     */
    private List<MailParamResDTO> getListPramMailDTO(Map<String, String> params, EmailCodeEnum emailCode) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(emailCode.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            mailParamResDTO.setValue(params.getOrDefault(mailParamResDTO.getParamName(), mailParamResDTO.getValue()));
        });
        return paramNameByCode;
    }

    /*
    @Override
    public void sendMailSC16(User actor, User parentOfActor, Subscription oldSubscription, Subscription newSubscription,
        List<SubscriptionAddons> oldSubAddons, List<SubscriptionAddons> newSubAddons, EmailCodeEnum emailCode, String timeUpdate) {

        Set<AddonNameDTO> oldAddons = addonRepository
            .findAddonNameByIds(oldSubAddons.stream().map(SubscriptionAddons::getAddonsId).collect(Collectors.toList()));
        Set<AddonNameDTO> newAddons = addonRepository
            .findAddonNameByIds(newSubAddons.stream().map(SubscriptionAddons::getAddonsId).collect(Collectors.toList()));

        Map<String, String> response = new HashMap<>();

        String serviceName = oldSubscription.getPricing().getServiceEntity().getServiceName();
        String oldPricingName = oldSubscription.getPricing().getPricingName();
        String newPricingName = newSubscription.getPricing().getPricingName();

        response.put(MailParam.NAME_SERVICE, serviceName);
        response.put(MailParam.NAME_PRICING, newPricingName);
        response.put(MailParam.CODE_TRANSACTION, newSubscription.getSubCodeDHSXKD());

        List<String> updateContents = new ArrayList<>();

        // set param nếu thay đổi thì mới push param
        // nếu thay đổi tên
        if (!Objects.equals(oldPricingName, newPricingName)) {
            updateContents.add(String.format(UpdateContentTemplate.CONTENT_NAME_PRICING, 1, oldPricingName,
                newPricingName));
        }

        // nếu thay số lượng pricing
        if (!Objects.equals(oldSubscription.getQuantity(), newSubscription.getQuantity())) {
            updateContents
                .add(String.format(UpdateContentTemplate.CONTENT_QUANTITY_PRICING, updateContents.size() + 1, oldSubscription.getQuantity(),
                    newSubscription.getQuantity()));

            // thay đổi đơn giá
            List<BillItem> oldBillPricingItems = billItemRepository
                .getAllBillItemPricingAndAddonsOfSubscriptionOldAndNew(newSubscription.getId(), 1, BillItemType.PRICING.value);
            List<BillItem> newBillPricingItems = billItemRepository
                .getAllBillItemPricingAndAddonsOfSubscriptionOldAndNew(newSubscription.getId(), 0, BillItemType.PRICING.value);

            // thay đổi đơn giá pricing
            oldBillPricingItems.stream().filter(e -> BillItemType.PRICING.value.equals(e.getObjectType())).forEach(oldBillItem -> {
                newBillPricingItems.stream()
                    .filter(e -> e.getObjectType().equals(oldBillItem.getObjectType()) && e.getObjectId().equals(oldBillItem.getObjectId())
                        && BigDecimal.ZERO.compareTo(e.getAmountAfterTax()) != 0 && BigDecimal.ZERO.compareTo(oldBillItem.getAmountAfterTax()) != 0
                        && !e.getAmountAfterTax().equals(oldBillItem.getAmountAfterTax()))
                    .findFirst().ifPresent(billItem -> {
                    updateContents.add(String.format(UpdateContentTemplate.CONTENT_PRICE_PRICING, updateContents.size() + 1,
                        couponService.convertFormatAmount(oldBillItem.getAmountAfterTax()),
                        couponService.convertFormatAmount(billItem.getAmountAfterTax())));
                });
            });
        }

        // nếu thay đổi số lượng và giá addon
        oldSubAddons.forEach(oldSubAddon -> {
            newSubAddons.stream().filter(e -> Objects.equals(e.getAddonsId(), oldSubAddon.getAddonsId())).findFirst().ifPresent(newSubAddon -> {
                if (!Objects.equals(oldSubAddon.getQuantity(), newSubAddon.getQuantity())) {
                    BillItem oldBillItem = new BillItem();
                    BillItem newBillItem = new BillItem();
                    if (Objects.equals(timeUpdate, SubscriptionConstant.UPDATE_AFTER_PAY)) {
                        oldBillItem = billItemRepository.getNearestBillItemBySubscriptionIdAndObjId(oldSubscription.getId(),
                            BillItemType.ADDON.value, oldSubAddon.getAddonsId(), 1);
                        newBillItem = billItemRepository.getNewestBillItemBySubscriptionIdAndObjId(newSubscription.getId(),
                            BillItemType.ADDON.value, newSubAddon.getAddonsId());
                    } else {
                        oldBillItem = billItemRepository.getNearestBillItemBySubscriptionIdAndObjId(oldSubscription.getId(),
                            BillItemType.ADDON.value, oldSubAddon.getAddonsId(), 1);
                        newBillItem = billItemRepository.getNearestBillItemBySubscriptionIdAndObjId(oldSubscription.getId(),
                            BillItemType.ADDON.value, oldSubAddon.getAddonsId(), 0);
                    }
                    //Thay đổi số lượng
                    updateContents.add(
                        String.format(UpdateContentTemplate.CONTENT_QUANTITY_ADDON, updateContents.size() + 1, oldBillItem.getItemName(),
                            oldSubAddon.getQuantity().toString(), newBillItem.getQuantity().toString()));
                    //Thay đổi giá
                    updateContents.add(String.format(UpdateContentTemplate.CONTENT_PRICE_ADDON, updateContents.size() + 1,
                        oldBillItem.getItemName(),
                        couponService.convertFormatAmount(oldBillItem.getAmountAfterTax()),
                        couponService.convertFormatAmount(newBillItem.getAmountAfterTax())));
                }
            });
        });

        // check xem addon có thêm mới không
        newAddons.forEach(newAddon -> {
            Optional<AddonNameDTO> oldAddonOptional = oldAddons.stream().filter(e -> newAddon.getId().equals(e.getId()))
                .findFirst();
            if (!oldAddonOptional.isPresent()) {
                // nếu không tồn tại chính tỏ addon đã bị xóa
                updateContents.add(
                    String.format(UpdateContentTemplate.CONTENT_NAME_ADDON_ADDED, updateContents.size() + 1,
                        newAddon.getName()));
            }
        });

        // nếu không tồn tại chính tỏ addon đã bị xóa
        oldSubAddons.forEach(oldAddon -> {
                Optional<SubscriptionAddons> newAddonOptional = newSubAddons.stream()
                    .filter(newAddon -> oldAddon.getAddonsId().equals(newAddon.getAddonsId()))
                    .findFirst();
                if (!newAddonOptional.isPresent()) {
                    oldAddons.stream().filter(e -> e.getId().equals(oldAddon.getAddonsId())).findFirst().ifPresent(addon -> {
                        updateContents.add(
                            String.format(UpdateContentTemplate.CONTENT_NAME_ADDON_REMOVE, updateContents.size() + 1, addon.getName()));
                    });
                }
            });

        response.put(MailParam.USER, actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName());
        String linkDetail = String.format(SubscriptionConstant.LINK_SME_SUBS_SERVICE_DETAIL, webHost, newSubscription.getId());
        response.put(MailParam.LINK_USE_SUBS, linkDetail);
        if (!CollectionUtils.isEmpty(updateContents)) {
            response.put(MailParam.CONTENT_NAME_PRICING, convertListToStringContentMail(updateContents));
        }

        response
            .put(MailParam.DATE_MODIFIED, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS));
        boolean isChangeNow = Objects.isNull(newSubscription.getPricing().getChangePricingDate()) || Objects.equals(newSubscription.getPricing().getChangePricingDate(), ComboChangeEnum.NOW.value);
        response.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(
            isChangeNow ? new Date() : DateUtil.toDate(DateUtil.toLocalDate(newSubscription.getEndCurrentCycle()).plusDays(1)),
            DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        response.put(MailParam.NAME_MODIFIED, actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()));

        sendMailTo(response, emailCode, actor.getEmail());
        if (Objects.nonNull(parentOfActor)) {
            response.put(MailParam.USER, parentOfActor.getLastName() + CharacterConstant.SPACE + parentOfActor.getFirstName());
            sendMailTo(response, emailCode, parentOfActor.getEmail());
        }
    }
    */

    @Override
    public void sendMailSC16UpdateSubs(User actor, User parentOfActor, Subscription oldSubscription, List<SubscriptionAddons> oldSubAddons,
        List<BillItem> newBillItems, List<Long> addonIdsAdd, List<Long> addonIdsRemove, EmailCodeEnum emailCode) {

        Map<String, String> response = new HashMap<>();
        HashMap<String, String> paramsToAssignee = new HashMap<>();

        String serviceName = oldSubscription.getPricing().getServiceEntity().getServiceName();
        String oldPricingName = oldSubscription.getPricing().getPricingName();
        String newPricingName = oldSubscription.getPricing().getPricingName();

        response.put(MailParam.NAME_SERVICE, serviceName);
        response.put(MailParam.NAME_PRICING, newPricingName);
        String transactionCode = ObjectUtil.getOrDefault(oldSubscription.getSubCodeDHSXKD(),newPricingName);
        response.put(MailParam.CODE_TRANSACTION, transactionCode);

        paramsToAssignee.put(MailParam.NAME_SERVICE, serviceName);
        paramsToAssignee.put(MailParam.NAME_PRICING, newPricingName);
        paramsToAssignee.put(MailParam.CODE_TRANSACTION, transactionCode);
        List<String> updateContents = new ArrayList<>();

        // set param nếu thay đổi thì mới push param
        // nếu thay đổi tên
        if (!Objects.equals(oldPricingName, newPricingName)) {
            updateContents.add(String.format(UpdateContentTemplate.CONTENT_NAME_PRICING, 1, oldPricingName,
                newPricingName));
        }
        BillItem oldBillPricingItems = billItemRepository.getNewestBillItemBySubscriptionIdAndObjId(oldSubscription.getId(),
            BillItemType.PRICING.getValue(), oldSubscription.getPricingId());

        Optional<BillItem> newBillPricingItems = newBillItems.stream().filter(b -> Objects.equals(b.getObjectType(), BillItemType.PRICING.getValue())
            && Objects.equals(b.getObjectId(), oldSubscription.getPricingId()) && (Objects.isNull(oldSubscription.getPricingMultiPlanId())
            || (Objects.nonNull(oldSubscription.getPricingMultiPlanId()) && Objects
            .equals(b.getMultiPlanId(), oldSubscription.getPricingMultiPlanId())))).findFirst();

        // nếu thay số lượng pricing
        if (newBillPricingItems.isPresent()) {
            if (!Objects.equals(oldSubscription.getQuantity(), newBillPricingItems.get().getQuantity())) {
                updateContents
                    .add(String.format(UpdateContentTemplate.CONTENT_QUANTITY_PRICING, updateContents.size() + 1, oldSubscription.getQuantity(),
                        newBillPricingItems.get().getQuantity()));
            }
            // thay đổi giá pricing
            if (!Objects.equals(oldBillPricingItems.getAmountAfterTax(), newBillPricingItems.get().getAmountAfterTax())) {
                updateContents.add(String.format(UpdateContentTemplate.CONTENT_PRICE_PRICING, updateContents.size() + 1,
                    couponService.convertFormatAmount(oldBillPricingItems.getAmountAfterTax()),
                    couponService.convertFormatAmount(newBillPricingItems.get().getAmountAfterTax())));

            }
        }

        // nếu thay đổi số lượng và giá addon
        List<BillItem> newBillAddonItems = newBillItems.stream().filter(b -> Objects.equals(b.getObjectType(), BillItemType.ADDON.getValue()))
            .collect(Collectors.toList());
        oldSubAddons.forEach(oldSubAddon -> {
            newBillAddonItems.stream().filter(bi -> Objects.equals(bi.getObjectType(), BillItemType.ADDON.getValue())
                && Objects.equals(bi.getObjectId(), oldSubAddon.getAddonsId())).findFirst().ifPresent(newBillAddon -> {
                if (!Objects.equals(oldSubAddon.getQuantity(), newBillAddon.getQuantity())) {
                    BillItem oldBillItem = billItemRepository.getNewestBillItemBySubscriptionIdAndObjId(oldSubscription.getId(),
                        BillItemType.ADDON.getValue(), oldSubAddon.getAddonsId());
                    //Thay đổi số lượng
                    updateContents.add(
                        String.format(UpdateContentTemplate.CONTENT_QUANTITY_ADDON, updateContents.size() + 1, oldBillItem.getItemName(),
                            oldSubAddon.getQuantity().toString(), newBillAddon.getQuantity().toString()));
                    //Thay đổi giá
                    updateContents.add(String.format(UpdateContentTemplate.CONTENT_PRICE_ADDON, updateContents.size() + 1,
                        oldBillItem.getItemName(),
                        couponService.convertFormatAmount(oldBillItem.getAmountAfterTax()),
                        couponService.convertFormatAmount(newBillAddon.getAmountAfterTax())));
                }
            });
        });

        //Nếu có addon thêm mới hoặc xóa addon
        Set<Addon> addonsAdd = addonRepository.findAllByIdIn(addonIdsAdd);
        Set<Addon> addonsRemove = addonRepository.findAllByIdIn(addonIdsRemove);
        //Thêm mới addon
        if (!addonsAdd.isEmpty()) {
            addonsAdd.forEach(a -> {
                updateContents.add(String.format(UpdateContentTemplate.CONTENT_NAME_ADDON_ADDED, updateContents.size() + 1, a.getName()));
            });
        }
        //Xóa addon
        if (!addonsRemove.isEmpty()) {
            addonsRemove.forEach(a -> {
                updateContents.add(String.format(UpdateContentTemplate.CONTENT_NAME_ADDON_REMOVE, updateContents.size() + 1, a.getName()));
            });
        }

        response.put(MailParam.USER, actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName());
        paramsToAssignee.put(MailParam.USER, actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName());
        String linkDetail = String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, oldSubscription.getId());
        response.put(MailParam.LINK_USE_SUBS, linkDetail);
        if (!CollectionUtils.isEmpty(updateContents)) {
            response.put(MailParam.CONTENT_NAME_PRICING, convertListToStringContentMail(updateContents));
            paramsToAssignee.put(MailParam.CONTENT_NAME_PRICING, convertListToStringContentMail(updateContents));
        }

        response
            .put(MailParam.DATE_MODIFIED, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS));
        paramsToAssignee.put(MailParam.DATE_MODIFIED, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS));
        boolean isChangeNow = Objects.isNull(oldSubscription.getPricing().getUpdateSubscriptionDate()) ||
            Objects.equals(oldSubscription.getPricing().getUpdateSubscriptionDate(), ComboChangeEnum.NOW.value)
            || Objects.isNull(oldSubscription.getEndCurrentCycle());
        response.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(
            isChangeNow ? new Date() : DateUtil.toDate(DateUtil.toLocalDate(oldSubscription.getEndCurrentCycle()).plusDays(0)),
            DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        paramsToAssignee.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(
            isChangeNow ? new Date() : DateUtil.toDate(DateUtil.toLocalDate(oldSubscription.getEndCurrentCycle()).plusDays(0)),
            DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        response.put(MailParam.NAME_MODIFIED, actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()));
        paramsToAssignee.put(MailParam.NAME_MODIFIED, actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()));

        sendMailTo(response, emailCode, actor.getEmail());

        if (oldSubscription.getAssigneeId() != null) {
            userRepository.findUserById(oldSubscription.getAssigneeId())
                .ifPresent(assignee -> {
                    paramsToAssignee.put("$STAFF_NAME", assignee.getFullName());
                    actionNotificationService.send(new SCD03(assignee, oldSubscription, paramsToAssignee));
                });
        }
        if (Objects.nonNull(parentOfActor)) {
            response.put(MailParam.USER, parentOfActor.getLastName() + CharacterConstant.SPACE + parentOfActor.getFirstName());
            sendMailTo(response, emailCode, parentOfActor.getEmail());
        }
    }

    @Override
    public List<BillItem> getBillItemCalculate(BillPopUpCostIncurred calculateCostIncurred) {
        BillCostIncurred allBill = calculateCostIncurred.getAllBill();
        BillCostIncurred costIncurred = calculateCostIncurred.getCostIncurred();
        List<BillItem> result = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(allBill.getCostIncurred())) {
            List<BillItemDTO> billItemList = allBill.getCostIncurred();
            List<BillItemDTO> billItemCostIncurredList = costIncurred.getCostIncurred();
            AtomicLong billObjectId = new AtomicLong(0);

            billItemList.stream().filter(b -> Objects.isNull(b.getBillItemId()) && Objects.nonNull(b.getItemType()) &&
                !BillItemType.APPLY_FEE.equals(b.getItemType())).forEach(item -> {
                if (ChangeSubActionEnum.DELETE.equals(item.getActionChange())) {
                    billObjectId.set(item.getObjectId());
                }
                BigDecimal costIncurredAmount = BigDecimal.ZERO;
                if (!org.springframework.util.CollectionUtils.isEmpty(billItemCostIncurredList)) {
                    Optional<BillItemDTO> itemIncurred = billItemCostIncurredList.stream().filter(b ->
                        Objects.equals(b.getObjectId(), item.getObjectId()) && Objects.equals(b.getItemType(), item.getItemType())).findFirst();
                    if (itemIncurred.isPresent()) {
                        costIncurredAmount = itemIncurred.get().getFinalAmountAfterTax();
                    }
                }
                result.add(new BillItem(item.getBillItemId(), null, item.getItemName(), item.getPreAmountTax(),
                    item.getIntoAmountPreTax(), item.getFinalAmountPreTax(), item.getFinalAmountAfterTax(), item.getObjectId(),
                    Objects.nonNull(item.getItemType()) ? item.getItemType().getValue() : null,
                    item.getStartDate(), item.getEndDate(), Objects.equals(billObjectId.get(), item.getObjectId()) ?
                    ChangeSubActionEnum.DELETE.value
                    : Objects.nonNull(item.getActionChange()) ? item.getActionChange().value : ChangeSubActionEnum.UNSET.value,
                    item.getQuantity(), item.getMultiPlanId(), costIncurredAmount));
            });
        }
        return  result;
    }

    @Override
    public void sendMailSCB16(User actor, User parentOfActor, Subscription oldSubscription, Subscription newSubscription,
        List<SubscriptionComboAddon> listOldAddons, List<SubscriptionComboAddon> listNewAddons, List<SubscriptionSetupFee> oldFees,
        List<SubscriptionSetupFee> newFees, EmailCodeEnum emailCode) {
        if ((Objects.isNull(parentOfActor) || StringUtils.isEmpty(parentOfActor.getEmail())) && StringUtils.isEmpty(actor.getEmail())) {
            return;
        }
        Map<String, String> mapDefaultValue = new HashMap<>();
        String comboName = Objects.nonNull(oldSubscription.getComboPlanId()) ? oldSubscription.getComboPlan().getCombo().getComboName() : "null";
        String comboPlanName = Objects.nonNull(oldSubscription.getComboPlanId()) ? oldSubscription.getComboPlan().getComboName() : "null";
        String newComboPlanName = Objects.nonNull(oldSubscription.getComboPlanId()) ? newSubscription.getComboPlan().getComboName() : "null";

        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO_PRICING.getValue(), comboPlanName);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COMBO.getValue(), comboName);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.CODE_TRANSACTION.getValue(), newSubscription.getSubCode());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_MODIFIED.getValue(), actor.getFirstName() + actor.getLastName());
        mapDefaultValue
            .put(ParamEmailEnum.DATE_MODIFIED.getValue(), DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YY_HH_MM_SS));
        Integer changePlanConfig = Objects.nonNull(newSubscription.getComboPlan()) ? newSubscription.getComboPlan().getIsUpdateNow()
            : newSubscription.getPricing().getIsUpdateNow();
        mapDefaultValue
            .put(ParamEmailEnum.NAME_MODIFIED.getValue(), actor.getFirstName().concat(CharacterConstant.SPACE).concat(actor.getLastName()));
        mapDefaultValue.put(ParamEmailEnum.DATE_APPLY.getValue(), DateUtil.convertDateToString(
            Objects.equals(changePlanConfig, ComboChangeEnum.NOW.value) ? new Date() : newSubscription.getEndCurrentCycle(),
            DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(), actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName());
        String linkDetail = Objects.nonNull(newSubscription.getComboPlanId()) ?
            String.format(SubscriptionConstant.LINK_SME_SUBS_COMBO_DETAIL, webHost, newSubscription.getId())
            : String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, newSubscription.getId());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.LINK_USE_SUBS.getValue(), linkDetail);
        mapDefaultValue
            .putIfAbsent(ParamEmailEnum.NAME_COMBO_OLD.getValue(), !Objects.equals(comboName, newComboPlanName) ? comboPlanName : "null");
        mapDefaultValue
            .putIfAbsent(ParamEmailEnum.NAME_COMBO_NEW.getValue(), !Objects.equals(comboName, newComboPlanName) ? newComboPlanName : "null");

        Map<Long, SubscriptionComboAddon> mapNewAddons = listNewAddons.stream()
            .collect(Collectors.toMap(SubscriptionComboAddon::getAddonId, Function.identity(), (o1, o2) -> o1));

        Map<Long, SubscriptionComboAddon> mapOldAddons = listOldAddons.stream()
            .collect(Collectors.toMap(SubscriptionComboAddon::getAddonId, Function.identity(), (o1, o2) -> o1));

        List<AddonsResDTO> addonOlds = addonRepository.findListAddonDtoByIds(mapOldAddons.keySet()).stream().filter(Objects::nonNull).collect(
            Collectors.toList());
        List<AddonsResDTO> addonNews = addonRepository.findListAddonDtoByIds(mapNewAddons.keySet()).stream().filter(Objects::nonNull).collect(
            Collectors.toList());
        Map<Long, AddonsResDTO> addonMapNew = addonNews.stream()
            .collect(Collectors.toMap(AddonsResDTO::getId, Function.identity()));
        Map<Long, AddonsResDTO> addonMapOld = addonOlds.stream()
            .collect(Collectors.toMap(AddonsResDTO::getId, Function.identity()));
        // nếu thay đổi số lượng addon
        String tagParamNameAddonChangeQuantity = listOldAddons.stream().filter(
            oldAddon -> mapNewAddons.containsKey(oldAddon.getAddonId()) && !oldAddon.getQuantity()
                .equals(mapNewAddons.get(oldAddon.getAddonId()).getQuantity()))
            .map(oldAddon -> String
                .join(StringUtils.EMPTY, "<li>", Objects.nonNull(addonMapOld.get(oldAddon.getAddonId())) ?
                        addonMapOld.get(oldAddon.getAddonId()).getName() : "", ": Từ ", oldAddon.getQuantity().toString(),
                    " sang ", mapNewAddons.get(oldAddon.getAddonId()).getQuantity().toString(), "</li>")
            ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));

        // Danh sách các addon bị xóa
        List<String> listAddonNameDeleted = listOldAddons.stream().filter(oldAddon -> !mapNewAddons.containsKey(oldAddon.getAddonId()))
            .map(item -> addonMapOld.get(item.getAddonId()).getName())
            .collect(Collectors.toList());
        // Danh sách các addon thêm mới
        List<String> listAddonNameNewAdd = listNewAddons.stream().filter(newAddon -> !mapOldAddons.containsKey(newAddon.getAddonId()))
            .map(item -> addonMapNew.get(item.getAddonId()).getName())
            .collect(Collectors.toList());

        String tagParamNameAddonDeleted = listAddonNameDeleted.stream()
            .map(addonNameDeleted -> String.join(StringUtils.EMPTY, "<li>", addonNameDeleted, "</li>")
            ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));

        String tagParamNameAddonAdd = listAddonNameNewAdd.stream()
            .map(addonNameAdd -> String.join(StringUtils.EMPTY, "<li>", addonNameAdd, "</li>")
            ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));

//        Map<Long, SubscriptionSetupFee> mapSubscriptionSetupFeeOld = oldFees.stream()
//            .collect(Collectors.toMap(SubscriptionSetupFee::getAddonId, Function.identity(), (o1, o2) -> o1));
        Map<Long, SubscriptionSetupFee> mapSubscriptionSetupFeeNew = newFees.stream()
            .collect(Collectors.toMap(SubscriptionSetupFee::getAddonId, Function.identity(), (o1, o2) -> o1));

        String tagParamFeeChange = oldFees.stream().filter(
            oldFee -> mapSubscriptionSetupFeeNew.containsKey(oldFee.getAddonId()) && Objects
                .nonNull(mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()))
                && Objects.nonNull(mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice())
                && mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice()
                .equals(oldFee.getPrice()))
            .map(oldFee -> String.join(StringUtils.EMPTY, "<li>", Objects.nonNull(addonMapOld.get(oldFee.getAddonId())) ?
                    addonMapOld.get(oldFee.getAddonId()).getName() : "", ": Từ ",
                oldFee.getPrice().toString(),
                " sang ", mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice().toString(), "</li>")
            ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));
        Optional<String> tagParamComboPlanFeeChangeOpt = oldFees.stream().filter(
            oldFee -> mapSubscriptionSetupFeeNew.containsKey(oldFee.getComboPlanId())
                && Objects.nonNull(mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()))
                && Objects.nonNull(mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice())
                && mapSubscriptionSetupFeeNew.get(oldFee.getAddonId())
                .getPrice().equals(oldFee.getPrice()) )
            .map(oldFee -> mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice().toString()).findFirst();

        Optional<String> tagParamComboplanOldFeeOpt = oldFees.stream().map(oldFee -> oldFee.getPrice().toString()).findFirst();

        mapDefaultValue.put(ParamEmailEnum.PRICE_COMBO_OLD.getValue(),
            tagParamComboplanOldFeeOpt.isPresent() ? tagParamComboplanOldFeeOpt.get() : "null");
        mapDefaultValue.putIfAbsent(ParamEmailEnum.PRICE_COMBO_NEW.getValue(),
            tagParamComboPlanFeeChangeOpt.isPresent() ? tagParamComboPlanFeeChangeOpt.get() : "null");
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_DELETED.getValue(),
            CollectionUtils.isEmpty(listAddonNameDeleted) ? "null" : tagParamNameAddonDeleted);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_ADDED.getValue(),
            CollectionUtils.isEmpty(listAddonNameNewAdd) ? "null" : tagParamNameAddonAdd);
        mapDefaultValue
            .putIfAbsent(ParamEmailEnum.NAME_ADDON_AMOUNT.getValue(), StringUtils.isEmpty(tagParamFeeChange) ? "null" : tagParamFeeChange);
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_QUANTITY.getValue(),
            StringUtils.isEmpty(tagParamNameAddonChangeQuantity) ? "null" : tagParamNameAddonChangeQuantity);
        Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService
            .replaceParamEmailTemplate(emailCode.getValue(), mapDefaultValue, null);
        if (emailTemplateOpt.isPresent() && StringUtils.isNotEmpty(actor.getEmail())) {
            AtomicInteger index = new AtomicInteger(1);
            String contentHtmlResult = Arrays.asList(emailTemplateOpt.get().getContent().split(System.lineSeparator())).stream()
                .filter(item -> !item.contains("null")).map(item -> {
                    if (item.matches(".*>\\d.*") && item.trim().endsWith("</p>")) {
                        return item.replaceFirst(">\\d", ">" + index.getAndIncrement());
                    }
                    return item;
                }).collect(Collectors.joining(System.lineSeparator()));
            emailTemplateOpt.get().setContent(contentHtmlResult);
            emailService.save(actor.getEmail(), emailTemplateOpt.get());
        }
        if (Objects.nonNull(parentOfActor) && StringUtils.isNotEmpty(parentOfActor.getEmail())) {
            mapDefaultValue
                .put(ParamEmailEnum.USER.getValue(), parentOfActor.getLastName() + CharacterConstant.SPACE + parentOfActor.getFirstName());
            emailService.save(parentOfActor.getEmail(), emailTemplateOpt.get());
        }
    }

    /**
     * Gửi mail khi sub sắp đến kỳ gia hạn
     */
    @Override
    public void sendMailWhenWarningSubscriptionIsComingRenewal(String actorEmail, String parentActorEmail, Long subscriptionId,
        String serviceName,
        String pricingName, Date paymentCycleDate, String companyName, String userFullName, String parentFullName,
        Integer numbersOfDateRemaining) {

        Map<String, String> response = new HashMap<>();
        EmailCodeEnum emailCode;
        boolean isSubPricing = Objects.nonNull(pricingName) && Objects.nonNull(serviceName);
        if (isSubPricing) {
            response.put(MailParam.NAME_SERVICE, serviceName);
            response.put(MailParam.NAME_PRICING, pricingName);
            emailCode = EmailCodeEnum.SC43;
        } else {
            response.put(MailParam.NAME_COMBO, serviceName);
            response.put(MailParam.NAME_COMBO_PRICING, pricingName);
            emailCode = EmailCodeEnum.SCB43;
        }

        response.put(MailParam.NAME_COMPANY, companyName);
        response.put(MailParam.REMAIN_DAYS, numbersOfDateRemaining.toString());
        response.put(MailParam.USER, userFullName);

        String linkDetail = String.format(SubscriptionConstant.LINK_SME_EXTEND_SERVICE, webHost, subscriptionId);

        response.put(MailParam.LINK_RENEWING, linkDetail);
        sendMailTo(response, emailCode, actorEmail);
        if (Objects.nonNull(parentActorEmail)) {
            response.put(MailParam.USER, parentFullName);
            sendMailTo(response, emailCode, actorEmail);
        }
    }

    /**
     * Lấy ra full name của user
     */
    private String getUserFullName(User user) {
        return user.getLastName() + CharacterConstant.SPACE + user.getFirstName();
    }

    /**
     * Gửi mail khi sub đổi gói thành công
     */
    @Override
    public void sendMailSC20(User actor, User parentOfActor, Subscription oldSub,
        Subscription newSub, EmailCodeEnum emailCode) {
        Map<String, String> response = new HashMap<>();
        if (Objects.nonNull(oldSub.getPricingId())) {
            String serviceName = oldSub.getPricing().getServiceEntity().getServiceName();
            String oldPricingName = oldSub.getPricing().getPricingName();
            String newPricingName = newSub.getPricing().getPricingName();

            String lastName = Objects.nonNull(actor.getLastName()) ? actor.getLastName() : "";
            String firstName = Objects.nonNull(actor.getFirstName()) ? actor.getFirstName() : "";
            String fullName = lastName.concat(CharacterConstant.SPACE).concat(firstName);
            String transactionCode = ObjectUtil.getOrDefault(oldSub.getSubCodeDHSXKD(), newPricingName);
            response.put(MailParam.USER, fullName);
            response.put(MailParam.NAME_SERVICE, serviceName);
            response.put(MailParam.NAME_PRICING_NEW, newPricingName);
            response.put(MailParam.NAME_PRICING_OLD, oldPricingName);
            response.put(MailParam.CODE_TRANSACTION, transactionCode);
            response.put(MailParam.NAME_CHANGED_PRICING, fullName);
            response.put(MailParam.DATE_CHANGED_PRICING, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            String linkUseSub = String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, newSub.getId());
            response.put(MailParam.LINK_DETAIL, linkUseSub);

            Date dateApply = new Date();
            if (Objects.equals(ComboChangeEnum.END_OF_PERIOD.value, oldSub.getPricing().getChangePricingDate())){
                dateApply = DateUtil.toDate(DateUtil.toLocalDate(oldSub.getEndCurrentCycle()).plusDays(1L));
            }
            response.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(dateApply, DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));

            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCode);
            emailService.sendMail(actor.getEmail(), emailCode, mailParam);
            if (Objects.nonNull(parentOfActor)) {
                lastName = Objects.nonNull(parentOfActor.getLastName()) ? parentOfActor.getLastName() : "";
                firstName = Objects.nonNull(parentOfActor.getFirstName()) ? parentOfActor.getFirstName() : "";
                fullName = lastName.concat(CharacterConstant.SPACE).concat(firstName);
                response.put(MailParam.USER, fullName);
                linkUseSub = String.format(subscriptionDetailService.getLinkSubDetailSME(parentOfActor.getId()), webHost, newSub.getId());
                response.put(MailParam.LINK_DETAIL, linkUseSub);
                mailParam = getListPramMailDTO(response, emailCode);
                emailService.sendMail(parentOfActor.getEmail(), emailCode, mailParam);
            }
        } else {
            throw exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID, "null");
        }
    }

    /**
     * Gửi mail khi sub đổi gói thành công cuối chu kỳ
     */
    @Override
    public void sendMailSC20EndCycle(User actor, User parentOfActor, Subscription oldSub,
        ChangeSubscription newSub, Pricing newPricing, Pricing oldPricing, EmailCodeEnum emailCode) {
        Map<String, String> response = new HashMap<>();
        if (Objects.nonNull(oldSub.getPricingId())) {
            String serviceName = oldSub.getPricing().getServiceEntity().getServiceName();
            String oldPricingName = oldSub.getPricing().getPricingName();
            String newPricingName = newPricing.getPricingName();

            String lastName = Objects.nonNull(actor.getLastName()) ? actor.getLastName() : "";
            String firstName = Objects.nonNull(actor.getFirstName()) ? actor.getFirstName() : "";
            String fullName = lastName.concat(CharacterConstant.SPACE).concat(firstName);
            String transactionCode = ObjectUtil.getOrDefault(oldSub.getSubCodeDHSXKD(), newPricingName);
            response.put(MailParam.USER, fullName);
            response.put(MailParam.NAME_SERVICE, serviceName);
            response.put(MailParam.NAME_PRICING_NEW, newPricingName);
            response.put(MailParam.NAME_PRICING_OLD, oldPricingName);
            response.put(MailParam.CODE_TRANSACTION, transactionCode);
            response.put(MailParam.NAME_CHANGED_PRICING, fullName);
            response.put(MailParam.DATE_CHANGED_PRICING, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            String linkUseSub = String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, newSub.getId());
            response.put(MailParam.LINK_DETAIL, linkUseSub);

            Date dateApply = new Date();
            if (Objects.equals(ComboChangeEnum.END_OF_PERIOD.value, oldPricing.getChangePricingDate())) {
                dateApply = DateUtil.toDate(DateUtil.toLocalDate(oldSub.getEndCurrentCycle()).plusDays(1L));
            }
            response.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(dateApply, DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));

            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCode);
            emailService.sendMail(actor.getEmail(), emailCode, mailParam);
            if (Objects.nonNull(parentOfActor)) {
                lastName = Objects.nonNull(parentOfActor.getLastName()) ? parentOfActor.getLastName() : "";
                firstName = Objects.nonNull(parentOfActor.getFirstName()) ? parentOfActor.getFirstName() : "";
                fullName = lastName.concat(CharacterConstant.SPACE).concat(firstName);
                response.put(MailParam.USER, fullName);
                linkUseSub = String.format(subscriptionDetailService.getLinkSubDetailSME(parentOfActor.getId()), webHost, newSub.getId());
                response.put(MailParam.LINK_DETAIL, linkUseSub);
                mailParam = getListPramMailDTO(response, emailCode);
                emailService.sendMail(parentOfActor.getEmail(), emailCode, mailParam);
            }
        } else {
            throw exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID, "null");
        }
    }

    @Override
    public void sendMailSCB20(User actor, User parentOfActor, Subscription oldSub, Subscription newSub,
        List<SubscriptionComboAddon> listOldAddons, List<SubscriptionComboAddon> listNewAddons, List<SubscriptionSetupFee> oldFees,
        List<SubscriptionSetupFee> newFees, EmailCodeEnum emailCode) {
        if ((Objects.isNull(parentOfActor) || StringUtils.isEmpty(parentOfActor.getEmail())) && StringUtils.isEmpty(actor.getEmail())) {
            return;
        }
        Map<String, String> mapDefaultValue = new HashMap<>();
        if (Objects.nonNull(oldSub.getComboPlanId())) {
            String comboName = oldSub.getComboPlan().getCombo().getComboName();
            String oldComboPlanName = oldSub.getComboPlan().getComboName();
            String newComboPlanName = newSub.getComboPlan().getComboName();

            String lastName = Objects.nonNull(actor.getLastName()) ? actor.getLastName() : "";
            String firstName = Objects.nonNull(actor.getFirstName()) ? actor.getFirstName() : "";
            String fullName = lastName.concat(CharacterConstant.SPACE).concat(firstName);
            mapDefaultValue.put(ParamEmailEnum.USER.getValue(), fullName);
            mapDefaultValue.put(ParamEmailEnum.NAME_COMBO.getValue(), comboName);
            mapDefaultValue.put(ParamEmailEnum.NAME_COMBO_OLD.getValue(), oldComboPlanName);
            mapDefaultValue.put(ParamEmailEnum.NAME_COMBO_NEW.getValue(), newComboPlanName);
            mapDefaultValue.put(MailParam.CODE_TRANSACTION, newSub.getSubCode());
            mapDefaultValue.put(MailParam.NAME_CHANGED_PRICING, fullName);
            mapDefaultValue.put(MailParam.DATE_CHANGED_PRICING, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            Integer changePlanConfig = Objects.nonNull(newSub.getComboPlan()) ? newSub.getComboPlan().getIsChangeNow()
                : newSub.getPricing().getIsChangeNow();
            mapDefaultValue.put(MailParam.DATE_APPLY, DateUtil.convertDateToString(Objects.equals(changePlanConfig, ComboChangeEnum.NOW.value)
                ? new Date() : newSub.getEndCurrentCycle(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
            mapDefaultValue.put(ParamEmailEnum.PRICE_COMBO_OLD.getValue(),
                oldSub.getComboPlan().getPrice().equals(newSub.getComboPlan().getPrice()) ? "null"
                    : oldSub.getComboPlan().getPrice().toString());
            mapDefaultValue.put(ParamEmailEnum.PRICE_COMBO_NEW.getValue(),
                oldSub.getComboPlan().getPrice().equals(newSub.getComboPlan().getPrice()) ? "null"
                    : newSub.getComboPlan().getPrice().toString());

            Map<Long, SubscriptionComboAddon> mapNewAddons = listNewAddons.stream()
                .collect(Collectors.toMap(SubscriptionComboAddon::getAddonId, Function.identity(), (o1, o2) -> o1));

            Map<Long, SubscriptionComboAddon> mapOldAddons = listOldAddons.stream()
                .collect(Collectors.toMap(SubscriptionComboAddon::getAddonId, Function.identity(), (o1, o2) -> o1));

            List<AddonsResDTO> addonOlds = addonRepository.findListAddonDtoByIds(mapNewAddons.keySet());
            List<AddonsResDTO> addonNews = addonRepository.findListAddonDtoByIds(mapNewAddons.keySet());
            Map<Long, AddonsResDTO> addonMapNew = addonOlds.stream()
                .collect(Collectors.toMap(AddonsResDTO::getAddonsId, Function.identity(), (o1, o2) -> o1));
            Map<Long, AddonsResDTO> addonMapOld = addonNews.stream()
                .collect(Collectors.toMap(AddonsResDTO::getAddonsId, Function.identity(), (o1, o2) -> o1));
            // nếu thay đổi số lượng addon
            String tagParamNameAddonChangeQuantity = listOldAddons.stream().filter(
                oldAddon -> mapNewAddons.containsKey(oldAddon.getAddonId()) && !oldAddon.getQuantity()
                    .equals(mapNewAddons.get(oldAddon.getAddonId()).getQuantity()))
                .map(oldAddon -> String.join(StringUtils.EMPTY, "<li>", addonMapOld.get(oldAddon.getAddonId()).getName(), ": Từ ",
                    oldAddon.getQuantity().toString(),
                    " sang ", mapNewAddons.get(oldAddon.getAddonId()).getQuantity().toString(), "</li>")
                ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));

            // Danh sách các addon bị xóa
            List<String> listAddonNameDeleted = listOldAddons.stream().filter(oldAddon -> !mapNewAddons.containsKey(oldAddon.getAddonId()))
                .map(item -> addonMapOld.get(item.getAddonId()).getName())
                .collect(Collectors.toList());
            // Danh sách các addon thêm mới
            List<String> listAddonNameNewAdd = listNewAddons.stream().filter(newAddon -> !mapOldAddons.containsKey(newAddon.getAddonId()))
                .map(item -> addonMapNew.get(item.getAddonId()).getName())
                .collect(Collectors.toList());

            String tagParamNameAddonDeleted = listAddonNameDeleted.stream()
                .map(addonNameDeleted -> String.join(StringUtils.EMPTY, "<li>", addonNameDeleted, "</li>")
                ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));

            String tagParamNameAddonAdd = listAddonNameNewAdd.stream()
                .map(addonNameAdd -> String.join(StringUtils.EMPTY, "<li>", addonNameAdd, "</li>")
                ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));
            Map<Long, SubscriptionSetupFee> mapSubscriptionSetupFeeNew = newFees.stream()
                .collect(Collectors.toMap(SubscriptionSetupFee::getAddonId, Function.identity(), (o1, o2) -> o1));

            String tagParamFeeChange = oldFees.stream().filter(
                oldFee -> mapSubscriptionSetupFeeNew.containsKey(oldFee.getAddonId()) && mapSubscriptionSetupFeeNew.get(oldFee.getAddonId())
                    .getPrice().equals(oldFee.getPrice()))
                .map(oldFee -> String.join(StringUtils.EMPTY, "<li>", addonMapOld.get(oldFee.getAddonId()).getName(), ": Từ ",
                    oldFee.getPrice().toString(),
                    " sang ", mapSubscriptionSetupFeeNew.get(oldFee.getAddonId()).getPrice().toString(), "</li>")
                ).collect(Collectors.joining(System.lineSeparator(), "<ul>", "</ul>"));
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_DELETED.getValue(),
                CollectionUtils.isEmpty(listAddonNameDeleted) ? "null" : tagParamNameAddonDeleted);
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_ADDED.getValue(),
                CollectionUtils.isEmpty(listAddonNameNewAdd) ? "null" : tagParamNameAddonAdd);
            mapDefaultValue
                .putIfAbsent(ParamEmailEnum.NAME_ADDON_AMOUNT.getValue(), StringUtils.isEmpty(tagParamFeeChange) ? "null" : tagParamFeeChange);
            mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_ADDON_QUANTITY.getValue(),
                StringUtils.isEmpty(tagParamNameAddonChangeQuantity) ? "null" : tagParamNameAddonChangeQuantity);
            Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService
                .replaceParamEmailTemplate(emailCode.getValue(), mapDefaultValue, null);
            if (emailTemplateOpt.isPresent() && StringUtils.isNotEmpty(actor.getEmail())) {
                AtomicInteger index = new AtomicInteger(1);
                String contentHtmlResult = Arrays.asList(emailTemplateOpt.get().getContent().split(System.lineSeparator())).stream()
                    .filter(item -> !item.contains("null")).map(item -> {
                        if (item.matches(".*>\\d.*") && item.trim().endsWith("</p>")) {
                            return item.replaceFirst(">\\d", ">" + index.getAndIncrement());
                        }
                        return item;
                    }).collect(Collectors.joining(System.lineSeparator()));
                emailTemplateOpt.get().setContent(contentHtmlResult);
                emailService.save(actor.getEmail(), emailTemplateOpt.get());
            }
            if (Objects.nonNull(parentOfActor) && StringUtils.isNotEmpty(parentOfActor.getEmail())) {
                mapDefaultValue
                    .put(ParamEmailEnum.USER.getValue(), parentOfActor.getLastName() + CharacterConstant.SPACE + parentOfActor.getFirstName());
                emailService.save(parentOfActor.getEmail(), emailTemplateOpt.get());
            }
        } else {
            throw exceptionFactory.resourceNotFound(Resources.COMBO_PLAN, ErrorKey.ID, "null");
        }
    }

    /**
     * Convert danh sách nội dung chỉnh sửa thành chuỗi nội dung mail
     */
    private String convertListToStringContentMail(List<String> updateContents) {
        if (!Collections.isEmpty(updateContents)) {
            StringBuilder stringBuilder = new StringBuilder();
            updateContents.forEach((value) -> {
                stringBuilder.append(value).append("<br>");
            });
            return stringBuilder.toString();
        }
        return null;
    }

    /**
     * lấy ra ngày chênh lệch còn lại
     */
    private Integer getRemainDateSendMail(Date date) {
        return Math.abs(Period.between(DateUtil.toLocalDate(date), LocalDate.now()).getDays());
    }

    /**
     * gửi mail với danh sách param truyền vào
     */
    private void sendMailTo(Map<String, String> valuePrams, EmailCodeEnum emailCode, String to) {
        List<MailParamResDTO> mailParam = getListPramMailDTO(valuePrams, emailCode);
        emailService.sendMail(to, emailCode, mailParam);
    }

    /**
     * gửi mail khi sub hủy thuê bao thành công SC-28
     */
    @Override
    public void sendMailCancelSubscription(User actor, User parentOfActor, Subscription subscription, EmailCodeEnum emailCode) {
        String objectName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getCombo().getComboName()
            : subscription.getPricing().getServiceEntity().getServiceName();
        String objectPlanName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getComboName()
            : subscription.getPricing().getPricingName();
        String userName = actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName();
        String transactionCode = ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(),objectPlanName);
        String canceledName = actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName());
        DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String cancelDate = df.format(new Date());
        CancelTimeTypeEnum cancelTime =
            Objects.nonNull(subscription.getComboPlanId()) ? CancelTimeTypeEnum.valueOf(subscription.getComboPlan().getCancelDate())
                : CancelTimeTypeEnum.valueOf(subscription.getPricing().getCancelDate());
        String cancelDateApply = Objects.equals(cancelTime, CancelTimeTypeEnum.RIGHT_NOW) ? cancelDate :
            Objects.nonNull(subscription.getEndCurrentCycle()) ? df.format(subscription.getEndCurrentCycle()) : "";
        String linkDetail = Objects.nonNull(subscription.getComboPlanId()) ?
            String.format(SubscriptionConstant.LINK_SME_SUBS_COMBO_DETAIL, webHost, subscription.getId())
            : String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, subscription.getId());

        String linkReActive = Objects.nonNull(subscription.getComboPlanId()) ?
            String.format(SubscriptionConstant.LINK_SME_RE_ACTIVE_COMBO, webHost, subscription.getId(),
                CharacterConstant.QUESTION_MARK + SubscriptionConstant.MODAL_RE_ACTIVE)
            : emailService.getLinkReActive(subscription.getId(), PortalType.SME);

        Map<String, String> response = new HashMap<>();
        response.put(MailParam.NAME_SERVICE, objectName);
        response.put(MailParam.NAME_PRICING, objectPlanName);
        response.put(MailParam.NAME_COMBO, objectName);
        response.put(MailParam.NAME_COMBO_PRICING, objectPlanName);
        response.put(MailParam.USER, userName);
        response.put(MailParam.CODE_TRANSACTION, transactionCode);
        response.put(MailParam.NAME_CANCELED, canceledName);
        response.put(MailParam.DATE_CANCELED, cancelDate);
        response.put(MailParam.DATE_APPLY_CANCELED, cancelDateApply);
        response.put(MailParam.LINK_DETAIL, linkDetail);
        response.put(MailParam.LINK_RE_ACTIVE, linkReActive);
        sendMailTo(response, emailCode, actor.getEmail());
        if (Objects.nonNull(parentOfActor)) {
            response.put(MailParam.USER, getUserFullName(parentOfActor));
            linkDetail = Objects.nonNull(subscription.getComboPlanId()) ?
                    String.format(SubscriptionConstant.LINK_SME_SUBS_COMBO_DETAIL, webHost, subscription.getId())
                    : String.format(subscriptionDetailService.getLinkSubDetailSME(parentOfActor.getId()), webHost, subscription.getId());
            response.put(MailParam.LINK_DETAIL, linkDetail);
            sendMailTo(response, emailCode, parentOfActor.getEmail());
        }
    }

    @Override
    public void sendMailCancelSubscriptionNew(User actor, User parentOfActor, User devOrAdmin, Subscription subscription, EmailCodeEnum emailCode, Boolean isPersonal, PortalType portalType, int typeLink) {
        String objectName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getCombo().getComboName()
            : subscription.getPricing().getServiceEntity().getServiceName();
        String objectPlanName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getComboName()
            : subscription.getPricing().getPricingName();
        boolean isSME = isPersonal || (!Objects.equals(typeLink, DEV) && !Objects.equals(typeLink, ADMIN));
        String userName = isSME && Objects.nonNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName() : actor.getName();
        String transactionCode = ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(), objectPlanName);
        String canceledName = isSME && Objects.nonNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()) : actor.getName();
        DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String cancelDate = df.format(new Date());
        CancelTimeTypeEnum cancelTime =
            Objects.nonNull(subscription.getComboPlanId()) ? CancelTimeTypeEnum.valueOf(subscription.getComboPlan().getCancelDate())
                : CancelTimeTypeEnum.valueOf(subscription.getPricing().getCancelDate());
        String cancelDateApply = !Objects.equals(subscription.getAwaitingCancel(), CancelTimeTypeEnum.FINISHED_CYCLE.value) ? cancelDate :
            Objects.nonNull(subscription.getEndCurrentCycle()) ? df.format(subscription.getEndCurrentCycle()) : "";

        String typeLinkDetail = subscriptionDetailService.getLinkSubDetailSME(actor.getId());
        String linkReActive = emailService.getLinkReActive(subscription.getId(), PortalType.valueOf(typeLink));
        if (typeLink == DEV) {
            typeLinkDetail = SubscriptionConstant.LINK_DEV_SUBS_SERVICE_DETAIL;
        } else if (typeLink == ADMIN) {
            typeLinkDetail = SubscriptionConstant.LINK_ADMIN_SUBS_SERVICE_DETAIL;
        }

        String linkDetail = String.format(typeLinkDetail, webHost, subscription.getId());

        User userUseService = userRepository.findUserById(subscription.getUserId()).get();
        User receiverDev = userRepository.getProviderByService(subscription.getId());
        String sourceCancel;
        String provider;
        String providerOrNot = CharacterConstant.BLANK;
        String smeName;
        switch (portalType) {
            case ADMIN:
                sourceCancel = MailParams.ADMIN;
                provider = devOrAdmin == null ? CharacterConstant.BLANK :
                        receiverDev.getName() == null ? CharacterConstant.BLANK : receiverDev.getName();
                providerOrNot = CharacterConstant.BLANK;
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case DEV:
                sourceCancel = devOrAdmin != null ? MailParams.PROVIDER : MailParams.SME_YOU;
                provider = providerOrNot = devOrAdmin == null ? CharacterConstant.BLANK :
                        receiverDev.getName() == null ? CharacterConstant.BLANK : receiverDev.getName();
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case SME:
                sourceCancel = isPersonal ? MailParams.PERSONAL : MailParams.SME;
                provider = smeName = isPersonal ? String.join(" ", Optional.ofNullable(actor.getLastName()).orElse(""),
                        Optional.ofNullable(actor.getFirstName()).orElse("")).trim() :
                        actor.getName() == null ? CharacterConstant.BLANK : actor.getName();
                break;
            default:
                sourceCancel = MailParams.ADMIN;
                provider = CharacterConstant.BLANK;
                smeName = CharacterConstant.BLANK;
        }

        Map<String, String> response = new HashMap<>();
        response.put(MailParam.NAME_SERVICE, objectName);
        response.put(MailParam.NAME_PRICING, objectPlanName);
        response.put(MailParam.NAME_COMBO, objectName);
        response.put(MailParam.NAME_COMBO_PRICING, objectPlanName);
        response.put(MailParam.USER, userName);
        response.put(MailParam.CODE_TRANSACTION, transactionCode);
        response.put(MailParam.NAME_CANCELED, canceledName);
        response.put(MailParam.DATE_CANCELED, cancelDate);
        response.put(MailParam.DATE_APPLY_CANCELED, cancelDateApply);
        response.put(MailParam.LINK_DETAIL, linkDetail);
        response.put(MailParam.PROVIDER, provider);
        response.put(MailParam.PROVIDER_OR_NOT, providerOrNot);
        response.put(MailParam.SME_NAME, smeName);
        response.put(MailParam.SOURCE_CANCELED, sourceCancel);
        response.put(MailParam.SME_DEV_OR_ADMIN, sourceCancel);
        response.put(MailParam.LINK_RE_ACTIVE, linkReActive);
        if (devOrAdmin == null) {
            sendMailTo(response, emailCode, actor.getEmail());
            if (Objects.nonNull(parentOfActor)) {
                typeLinkDetail = subscriptionDetailService.getLinkSubDetailSME(parentOfActor.getId());
                if (typeLink == DEV) {
                    typeLinkDetail = SubscriptionConstant.LINK_DEV_SUBS_SERVICE_DETAIL;
                } else if (typeLink == ADMIN) {
                    typeLinkDetail = SubscriptionConstant.LINK_ADMIN_SUBS_SERVICE_DETAIL;
                }

                linkDetail = String.format(typeLinkDetail, webHost, subscription.getId());
                response.put(MailParam.LINK_DETAIL, linkDetail);
                String actorName = isSME ? String.join(" ", Optional.ofNullable(devOrAdmin.getLastName()).orElse(""),
                        Optional.ofNullable(devOrAdmin.getFirstName()).orElse("")).trim() : devOrAdmin.getName();
                response.put(MailParam.USER, actorName);
                sendMailTo(response, emailCode, parentOfActor.getEmail());
            }
        } else {
            boolean isPersonalReceiver = userService.isPersonal(devOrAdmin);
            String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? String.join(" ", Optional.ofNullable(actor.getLastName()).orElse(""),
                    Optional.ofNullable(actor.getFirstName()).orElse("")).trim() : actor.getName();
            response.put(MailParam.USER, actorName);
            sendMailTo(response, emailCode, devOrAdmin.getEmail());
        }

    }

    @Override
    public void sendMailAllSmeAdmin(List<User> receiverAdmin, User receiverDev, Subscription subscription, EmailCodeEnum emailCodeEnum, boolean isPersonal, int typeLink) {
        List<MailSendParamDTO> param = new ArrayList<>();
        Map<String, String> response = new HashMap<>();

        // lấy thông tin user sử dụng dịch vụ
        User userUseService = userRepository.findUserById(subscription.getUserId()).get();
        String provider = userRepository.getProviderByServiceForEmail(subscription.getId());

        Pricing pricing = pricingRepository.findByIdAndDeletedFlag(subscription.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, subscriptionMessage, null);
            return new ResourceNotFoundException(messageNotFound, Resources.SUBSCRIPTION, ErrorKey.Pricing.ID,
                    MessageKeyConstant.NOT_FOUND);
        });
        ServiceEntity subPricingService = serviceRepository
                .findByIdAndDeletedFlag(pricing.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> {
                    String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, subscriptionMessage, null);
                    return new ResourceNotFoundException(messageNotFound, Resources.SUBSCRIPTION, ErrorKey.Pricing.SERVICE_ID,
                            MessageKeyConstant.NOT_FOUND);
                });
        String smeName = userUseService.getLastName() != null && userUseService.getFirstName() != null ? userUseService.getLastName().concat(CharacterConstant.SPACE).concat(userUseService.getFirstName())
                : userUseService.getName();
        String transactionCode = ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(), pricing.getPricingName());
        DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String cancelDate = df.format(new Date());
        User userCancel = null;
        String canceledName = CharacterConstant.BLANK;
        if (Objects.nonNull(subscription.getCanceledBy()) && !subscription.getCanceledBy().equals(BATCH)) {
            userCancel = userRepository.findFirstById(Long.parseLong(subscription.getCanceledBy()));
        }
        if (userCancel != null) {
            String lastName = userCancel.getLastName();
            lastName = (lastName == null ?  "" : lastName);
            String firstName = userCancel.getFirstName();
            firstName = (firstName == null ?  "" : firstName);
            canceledName = isPersonal || (!Objects.equals(typeLink, DEV) && !Objects.equals(typeLink, ADMIN)) ? lastName.concat(CharacterConstant.SPACE).concat(
                firstName) : userCancel.getName();
        }
        CancelTimeTypeEnum cancelTime =
            Objects.nonNull(subscription.getComboPlanId()) ? CancelTimeTypeEnum.valueOf(subscription.getComboPlan().getCancelDate())
                    : CancelTimeTypeEnum.valueOf(subscription.getPricing().getCancelDate());
        String cancelDateApply = Objects.equals(cancelTime, CancelTimeTypeEnum.RIGHT_NOW) ? cancelDate :
            Objects.nonNull(subscription.getEndCurrentCycle()) ? df.format(subscription.getEndCurrentCycle()) : "";

        Integer timeReactive = pricing.getActiveDate();
        String strTimeReactive = CharacterConstant.BLANK;
        if (timeReactive != null && timeReactive != -1L) {
            strTimeReactive = String.format(SubscriptionConstant.STR_TIME_EXTEND_OR_REACTIVE, timeReactive);
        }
        String linkReActive = emailService.getLinkReActive(subscription.getId(), PortalType.valueOf(typeLink));

        String typeLinkRenewing = SubscriptionConstant.LINK_SME_EXTEND_SERVICE;
        if (typeLink == DEV) {
            typeLinkRenewing = SubscriptionConstant.LINK_DEV_EXTEND_SERVICE;
        } else if (typeLink == ADMIN) {
            typeLinkRenewing = SubscriptionConstant.LINK_ADMIN_EXTEND_SERVICE;
        }

        String linkRenewing = String.format(typeLinkRenewing, webHost, subscription.getId());
        Subscription sub = subscriptionRepository.findById(subscription.getId()).get();
        Bills billCurrent = billsRepository.getBillOutOfDate(sub.getId());
        String note = CharacterConstant.BLANK;
        if (billCurrent != null) {
            note = "Thanh toán cho chu kỳ " + billCurrent.getCurrentCycle() + " (từ " + billCurrent.getBillingDate() + " đến " + billCurrent.getEndDate() + ")";
        }
        Department department = null;
        if (userUseService.getDepartmentId() != null) {
            department = departmentsRepository.findById(userUseService.getDepartmentId()).orElse(new Department());
        }
        String phoneProvince = department == null || department.getProvinceId() == null ?
                StringUtils.EMPTY : contactProvinceRepository.getPhoneInProvince(department.getProvinceId());
        response.put(SubscriptionConstant.MailParam.NAME_SERVICE, subPricingService.getServiceName());
        response.put(SubscriptionConstant.MailParam.NAME_PRICING, pricing.getPricingName());
//            response.put(SubscriptionConstant.MailParam.NAME_COMBO, objectName);
//            response.put(SubscriptionConstant.MailParam.NAME_COMBO_PRICING, objectPlanName);
        response.put(SubscriptionConstant.MailParam.CODE_TRANSACTION, transactionCode);
        response.put(SubscriptionConstant.MailParam.SME_NAME, smeName != null ? smeName : CharacterConstant.BLANK);
        response.put(SubscriptionConstant.MailParam.NOTE, note);
        response.put(SubscriptionConstant.MailParam.SUB_CODE, subscription.getSubCode());
        response.put(SubscriptionConstant.MailParam.AMOUNT,
            billCurrent == null ? CharacterConstant.BLANK : billCurrent.getTotalAmount().toString());
        response.put(SubscriptionConstant.MailParam.LINK_RE_ACTIVE, linkReActive);
        response.put(SubscriptionConstant.MailParam.LINK_RENEWING, linkRenewing);
        response.put(MailParam.TIME_EXTEND_OR_REACTIVE, strTimeReactive);
        response.put(SubscriptionConstant.MailParam.HOTLINE_TINH, phoneProvince);
        response.put(SubscriptionConstant.MailParam.NAME_CANCELED, canceledName);
        response.put(SubscriptionConstant.MailParam.DATE_CANCELED, cancelDate);
        response.put(SubscriptionConstant.MailParam.DATE_APPLY_CANCELED, cancelDateApply);
        if (typeLink == ADMIN) {
            response.put(SubscriptionConstant.MailParam.PROVIDER, provider);
            response.put(SubscriptionConstant.MailParam.IS_ADMIN, MailParams.ADMIN_OR_DEV);
        } else {
            response.put(SubscriptionConstant.MailParam.PROVIDER, CharacterConstant.BLANK);
            response.put(SubscriptionConstant.MailParam.IS_ADMIN, CharacterConstant.BLANK);
        }
        receiverAdmin.forEach(u -> {
            boolean isPersonalReceiver = userService.isPersonal(u);
            String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? String.join(" ", Optional.ofNullable(u.getLastName()).orElse(""),
                    Optional.ofNullable(u.getFirstName()).orElse("")).trim() : u.getName();
            response.put(SubscriptionConstant.MailParam.USER, actorName);
            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCodeEnum);
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(u.getEmail());
            mailSendParamDTO.setListMailParam(mailParam);
            param.add(mailSendParamDTO);
        });
        emailService.sendMultiMail( emailCodeEnum, param);
        if (receiverDev != null) {
            boolean isPersonalReceiver = userService.isPersonal(receiverDev);
            String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? String.join(" ", Optional.ofNullable(receiverDev.getLastName()).orElse(""),
                    Optional.ofNullable(receiverDev.getFirstName()).orElse("")).trim() : receiverDev.getName();
            linkReActive = String.format(SubscriptionConstant.LINK_DEV_RE_ACTIVE_SERVICE, webHost, subscription.getId(),
                CharacterConstant.QUESTION_MARK + SubscriptionConstant.MODAL_RE_ACTIVE);
            response.put(SubscriptionConstant.MailParam.USER, actorName);
            response.put(SubscriptionConstant.MailParam.LINK_RE_ACTIVE, linkReActive);
            sendMailTo(response, emailCodeEnum, receiverDev.getEmail());
        }
    }

    @Override
    public void sendMailReactive(User actor, User parentOfActor, User devOrAdmin, Subscription subscription, EmailCodeEnum emailCode, int typeUserLogin, Boolean isPersonal, String typeLink) {
        String objectName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getCombo().getComboName()
            : subscription.getPricing().getServiceEntity().getServiceName();
        String objectPlanName = Objects.nonNull(subscription.getComboPlanId()) ? subscription.getComboPlan().getComboName()
            : subscription.getPricing().getPricingName();
        String userName = isPersonal ? actor.getLastName() + CharacterConstant.SPACE + actor.getFirstName() : actor.getName();
        String transactionCode = ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(), objectPlanName);
        String nameReactive = isPersonal || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? actor.getFullName() : actor.getName();
        DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        String reactiveDate = df.format(Objects.nonNull(subscription.getReactiveDate()) ? subscription.getReactiveDate() : new Date());
        String dateStartReactive = df.format(subscription.getStartCurrentCycle());
        String dateEndReactive = df.format(subscription.getEndCurrentCycle());

        Subscription sub = subscriptionRepository.findById(subscription.getId()).get();
        Bills billCurrent = billsRepository.getFirstByOfCycle(sub.getId(), sub.getCurrentCycle());
        String typeLinkDetail = subscriptionDetailService.getLinkSubDetailSME(actor.getId());

        String linkReActive = emailService.getLinkReActive(subscription.getId(), PortalType.valueOf(typeLink));
        if (Objects.equals(typeLink, SubscriptionConstant.DEV_PORTAL)) {
            typeLinkDetail = SubscriptionConstant.LINK_DEV_SUBS_SERVICE_DETAIL;
        } else if (Objects.equals(typeLink, SubscriptionConstant.ADMIN_PORTAL)) {
            typeLinkDetail = SubscriptionConstant.LINK_ADMIN_SUBS_SERVICE_DETAIL;
        }
        String linkDetail = String.format(typeLinkDetail, webHost, subscription.getId());

        User userUseService = userRepository.findUserById(subscription.getUserId()).get();
        User receiverDev = userRepository.getProviderByService(subscription.getId());
        String sourceReactive = CharacterConstant.BLANK;
        String provider = CharacterConstant.BLANK;
        String providerOrNot = CharacterConstant.BLANK;
        String smeName = CharacterConstant.BLANK;

        switch (typeUserLogin) {
            case ADMIN:
                sourceReactive = MailParams.ADMIN;
                provider = devOrAdmin == null ? CharacterConstant.BLANK :
                        receiverDev.getName() == null ? CharacterConstant.BLANK : receiverDev.getName();
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case DEV:
                sourceReactive = devOrAdmin != null ? MailParams.PROVIDER : MailParams.SME_YOU;
                provider = providerOrNot = devOrAdmin == null ? CharacterConstant.BLANK :
                        receiverDev.getName() == null ? CharacterConstant.BLANK : receiverDev.getName();
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            case SME:
                sourceReactive = isPersonal ? MailParams.PERSONAL : MailParams.SME;
                provider = providerOrNot = isPersonal ? String.join(" ", Optional.ofNullable(receiverDev.getLastName()).orElse(""),
                        Optional.ofNullable(receiverDev.getFirstName()).orElse("")).trim() :
                        receiverDev.getName() == null ? CharacterConstant.BLANK : receiverDev.getName();
                smeName = isPersonal ? String.join(" ", Optional.ofNullable(userUseService.getLastName()).orElse(""),
                        Optional.ofNullable(userUseService.getFirstName()).orElse("")).trim() :
                        userUseService.getName() == null ? CharacterConstant.BLANK : userUseService.getName();
                break;
            default:
        }

        String numberOfCycle = (subscription.getNumberOfCycles() == null || subscription.getNumberOfCycles() == -1)
            ? SubscriptionConstant.MailParams.QUANTITY_UNLIMITED :
                subscription.getNumberOfCycles().toString();

        //chu kỳ của gói
        String cycle = CharacterConstant.BLANK;
        if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
            Optional<PricingMultiPlan> pricingMultiPlan = pricingMultiPlanRepository.findByIdAndDeletedFlag(subscription.getPricingMultiPlanId(),  DeletedFlag.NOT_YET_DELETED.getValue());
            if (pricingMultiPlan.isPresent()) {
                cycle = pricingMultiPlan.get().getPaymentCycle().toString() + CharacterConstant.SPACE + pricingService.getTitleCycleType(pricingMultiPlan.get().getCircleType());
            }
        } else {
            Optional<Pricing> pricing = pricingRepository.findByIdAndDeletedFlag(subscription.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
            if (pricing.isPresent()) {
                cycle = pricing.get().getPaymentCycle().toString() + CharacterConstant.SPACE + pricingService.getTitleCycleType(pricing.get().getCycleType());
            }
        }

        Map<String, String> response = new HashMap<>();
        response.put(MailParam.NAME_SERVICE, objectName);
        response.put(MailParam.NAME_PRICING, objectPlanName);
        response.put(MailParam.NAME_COMBO, objectName);
        response.put(MailParam.NAME_COMBO_PRICING, objectPlanName);
        response.put(MailParam.USER, userName);
        response.put(MailParam.SME_DEV_OR_ADMIN, sourceReactive);
        response.put(MailParam.CODE_TRANSACTION, transactionCode);
        response.put(MailParam.NAME_REACTIVE, nameReactive);
        response.put(MailParam.DATE_REACTIVE, reactiveDate);
        response.put(MailParam.DATE_START_REACTIVE, dateStartReactive);
        response.put(MailParam.DATE_END_REACTIVE, dateEndReactive);
        response.put(MailParam.SOURCE_REACTIVE, sourceReactive);
        response.put(MailParam.NUMBER_OF_CYCLE, numberOfCycle);
        response.put(MailParam.CURRENT_CYCLE, cycle);
        response.put(MailParam.AMOUNT, billCurrent == null ? CharacterConstant.BLANK : billCurrent.getTotalAmount().toString());
        response.put(MailParam.PROVIDER, provider);
        response.put(MailParam.PROVIDER_OR_NOT, providerOrNot);
        response.put(MailParam.SME_NAME, smeName);
        response.put(MailParam.LINK_RE_ACTIVE, linkReActive);
        response.put(MailParam.LINK_DETAIL, linkDetail);
        if (devOrAdmin == null) {
            sendMailTo(response, emailCode, actor.getEmail());
            if (Objects.nonNull(parentOfActor)) {
                typeLinkDetail = subscriptionDetailService.getLinkSubDetailSME(parentOfActor.getId());
                if (typeLink == SubscriptionConstant.DEV_PORTAL) {
                    typeLinkDetail = SubscriptionConstant.LINK_DEV_SUBS_SERVICE_DETAIL;
                } else if (typeLink == SubscriptionConstant.ADMIN_PORTAL) {
                    typeLinkDetail = SubscriptionConstant.LINK_ADMIN_SUBS_SERVICE_DETAIL;
                }
                linkDetail = String.format(typeLinkDetail, webHost, subscription.getId());
                response.put(MailParam.LINK_DETAIL, linkDetail);
                boolean isPersonalReceiver = userService.isPersonal(parentOfActor);
                String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? String.join(" ", Optional.ofNullable(parentOfActor.getLastName()).orElse(""),
                        Optional.ofNullable(parentOfActor.getFirstName()).orElse("")).trim() : parentOfActor.getName();
                response.put(MailParam.USER, actorName);
                sendMailTo(response, emailCode, parentOfActor.getEmail());
            }
        } else {
            boolean isPersonalReceiver = userService.isPersonal(devOrAdmin);
            String actorName = isPersonalReceiver || !Objects.equals(typeLink, DEV) || !Objects.equals(typeLink, ADMIN) ? String.join(" ", Optional.ofNullable(devOrAdmin.getLastName()).orElse(""),
                    Optional.ofNullable(devOrAdmin.getFirstName()).orElse("")).trim() : devOrAdmin.getName();
            response.put(MailParam.USER, actorName);
            sendMailTo(response, emailCode, devOrAdmin.getEmail());
        }

    }


    /**
     * gui mail khi gia han subscription
     */
    @Override
    public void sendMailRenewSubscription(Subscription subscription, User actor, User smeAdmin, String serviceName, String pricingName,
        Integer paymentCycle, Integer cycleType, PortalType portalType, EmailCodeEnum emailCodeEnum) {
        Map<String, String> response = new HashMap<>();
        List<String> roles = userRepository.getRoleDisplayName(actor.getId());
        String role = String.join(", ", roles);
        response.put(MailParam.USER, Objects.nonNull(actor.getLastName()) && Objects.isNull(actor.getFirstName()) ? actor.getLastName() :
            Objects.isNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getFirstName() :
            Objects.nonNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()) :
            CharacterConstant.SPACE);
        response.put(MailParam.NAME_SERVICE, serviceName);
        response.put(MailParam.NAME_PRICING, pricingName);
        response.put(MailParam.NAME_COMBO, serviceName);
        response.put(MailParam.NAME_COMBO_PRICING, pricingName);
        response.put(MailParam.CODE_TRANSACTION, ObjectUtil.getOrDefault(subscription.getSubCodeDHSXKD(), pricingName));
        response.put(MailParam.NAME_EXTEND, Objects.nonNull(actor.getLastName()) && Objects.isNull(actor.getFirstName()) ? actor.getLastName() :
            Objects.isNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getFirstName() :
            Objects.nonNull(actor.getLastName()) && Objects.nonNull(actor.getFirstName()) ? actor.getLastName().concat(CharacterConstant.SPACE).concat(actor.getFirstName()) :
            CharacterConstant.SPACE);
        response.put(MailParam.DATE_EXTEND, DateUtil.convertDateToString(new Date(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        response.put(MailParams.NUM_OF_CYCLE, subscription.getNumberOfCycles() == null || subscription.getNumberOfCycles() == -1 ?
            SubscriptionConstant.MailParams.QUANTITY_UNLIMITED : subscription.getNumberOfCycles().toString());
        response.put(MailParams.CYCLE, paymentCycle.toString().concat(CharacterConstant.SPACE)
            .concat(reportService.transTimeEnumToString(TimeTypeEnum.valueOf(cycleType))));
        response.put(MailParam.ROLE_EXTEND, role);
        response.put(MailParams.DATE_END, DateUtil.convertDateToString(subscription.getExpiredTime(), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH));
        String linkUseSub = Objects.nonNull(subscription.getComboPlanId()) ?
            String.format(SubscriptionConstant.LINK_SME_SUBS_COMBO_DETAIL, webHost, subscription.getId())
            : String.format(subscriptionDetailService.getLinkSubDetailSME(actor.getId()), webHost, subscription.getId());
        response.put(MailParam.LINK_USE_SUBS, linkUseSub);

        List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCodeEnum);
        emailService.sendMail(actor.getEmail(), emailCodeEnum, mailParam);

        if (Objects.nonNull(smeAdmin)) {
            linkUseSub = Objects.nonNull(subscription.getComboPlanId()) ?
                    String.format(SubscriptionConstant.LINK_SME_SUBS_COMBO_DETAIL, webHost, subscription.getId())
                    : String.format(subscriptionDetailService.getLinkSubDetailSME(smeAdmin.getId()), webHost, subscription.getId());
            response.put(MailParam.LINK_USE_SUBS, linkUseSub);
            response.put(MailParam.USER, smeAdmin.getLastName() + CharacterConstant.SPACE + smeAdmin.getFirstName());
            mailParam = getListPramMailDTO(response, emailCodeEnum);
            emailService.sendMail(smeAdmin.getEmail(), emailCodeEnum, mailParam);
        }

    }

    /**
     * Gửi mail khi sub sắp đến kì thanh toán
     */
    @Override
    public void sendMailWhenSubscriptionIsComingPeriod(Long actorId, Long parentActorId, String actorEmail, String parentActorEmail, Long subscriptionId, String serviceName,
        String pricingName, Date paymentCycleDate, String companyName, String userFullName, String parentFullName, Date finalPaymentTerm) {
        Map<String, String> response = new HashMap<>();
        EmailCodeEnum emailCode;
        boolean isSubPricing = Objects.nonNull(pricingName) && Objects.nonNull(serviceName);
        String linkDetail = String.format(subscriptionDetailService.getLinkSubDetailSME(actorId), webHost, subscriptionId);
        if (isSubPricing) {
            response.put(MailParam.NAME_SERVICE, serviceName);
            response.put(MailParam.NAME_PRICING, pricingName);
            emailCode = EmailCodeEnum.SC46;
            linkDetail = String.format(SubscriptionConstant.LINK_SME_EXTEND_SERVICE, webHost, subscriptionId);
        } else {
            response.put(MailParam.NAME_COMBO, serviceName);
            response.put(MailParam.NAME_COMBO_PRICING, pricingName);
            emailCode = EmailCodeEnum.SCB46;
        }
        response.put(MailParam.NAME_COMPANY, companyName);
        response.put(MailParam.DATE_EXTEND, new Date().toString());
        response.put(MailParam.USER, userFullName);

        response.put(MailParam.LINK_DETAIL, linkDetail);
        response.put(MailParam.LINK_RE_ACTIVE, emailService.getLinkReActive(subscriptionId, PortalType.SME));
        response.put(MailParam.REMAIN_DAYS, getRemainDateSendMail(paymentCycleDate).toString());

        List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCode);

        emailService.sendMail(actorEmail, emailCode, mailParam);
        if (Objects.nonNull(parentActorEmail)) {
            linkDetail = String.format(subscriptionDetailService.getLinkSubDetailSME(parentActorId), webHost, subscriptionId);
            if (isSubPricing) {
                linkDetail = String.format(SubscriptionConstant.LINK_SME_EXTEND_SERVICE, webHost, subscriptionId);
            }
            response.put(MailParam.LINK_DETAIL, linkDetail);
            response.put(MailParam.USER, parentFullName);
            List<MailParamResDTO> mailParamParent = getListPramMailDTO(response, emailCode);
            emailService.sendMail(parentActorEmail, emailCode, mailParamParent);
        }
    }

    /**
     * Throw orderService bad request bad request exception.
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     *
     * @return the bad request exception
     */
    private BadRequestException throwOrderServiceBadRequest(String messageKeyConstant, String errorKey) {
        String message = messageSource.getMessage(messageKeyConstant, new Object[]{errorKey},
            LocaleContextHolder.getLocale());
        return new BadRequestException(message, Resources.MASSOFFER, Resources.RESPONSE, messageKeyConstant);
    }

    @Override
    public ChangeSubscription saveInfoWhenSwapSubscription(Subscription subscription, SubscriptionEditorDTO request, Pricing pricing,
        PricingMultiPlan pricingMultiPlan, BigDecimal totalAmountNew, SubscriptionFormulaResDTO formulaResDTO) {
        ChangeSubscription changeSubscription = subscriptionService.saveSubSwapByBatch(subscription, new ChangeSubscription(),
            Objects.nonNull(request.getPricingMultiPlanId()) ? pricingMultiPlan.getCircleType() : pricing.getCycleType(),
            Objects.nonNull(request.getPricingMultiPlanId()) ? Math.toIntExact(pricingMultiPlan.getPaymentCycle()) : pricing.getPaymentCycle(),
            Objects.nonNull(request.getNumberOfCycles()) ? request.getNumberOfCycles() :
                Objects.nonNull(request.getPricingMultiPlanId()) ? pricingMultiPlan.getNumberOfCycle() : pricing.getNumberOfCycles(),
            totalAmountNew,
            formulaResDTO.getTotalAmountAfterRefund(),
            null,
            pricing.getId(),
            request.getQuantity(),
            false
        );
        changeSubscription.setPricingMultiPlanId(request.getPricingMultiPlanId());
        changeSubscription.setId(null);
        changeSubscription.setStatus(StatusEnum.INACTIVE.value);
        changeSubscription.setAction(ActionChangeSubEnum.NOW.value);
        changeSubscription.setPaymentMethod(Objects.nonNull(request.getPaymentMethod()) ? request.getPaymentMethod().value
            : subscription.getPaymentMethod());
        changeSubscription.setNextPaymentAmount(subscription.getNextPaymentAmount());
        ChangeSubscription changeSubscriptionNew = changeSubscriptionRepository.save(changeSubscription);

        //Lưu thông tin addon and coupon mới vào các bảng lưu tạm thời
        request.getAddonsList().forEach(addon -> {
            subscriptionService.saveAddonInChangeSubsPricing(changeSubscriptionNew.getId(), addon, ActionChangeSubEnum.END_OF_PERIOD.value);
        });

        changeSubscriptionPricingCouponRepository.saveAll(
            request.getCouponPricings().stream().map(
                x -> new ChangeSubscriptionPricingCoupon(null, changeSubscriptionNew.getId(),
                    x.getId(), x.getPricingId(), 0, ActionChangeSubEnum.END_OF_PERIOD.value,
                    x.getPricingMultiPlanId(),
                    Objects.nonNull(x.getType()) ? x.getType().value : null)
            ).collect(Collectors.toList())
        );

        changeSubscriptionCouponsRepository.saveAll(
            request.getCouponList().stream().map(
                x -> new ChangeSubscriptionCoupons(null, changeSubscriptionNew.getId(),
                    x.getId(), x.getPricingId(), 0, ActionChangeSubEnum.END_OF_PERIOD.value,
                    x.getPricingMultiPlanId(),
                    Objects.nonNull(x.getType()) ? x.getType().value : null)
            ).collect(Collectors.toList())
        );

        //Lưu thông tin tùy chỉnh của SUB
        saveChangeSubscriptionCustomInfo(request,changeSubscriptionNew.getId());

        return changeSubscription;
    }

    @Override
    public ChangeSubscription saveInfoWhenSwapSubscriptionCombo(Subscription subscriptionNew, SubscriptionSwapComboDevDTO request, ComboPlan comboPlan,
        BigDecimal totalAmountNew, BigDecimal totalAmountNewAfterRefund) {
        ChangeSubscription changeSubscription = subscriptionService.saveSubSwapByBatch(subscriptionNew, new ChangeSubscription(),
            comboPlan.getCycleType(),
            comboPlan.getPaymentCycle(),
            Objects.nonNull(request.getNumberOfCycles()) ? request.getNumberOfCycles() : comboPlan.getNumberOfCycles(),
            totalAmountNew,
            totalAmountNewAfterRefund,
            subscriptionNew.getComboPlanId(),
            null,
            request.getQuantity(),
            false
        );
        changeSubscription.setId(null);
        changeSubscription.setStatus(StatusEnum.INACTIVE.value);
       
        Integer changeCombo =
                Objects.isNull(request.getStartAt().value) ? ComboChangeEnum.END_OF_PERIOD.value
                        : request.getStartAt().value;
        // Set lại thời điểm đổi gói theo request truyền lên 
        changeSubscription.setAction(ComboChangeEnum.END_OF_PERIOD.value.equals(changeCombo)
                ? ActionChangeSubEnum.END_OF_PERIOD.value
                : ActionChangeSubEnum.NOW.value);

        changeSubscription.setPaymentMethod(Objects.nonNull(request.getPaymentMethod()) ? request.getPaymentMethod().value
            : subscriptionNew.getPaymentMethod());
        ChangeSubscription changeSubscriptionNew = changeSubscriptionRepository.save(changeSubscription);

        //Lưu thông tin addon and coupon mới vào các bảng lưu tạm thời
        request.getAddonsList().forEach(addon -> {
            subscriptionService.saveChangeAddonInSubsCombo(changeSubscriptionNew.getId(), addon, ActionChangeSubEnum.NOW.value);
        });

        changeSubscriptionComboCouponRepository.saveAll(
            request.getComboCouponList().stream().map(
                x -> new ChangeSubscriptionComboCoupon(null, changeSubscriptionNew.getId(),
                    x.getId(), x.getPricingId(), 0, ActionChangeSubEnum.NOW.value,
                    x.getPricingMultiPlanId(),
                    Objects.nonNull(x.getType()) ? x.getType().value : null)
            ).collect(Collectors.toList())
        );

        changeSubscriptionCouponsRepository.saveAll(
            request.getCouponList().stream().map(
                x -> new ChangeSubscriptionCoupons(null, changeSubscriptionNew.getId(),
                    x.getId(), x.getPricingId(), 0, ActionChangeSubEnum.NOW.value,
                    x.getPricingMultiPlanId(),
                    Objects.nonNull(x.getType()) ? x.getType().value : null)
            ).collect(Collectors.toList())
        );

        //Lưu thông tin tùy chỉnh của SUB
        subscriptionService.saveChangeSubscriptionComboCustomInfo(request,changeSubscriptionNew.getId());
        return changeSubscription;
    }

    /**
     * Lưu thông tin tùy chỉnh của SUB
     */
    private void saveChangeSubscriptionCustomInfo(SubscriptionEditorDTO request, Long changeSubscriptionId) {

        // Lưu thông tin thông tin custom cho Pricing
        Integer numberCycle = Objects.isNull(request.getNumberOfCycles()) ? null : request.getNumberOfCycles();

            ChangeSubscriptionSetupFee saved = changeSubscriptionSetupFeeRepository.save(
                new ChangeSubscriptionSetupFee(null,
                    changeSubscriptionId,
                    null,
                    request.getPricingId(),
                    numberCycle,
                    request.getPrice(),
                    request.getSetupFee(), null, 0,
                    request.getPricingMultiPlanId())
            );
            List<ChangeSubscriptionUnitLimited> unitLimitedList = request.getUnitLimitedList().stream()
                .map(x -> new ChangeSubscriptionUnitLimited(x.getUnitFrom(),
                    Objects.isNull(x.getUnitTo()) ? -1 : x.getUnitTo(),
                    x.getPrice(),
                    saved.getId(), request.getPricingMultiPlanId())).collect(Collectors.toList()
                );

            // Lưu thông tin thông tin custom cho Addon
            request.getAddonsList().forEach(addonSubDev -> {
                ChangeSubscriptionSetupFee addonSaved = changeSubscriptionSetupFeeRepository.save(
                    new ChangeSubscriptionSetupFee(null,
                        changeSubscriptionId,
                        addonSubDev.getId(),
                        null,
                        null,
                        addonSubDev.getPrice(),
                        addonSubDev.getSetupFee(), null, 0, addonSubDev.getAddonMultiPlanId())
                );
                List<ChangeSubscriptionUnitLimited> unitLimitedAddons = addonSubDev.getUnitLimitedList()
                    .stream().map(x ->
                        new ChangeSubscriptionUnitLimited(x.getUnitFrom(),
                            Objects.isNull(x.getUnitTo()) ? -1 : x.getUnitTo(),
                            x.getPrice(),
                            addonSaved.getId(), addonSubDev.getAddonMultiPlanId())
                    ).collect(Collectors.toList());
                unitLimitedList.addAll(unitLimitedAddons);
            });

            changeSubscriptionUnitLimitedRepository.saveAll(unitLimitedList);

            changeCustomFeeRepository.saveAll(request.getOtherFeeList().stream().filter(x -> Objects.isNull(x.getCustomerFeeId())).map(cf ->
                new ChangeCustomFee(null,
                    changeSubscriptionId,
                    cf.getName(),
                    cf.getPrice(),
                    cf.getDescription(),
                    0, 0, new Date(), 0, 0)
            ).collect(Collectors.toList()));
    }

    @Override
    public void sendMailAndNotifyFailedTransaction(List<UserTransactionDTO> actors,
            UserTransactionDTO customer, SubscriptionTransactionDTO subscription, Long transactionId,
            EmailCodeEnum emailCode, ActionNotificationEnum actionNotification,
            PortalType portalType) {
        log.info("sendMailAndNotifyFailedTransaction start");
        if (Objects.isNull(subscription)) {
            throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.SUBSCRIPTION, ErrorKey.ID);
        }
        StringBuilder serviceName = new StringBuilder();
        StringBuilder pricingName = new StringBuilder();
        StringBuilder period = new StringBuilder();
        Map<String, String> response = new HashMap<>();
        List<MailSendParamDTO> param = new ArrayList<>();
        List<NotificationDTO> notify = new ArrayList<>();

        //Lấy thông tin gói dịch vụ
        getInfoServiceTransaction(subscription.getPricingId(), subscription.getPricingMultiPlanId(), subscription.getComboPlanId(), serviceName,
            pricingName, period);

        String customerName = Objects.equals(customer.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) ? customer.getLastName()
            .concat(CharacterConstant.SPACE).concat(customer.getFirstName()) : customer.getName();

        //Nếu gửi cho admin thì add link sự cố
        if (Objects.equals(PortalType.ADMIN, portalType)) {
            String linkIncident = String.format(SubscriptionConstant.LINK_ADMIN_TRANSACTION_DETAIL, webHost, transactionId);
            response.put(MailParam.LINK_INCIDENT, linkIncident);
        }

        //Trường hợp đổi gói
        if (Objects.equals(EmailCodeEnum.SLA07, emailCode) || Objects.equals(EmailCodeEnum.SLA08, emailCode)) {
            StringBuilder oldServiceName = new StringBuilder();
            StringBuilder oldPricingName = new StringBuilder();
            StringBuilder oldPeriod = new StringBuilder();
            TransactionLog transactionLog = transactionLogService.getTransactionLog(transactionId);

            //Lấy thông tin gói dịch vụ cũ
            getInfoServiceTransaction(transactionLog.getOldPricingId(), transactionLog.getOldPricingMultiPlanId(), transactionLog.getOldComboPlanId(),
                oldServiceName, oldPricingName, oldPeriod);

            response.put(MailParam.CUSTOMER_COMPANY_NAME, customerName);
            response.put(MailParam.NAME_SERVICE, oldServiceName.toString());
            response.put(MailParam.NAME_PRICING, oldPricingName.toString());
            response.put(MailParam.NAME_PERIOD, oldPeriod.toString());
            response.put(MailParam.NAME_SERVICE1, serviceName.toString());
            response.put(MailParam.NAME_PRICING1, pricingName.toString());
            response.put(MailParam.NAME_PERIOD1, period.toString());
            String[] notifyParams = {customerName, oldServiceName.toString(), oldPricingName.toString(), oldPeriod.toString(),
                serviceName.toString(), pricingName.toString(), period.toString()};

            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCode);
            actors.forEach(actor -> {
                String actorName = String.join(" ", Optional.ofNullable(actor.getLastName()).orElse(""),
                    Optional.ofNullable(actor.getFirstName()).orElse("")).trim();
                response.put(MailParam.USER, actorName);
                MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
                mailSendParamDTO.setMailToSend(actor.getEmail());
                mailSendParamDTO.setListMailParam(mailParam);
                param.add(mailSendParamDTO);
                notify.add(getNotifyTransaction(actor, transactionId, actionNotification, notifyParams, portalType));
            });
            emailService.sendMultiMail(emailCode, param);
            NotifyUtil.sendNotify(notify, actionNotification.getCode());
            return;
        }

        response.put(MailParam.CUSTOMER_COMPANY_NAME, customerName);
        response.put(MailParam.NAME_SERVICE, serviceName.toString());
        response.put(MailParam.NAME_PRICING, pricingName.toString());
        response.put(MailParam.NAME_PERIOD, period.toString());
        String[] notifyParams = {customerName, serviceName.toString(), pricingName.toString(), period.toString()};

        actors.forEach(actor -> {
            String actorName = String.join(" ", Optional.ofNullable(actor.getLastName()).orElse(""),
                Optional.ofNullable(actor.getFirstName()).orElse("")).trim();
            response.put(MailParam.USER, actorName);
            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCode);
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(actor.getEmail());
            mailSendParamDTO.setListMailParam(mailParam);
            param.add(mailSendParamDTO);
            notify.add(getNotifyTransaction(actor, transactionId, actionNotification, notifyParams, portalType));
        });
        emailService.sendMultiMail(emailCode, param);
        NotifyUtil.sendNotify(notify, actionNotification.getCode());
        log.info("sendMailAndNotifyFailedTransaction End");
    }


    /**
     * Lấy thông tin dịch vụ gặp sự cố
     *
     */
    private void getInfoServiceTransaction(Long pricingId, Long pricingMultiPlanId, Long comboPlanId, StringBuilder serviceName,
        StringBuilder pricingName, StringBuilder period) {
        if (Objects.nonNull(pricingId)) {
            PricingTransactionDTO pricing = pricingService.getPricingTransaction(pricingId);
            PricingProcessDTO pricingProcess = new PricingProcessDTO();
            pricingProcess.setCreatedBy(pricing.getCreatedBy());
            pricingProcess.setCycleType(pricing.getCycleType());
            pricingProcess.setPaymentCycle(pricing.getPaymentCycle());
            pricingProcess.setPricingName(pricing.getPricingName());
            pricingProcess.setServiceId(pricing.getServiceId());
            //Check nếu là multi period thì lấy theo thông tin multi period
            if (Objects.nonNull(pricingMultiPlanId)) {
                pricingProcess = subMultiplePeriod.getPricingByPricingIdAndPricingMulId(pricing, pricingMultiPlanId);
            }
            pricingProcess.setIsOneTime(pricing.getIsOneTime());
            String serviceNameDB = serviceRepository.getServiceNameById(pricingProcess.getServiceId());
            serviceName.append(Objects.nonNull(serviceNameDB) ? serviceNameDB : "");
            pricingName.append(Objects.nonNull(pricingProcess.getPricingName()) ? pricingProcess.getPricingName() : "");
            period.append(StringUtils.isNotEmpty(pricingProcess.getPaymentCycle().toString()
                    .concat(CycleTypeEnum.valueOf(pricingProcess.getCycleType()).name)) && !Objects.equals(SubTypeEnum.ONCE.value, pricingProcess.getIsOneTime())
                            ? pricingProcess.getPaymentCycle().toString()
                                    .concat(CharacterConstant.SPACE)
                                    .concat(CycleTypeEnum.valueOf(pricingProcess.getCycleType()).name)
                            : "");

        } else if (Objects.nonNull(comboPlanId)) {
            ComboTransactionDTO comboPlan = comboPlanService.getComboPlanTransactional(comboPlanId);
            String comboName = comboPlanService.getComboNameById(comboPlan.getComboId());
            serviceName.append(Objects.nonNull(comboName) ? comboName : "");
            pricingName.append(Objects.nonNull(comboPlan.getComboName()) ? comboPlan.getComboName() : "");
            period.append(
                StringUtils.isNotEmpty(comboPlan.getPaymentCycle().toString().concat(CycleTypeEnum.valueOf(comboPlan.getCycleType()).name))
                    ? comboPlan.getPaymentCycle().toString().concat(CharacterConstant.SPACE)
                        .concat(CycleTypeEnum.valueOf(comboPlan.getCycleType()).name) : "");
            }
        }

    /**
     * get notify failed transaction
     *
     */
    private NotificationDTO getNotifyTransaction(UserTransactionDTO actor, Long transactionId, ActionNotificationEnum actionNotification, String[] params,
        PortalType portalType) {
        String content = NotifyUtil.getContent(actionNotification.getContent(), params);
        String title = NotifyUtil.getContent(actionNotification.getTitle(), null);
        NotificationDTO notificationDTO = new NotificationDTO(title,
            content,
            actionNotification.getScreentId(),
            actor.getId(),
            portalType.getType(),
            transactionId);
        return notificationDTO;
    }
        
    @Override
    public Subscription findBySubscriptionId(Long subscriptionId) {
        return subscriptionRepository.findById(subscriptionId).orElseThrow(() -> {
            log.warn("Subscription not existed with id ={0}" + subscriptionId);
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    subscriptionMessage, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.SUBSCRIPTION,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        });
    }
    
    @Override
    public SubscriptionTransactionDTO findBySubscriptionIdTransaction(Long subscriptionId) {
        return subscriptionRepository.findSubscriptionById(subscriptionId).orElseThrow(() -> {
            log.warn("Subscription not existed with id ={0}" + subscriptionId);
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    subscriptionMessage, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(messageNotFound, Resources.SUBSCRIPTION,
                    ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        });
    }
}
