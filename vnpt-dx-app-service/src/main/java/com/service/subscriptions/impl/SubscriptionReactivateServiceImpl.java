package com.service.subscriptions.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.constant.ComboConst;
import com.constant.SubscriptionConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.subscription.ChangeSubActionEnum;
import com.constant.enums.subscription.RegTypeEnum;
import com.constant.enums.transactionLog.TransactionCodeEnum;
import com.dto.services.ServicePricingInfo;
import com.dto.subscriptions.ReActiveSubDTO;
import com.dto.subscriptions.SubReactiveReqDTO;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.bills.Bills;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.Subscription;
import com.entity.transaction_log.TransactionLog;
import com.enums.ActionNotificationEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.integration.producer.subscription.SubscriptionProducer;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.billings.BillStatusEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeContinueEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.subscriptions.TypeReActiveEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant.ContentType;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.integration.backend.IntegrationKafkaDTO;
import com.onedx.common.dto.integration.backend.IntegrationOneSmeDTO;
import com.onedx.common.dto.integration.backend.KafkaMessageResponseDTO;
import com.onedx.common.dto.integration.backend.kafka.KafkaSecretParamDTO;
import com.onedx.common.dto.integration.backend.subscription.AddonSubReqDTO;
import com.onedx.common.dto.integration.dhsxkd.RegisterDHSXKDResponseDTO;
import com.onedx.common.dto.integration.dhsxkd.ShoppingCartDHSXKDReqDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.repository.bills.BillsRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.SubscriptionAddonsRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.users.UserRepository;
import com.service.bills.BillsService;
import com.service.email.EmailService;
import com.service.integrate.IntegrationService;
import com.service.integrated.ExecutiveProducerService;
import com.service.multiplePeriod.SubMultiplePeriod;
import com.service.notification.ActionNotificationService;
import com.service.notification.template.SCD07;
import com.service.pricing.PricingService;
import com.service.services.ServicesService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.subscriptions.SubscriptionHelperService;
import com.service.subscriptions.SubscriptionHistoryService;
import com.service.subscriptions.SubscriptionReactivateService;
import com.service.subscriptions.SubscriptionService;
import com.service.system.param.SystemParamService;
import com.service.transactionLog.TransactionLogService;
import com.service.users.UserService;
import com.service.utils.values.ValueManager.CustomerOrgTypeEnum;
import com.util.AuthUtil;
import com.util.NotifyUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SubscriptionReactivateServiceImpl implements SubscriptionReactivateService {

    /*
     * Các hằng số sử dụng ở nhiều class khác nhau
     */
    public static final Long DEFAULT_SUCCESS_ID = 0L;
    private static final Integer SWAP = 1;
    private static final String SERVICE = "SERVICE";
    private static final String ADMIN = "ADMIN";
    private static final String DEV = "DEV";
    private static final String SME = "SME";

    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Lazy
    @Autowired
    private SubscriptionService subscriptionService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    @Lazy
    private IntegrationService integrationService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private SubscriptionAddonsRepository subscriptionAddonsRepository;
    @Autowired
    private EmailService emailService;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private BillsRepository billsRepository;
    @Autowired
    private ServicesService servicesService;
    @Autowired
    private SubscriptionProducer subscriptionProducer;
    @Autowired
    private ExecutiveProducerService executiveProducerService;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private SubMultiplePeriod subMultiplePeriod;
    @Autowired
    private BillsService billsService;
    @Autowired
    private SubscriptionHelperService subscriptionHelperService;
    @Autowired
    private UserService userService;
    @Autowired
    private SubscriptionHistoryService subscriptionHistoryService;
    @Autowired
    private TransactionLogService transactionLogService;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private ActionNotificationService actionNotificationService;
    @Autowired
    private ExceptionFactory exceptionFactory;

    @Override
    @Transactional
    public BaseResponseDTO activateSubscription(Long subscriptionId, PortalType portal, String token, Long userId) {
        //kiem tra subscription đã hủy có tồn tại hoặc đã xóa chưa?
        Subscription sub = subscriptionRepository
            .checkSubscriptionCancelExits(subscriptionId)
            .orElseThrow(() -> throwSubscriptionBadRequest(
                MessageKeyConstant.NOT_FOUND, ErrorKey.Subscription.ID));
        Pricing pricingDB = pricingService.findByIdAndDeletedFlag(sub.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());

        // kiểm tra trạng thái hiển thị của cktt

        // validate trang thai hien thi cua chu ky thanh toan (neu co chu ky thanh toan)
        Long pricingMultiPlanId = sub.getPricingMultiPlanId();
        if (pricingMultiPlanId != null && pricingMultiPlanId != -1) {
            Optional<PricingMultiPlan> prm = pricingMultiPlanRepository.findByIdAndDeletedFlag(pricingMultiPlanId, 1);
            if (prm.isPresent()) {
                int displayStatus = prm.get().getDisplayStatus();
                if (displayStatus != 1) {
                    throw new BadRequestException("Không thể gia hạn do chu kỳ thanh toán đã tắt hiển thị", Resources.SUBSCRIPTION,
                        ErrorKey.Subscription.PRICINGMULTIPLAN, MessageKeyConstant.CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_PRM);
                }
            }
        }

        pricingDB = subMultiplePeriod.transformPricingMultiPlanIdToPricing(pricingDB, sub.getPricingMultiPlanId());
        // Admin tổng không kích hoạt lại sub của admin tỉnh. Admin tỉnh kích hoạt lại của nó và sme tạo
        // bypass với trường hợp đồng bộ với OneBSS
        if (!Objects.equals(portal, PortalType.ONE_BSS)) {
            validateProvinceAdmin(sub.getCreatedBy());
        }
        // Dev được kích hoạt các subscription trong doanh nghiệp
        // SME chỉ kích hoạt lại được subscription của chính nó đăng ký
        Set<Long> user = new HashSet<>();
        user.add(sub.getRegistedBy());
        user.add(sub.getUserId());
        if (portal.equals(PortalType.DEV)) {
            ServiceEntity serviceEntity = servicesService.findByIdAndDeletedFlag(sub.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
            if (!AuthUtil.getCurrentParentId().equals(serviceEntity.getUserId())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            // validate customerType cua service vs pricing
            if (!serviceEntity.getCustomerTypeCode().containsAll(pricingDB.getCustomerTypeCode()) ||
                CollectionUtils.isEmpty(pricingDB.getCustomerTypeCode())) {
                throw new BadRequestException("Sản phẩm dịch vụ không tồn tại trên nền tảng oneSME. Vui lòng đăng ký sử dụng dịch vụ khác",
                    Resources.SUBSCRIPTION,
                    ErrorKey.Subscription.PRICINGMULTIPLAN, MessageKeyConstant.CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_CUSTOMER_TYPE);
            }
        } else if (portal.equals(PortalType.SME) && !user.contains(AuthUtil.getCurrentParentId())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        } else if (portal.equals(PortalType.ONE_BSS) && (Objects.isNull(userId) || !user.contains(userId))) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        // kiểm tra thuê bao còn hóa đơn nào quá hạn thanh toán không
        subscriptionService.validateBillsOutOfDate(subscriptionId);
        //kiểm tra thoi gian kich hoat lai sau hủy
        subscriptionService.validateTimeActiveDate(sub);
        // Kích hoạt lại dịch vụ:
        sub.setStatus(SubscriptionStatusEnum.ACTIVE.value);
        sub.setStartedAt(new Date());
        sub.setAwaitingCancel(null);
        if (Objects.isNull(sub.getCurrentCycle()) && Objects.equals(sub.getRegType(), RegTypeEnum.OFFICIAL.value)) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Subscription.CURRENT_CYCLE);
        }
        sub.setCurrentCycle(sub.getCurrentCycle() + 1);
        if (Objects.equals(sub.getIsSwap(), SWAP)) {
            sub.setCurrentCycleSwap(Objects.nonNull(sub.getCurrentCycleSwap()) ? sub.getCurrentCycleSwap() + 1 : null);
        }
        sub.setStartCurrentCycle(new Date());
        Integer payment = pricingDB.getPaymentCycle();
        Integer numberCycle = Objects.isNull(sub.getNumberOfCycles()) ? pricingDB.getNumberOfCycles() : sub.getNumberOfCycles();
        Integer cycleType = Objects.isNull(sub.getCycleType()) ? pricingDB.getCycleType() : sub.getCycleType();
        if (Objects.isNull(payment)) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.PAYMENT_CYCLE);
        }
        if (Objects.isNull(cycleType)) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.CYCLE_TYPE);
        }

        LocalDate newEndCurrentCycle = DateUtil
            .calculateCycleDate(new Date(), payment,
                CycleTypeEnum.valueOf(cycleType), true, 1);
        sub.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));

        sub.setCurrentPaymentDate(
            PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType()) ?
                new Date() :
                DateUtil.toDate(
                    DateUtil.calculateCycleDate(
                        new Date(),
                        payment,
                        CycleTypeEnum.valueOf(cycleType),
                        false,
                        1)
                )
        );

        sub.setNextPaymentTime(
            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(sub.getCurrentPaymentDate(),
                payment, CycleTypeEnum.valueOf(cycleType), false,
                1)));

        if (Objects.nonNull(numberCycle) && !Objects.equals(numberCycle, ComboConst.UNSET)) {
            if (numberCycle.compareTo(sub.getCurrentCycle()) < 0) {
                // Nếu hủy và kích hoạt sub cuối, tăng cả chu kỳ của thuê bao
                numberCycle = sub.getCurrentCycle();
                sub.setNumberOfCycles(numberCycle);
            }
            Integer cycle = numberCycle - sub.getCurrentCycle() + 1;
            LocalDate newExpiredTime = DateUtil
                .calculateCycleDate(new Date(), payment,
                    CycleTypeEnum.valueOf(cycleType), true, cycle);
            sub.setExpiredTime(DateUtil.toDate(newExpiredTime));
        } else {
            sub.setExpiredTime(null);
        }

        sub = subscriptionRepository.save(sub);
        // Lưu lịch sử thay đổi trạng thái thuê bao
        subscriptionHistoryService.saveStatusHistory(sub);
        if (!StringUtils.isEmpty(token)) {
            integrationService.transactionOneSME(token, sub, pricingDB,
                IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION, null, null, null, false, null);
        }
        // Lưu vào transaction logs
        TransactionLog transactionLog = transactionLogService.addTransactionLog(
            pricingDB.getId(), sub.getUserId(), SERVICE, TransactionCodeEnum.REACTIVE.getValue(), sub.getId(), pricingMultiPlanId);
        CommonActivityLogInfoDTO activityLogInfoDTO = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION,
            transactionLog);

        // Call BE SPDV oneBSS
        if (Objects.equals(portal, PortalType.ONE_BSS)) {
            integrationService.transactionOneSME(token, sub, pricingDB,
                IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION, null, null, null, false, activityLogInfoDTO);
        }
        // Update status transaction
        transactionLogService.updateTransactionLogCompleted(transactionLog, activityLogInfoDTO);

        //Tạo bill mới
        List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
            .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
            .collect(Collectors.toList());
        //============================================Tính tiền=============================================//
        SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList, SubscriptionConstant.RE_ACTIVATE,
            null);

        BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
        totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
        Bills bills = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew, null, null);
        billsService.saveBillDetail(bills, formulaResDTO, CalculateTypeEnum.PRICING, sub, null);

        //Tính toán credit note
        if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
            subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
        }

        // thêm lịch sử khi SME kích hoạt lại thuê bao
        String hisContent = SubscriptionHistoryConstant.SUB_REACTIVE;
        int hisType = ContentType.SUB_REACTIVE;
        subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType,
            Objects.equals(portal, PortalType.ONE_BSS) ? userId : AuthUtil.getCurrentUserId());
        return new BaseResponseDTO(subscriptionId);
    }

    @Override
    @Transactional
    public ReActiveSubDTO reActivateSubscription(Long subscriptionId, PortalType portal, String token, SubReactiveReqDTO reactiveReqDTO) {
        //kiem tra subscription đã hủy có tồn tại hoặc đã xóa chưa?
        Subscription sub = subscriptionRepository
            .checkSubscriptionCancelExits(subscriptionId)
            .orElseThrow(() -> throwSubscriptionBadRequest(
                MessageKeyConstant.NOT_FOUND, ErrorKey.Subscription.ID));

        // check dịch vụ, gói dịch vụ, chu kỳ thah toán có thỏa mãn điều kiện gia hạn hay không
        ServiceEntity serviceEntity = serviceRepository.findByIdAndDeletedFlag(sub.getServiceId(), DeletedFlag.NOT_YET_DELETED
            .getValue()).orElseThrow(() -> throwSubscriptionBadRequest(
            MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_SERVICE_OFF, ErrorKey.Subscription.SERVICE));
        if (!serviceEntity.getStatus().equals(StatusEnum.ACTIVE.value)) {
            throw new BadRequestException(MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_SERVICE_OFF, Resources.SUBSCRIPTION,
                ErrorKey.Subscription.SERVICE,
                MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_SERVICE_OFF);
        }

        Pricing pricingDB = pricingRepository.findByIdAndDeletedFlag(sub.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> throwSubscriptionBadRequest(
                MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRICING_OFF, ErrorKey.Subscription.PRICING_ID_INTEGRATION));

        if (!pricingDB.getStatus().equals(StatusEnum.ACTIVE.value)) {
            throw new BadRequestException(MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRICING_OFF, Resources.SUBSCRIPTION,
                ErrorKey.Subscription.SERVICE,
                MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRICING_OFF);
        }

        PricingMultiPlan pricingMultiPlan = null;
        if (Objects.nonNull(sub.getPricingMultiPlanId())) {
            pricingMultiPlan = pricingMultiPlanRepository
                .findByIdAndDeletedFlag(sub.getPricingMultiPlanId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> throwSubscriptionBadRequest(MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRM_OFF,
                    ErrorKey.Subscription.PRICINGMULTIPLAN));
            if (!pricingMultiPlan.getDisplayStatus().equals(StatusEnum.ACTIVE.value)) {
                throw new BadRequestException(MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRM_OFF, Resources.SUBSCRIPTION,
                    ErrorKey.Subscription.SERVICE,
                    MessageKeyConstant.CAN_NOT_REACTIVE_SUBSCRIPTION_PRM_OFF);
            }
        }

        pricingDB = subMultiplePeriod.transformPricingMultiPlanIdToPricing(pricingDB, sub.getPricingMultiPlanId());
        // Admin tổng không kích hoạt lại sub của admin tỉnh. Admin tỉnh kích hoạt lại của nó và sme tạo
//        validateProvinceAdmin(sub.getCreatedBy());
        // Dev được kích hoạt các subscription trong doanh nghiệp
        // SME chỉ kích hoạt lại được subscription của chính nó đăng ký
        Set<Long> user = new HashSet<>();
        user.add(sub.getRegistedBy());
        user.add(sub.getUserId());
        if (portal.equals(PortalType.DEV)) {
            serviceEntity = servicesService.findByIdAndDeletedFlag(sub.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
            if (!AuthUtil.getCurrentParentId().equals(serviceEntity.getUserId())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            // validate customerType cua service vs pricing
            if (!serviceEntity.getCustomerTypeCode().containsAll(pricingDB.getCustomerTypeCode()) ||
                CollectionUtils.isEmpty(pricingDB.getCustomerTypeCode())) {
                throw new BadRequestException("Sản phẩm dịch vụ không tồn tại trên nền tảng oneSME. Vui lòng đăng ký sử dụng dịch vụ khác",
                    Resources.SUBSCRIPTION,
                    ErrorKey.Subscription.PRICINGMULTIPLAN, MessageKeyConstant.CAN_NOT_RENEW_SUBSCRIPTION_CAUSE_CUSTOMER_TYPE);
            }
        } else if (portal.equals(PortalType.SME) && !user.contains(AuthUtil.getCurrentParentId())) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        // kiểm tra thoi gian kich hoat lai sau hủy với TH là SME kích hoạt
        if (portal.equals(PortalType.SME)) {
            subscriptionService.validateTimeActiveDate(sub);
        } else {
            sub.setCurrentCycleReactive(reactiveReqDTO.getUseCycle());
            sub.setReactiveDate(reactiveReqDTO.getReactiveDate());
        }
        // kiểm tra thuê bao còn hóa đơn nào quá hạn thanh toán không
        Bills billsOutOfDate = billsRepository.findBillOutOfDate(subscriptionId);
        SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.RE_ACTIVE_SUB);
        Date currentDate = DateUtil.toDate(LocalDate.now());
        boolean isTimeInPaymentCycle = currentDate.compareTo(sub.getStartCurrentCycle()) >= 0 && (currentDate.before(sub.getEndCurrentCycle())
            || Objects.equals(currentDate.getTime(), sub.getEndCurrentCycle().getTime()));
        Bills bills = billsRepository.findCurrentBillBySubscriptionId(subscriptionId)
            .orElseThrow(() -> throwBillingBadRequest(MessageKeyConstant.DATA_EXISTS, ErrorKey.ID));

        Integer payment = pricingDB.getPaymentCycle();
        Integer cycleType = Objects.isNull(sub.getCycleType()) ? pricingDB.getCycleType() : sub.getCycleType();
        if (Objects.nonNull(pricingMultiPlan)) {
            cycleType = pricingMultiPlan.getCircleType();
            payment = Math.toIntExact(pricingMultiPlan.getPaymentCycle());
        }
        if (Objects.isNull(payment)) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.PAYMENT_CYCLE);
        }
        if (Objects.isNull(cycleType)) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.CYCLE_TYPE);
        }
        if (!portal.equals(PortalType.SME)) {
            sub.setTypeReactive(reactiveReqDTO.getTypeActiveInPaymentType().value);
        } else {
            sub.setTypeReactive(pricingDB.getTypeActiveInPaymentType());
        }
        subscriptionRepository.save(sub);
        // thêm lịch sử khi kích hoạt lại thuê bao
        String hisContent = SubscriptionHistoryConstant.SUB_REACTIVE;
        int hisType = ContentType.SUB_REACTIVE;

        if (!portal.equals(PortalType.SME) && billsOutOfDate == null) {
            if (isTimeInPaymentCycle && PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType())) {
                boolean isPaymentRequest = ChangeContinueEnum.CONTINUE.equals(reactiveReqDTO.getTypeActiveInPaymentType()) &&
                    YesNoEnum.YES.equals(reactiveReqDTO.getPaymentRequest());
                if (ChangeContinueEnum.CHANGE.equals(reactiveReqDTO.getTypeActiveInPaymentType()) || isPaymentRequest) {
                    //Tạo bill mới
                    List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
                        .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
                        .collect(Collectors.toList());
                    //============================================Tính tiền=============================================//
                    SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList,
                        SubscriptionConstant.RE_ACTIVATE, null);

                    BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
                    totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
                    Bills billNew = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew,
                        BillStatusEnum.INVALID.value, null);
                    billsService.saveBillDetailReactive(billNew, formulaResDTO, CalculateTypeEnum.PRICING, sub, null,
                        ChangeSubActionEnum.REACTIVE.value);
                    if (isPaymentRequest) {
                        // neu KH lai su dung chu ky cu, yeu cau thanh toan chu ky moi, update lai ngay sd-ket thuc cua hoa don
                        updateBillingDate(sub, billNew, pricingDB, pricingMultiPlan);
                    }
                    if (Objects.nonNull(billNew.getTotalAmount()) && BigDecimal.ZERO.compareTo(billNew.getTotalAmount()) == 0) {
                        billNew.setStatus(BillStatusEnum.PAID.value);
                        billNew.setActionType(4);
                        reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal,
                            reactiveReqDTO, true);
                        billNew.setEndDate(sub.getEndCurrentCycle());
                        billsRepository.save(billNew);
                        subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                        return new ReActiveSubDTO(null, subscriptionId);
                    }
                    //Tính toán credit note
                    if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
                        subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
                    }
                    billNew.setActionType(4); // action re-active bill in payment time
                    billsRepository.save(billNew);
                    return new ReActiveSubDTO(billNew.getId(), null);
                }
                reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO,
                    false);
                subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                return new ReActiveSubDTO(null, subscriptionId);
            }
        }
        if (billsOutOfDate != null || !isTimeInPaymentCycle) {
            // kiểm tra pricing_type
            if (PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType())
                || (billsOutOfDate != null && PricingTypeEnum.POSTPAID.value.equals(pricingDB.getPricingType()) &&
                Objects.equals(systemParam.getReActiveAllow(), 0))) {
                //Tạo bill mới
                List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
                    .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
                    .collect(Collectors.toList());
                //============================================Tính tiền=============================================//
                SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList,
                    SubscriptionConstant.RE_ACTIVATE, null);

                BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
                totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
                Bills billNew = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew,
                    BillStatusEnum.INVALID.value, null);
                billsService.saveBillDetailReactive(billNew, formulaResDTO, CalculateTypeEnum.PRICING, sub, null,
                    ChangeSubActionEnum.REACTIVE.value);

                if (Objects.nonNull(billNew.getTotalAmount()) && BigDecimal.ZERO.compareTo(billNew.getTotalAmount()) == 0) {
                    billNew.setStatus(BillStatusEnum.PAID.value);
                    billNew.setActionType(3);
                    reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO,
                        true);
                    billNew.setEndDate(sub.getEndCurrentCycle());
                    billsRepository.save(billNew);
                    subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                    return new ReActiveSubDTO(null, subscriptionId);
                }
                //Tính toán credit note
                if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
                    subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
                }
                billNew.setActionType(3); // action re-active bill out of date
                billsRepository.save(billNew);
                return new ReActiveSubDTO(billNew.getId(), null);
            } else if (billsOutOfDate == null && !isTimeInPaymentCycle) {
                //Tạo bill mới
                List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
                    .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
                    .collect(Collectors.toList());
                //============================================Tính tiền=============================================//
                SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList,
                    SubscriptionConstant.RE_ACTIVATE, null);

                BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
                totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
                Date dateFeature =
                    Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getReactiveDate()) ? reactiveReqDTO.getReactiveDate()
                        : null;
                Bills billNew = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew,
                    BillStatusEnum.INIT.value, dateFeature);
                billsService.saveBillDetailReactive(billNew, formulaResDTO, CalculateTypeEnum.PRICING, sub, null,
                    ChangeSubActionEnum.REACTIVE.value);

                if (Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getUseCycle())) {
                    billNew.setCurrentCycle(reactiveReqDTO.getUseCycle());
                } else {
                    billNew.setCurrentCycle(sub.getCurrentCycle() + 1);
                }

                if (Objects.nonNull(billNew.getTotalAmount()) && BigDecimal.ZERO.compareTo(billNew.getTotalAmount()) == 0) {
                    billNew.setStatus(BillStatusEnum.PAID.value);
                    billNew.setActionType(4);
                    reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO,
                        true);
                    billNew.setEndDate(sub.getEndCurrentCycle());
                    billsRepository.save(billNew);
                    subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                    return new ReActiveSubDTO(null, subscriptionId);
                }

                billsRepository.save(billNew);
                //Tính toán credit note
                if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
                    subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
                }
                reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.OUT_OF_DATE.value, portal, reactiveReqDTO,
                    false);
                subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                return new ReActiveSubDTO(null, subscriptionId);
            }
        } else if (PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType())) {
            if (pricingDB.getTypeActiveInPaymentType() == 1 || (pricingDB.getTypeActiveInPaymentType() == 0 &&
                pricingDB.getPaymentRequest() == 1)) {
                //Tạo bill mới
                List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
                    .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
                    .collect(Collectors.toList());
                //============================================Tính tiền=============================================//
                SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList,
                    SubscriptionConstant.RE_ACTIVATE, null);

                BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
                totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
                Bills billNew = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew,
                    BillStatusEnum.INVALID.value, null);
                billsService.saveBillDetailReactive(billNew, formulaResDTO, CalculateTypeEnum.PRICING, sub, null,
                    ChangeSubActionEnum.REACTIVE.value);

                if (Objects.nonNull(billNew.getTotalAmount()) && BigDecimal.ZERO.compareTo(billNew.getTotalAmount()) == 0) {
                    billNew.setStatus(BillStatusEnum.PAID.value);
                    billNew.setActionType(4);
                    reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO,
                        true);
                    billNew.setEndDate(sub.getEndCurrentCycle());
                    billsRepository.save(billNew);
                    subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                    return new ReActiveSubDTO(null, subscriptionId);
                }
                //Tính toán credit note
                if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
                    subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
                }
                billNew.setActionType(4); // action re-active bill in payment time
                billsRepository.save(billNew);
                return new ReActiveSubDTO(billNew.getId(), null);
            }
        }
        Bills billNew = null;
        if (!PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType())) {
            if (pricingDB.getTypeActiveInPaymentType() == 1 ||
                (Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getTypeActiveInPaymentType()) &&
                    ChangeContinueEnum.CHANGE.equals(reactiveReqDTO.getTypeActiveInPaymentType()))) {
                //Tạo bill mới
                List<AddonSubReqDTO> addonList = subscriptionAddonsRepository.findBySubscriptionId(subscriptionId)
                    .stream().map(x -> new AddonSubReqDTO(x.getAddonsId(), x.getQuantity(), new ArrayList<>(), x.getAddon().getBonusType()))
                    .collect(Collectors.toList());
                //============================================Tính tiền=============================================//
                SubscriptionFormulaResDTO formulaResDTO = subscriptionFormula.reCalculateUpdateSub(sub, addonList,
                    SubscriptionConstant.RE_ACTIVATE, null);

                BigDecimal totalAmountNew = formulaResDTO.getTotalAmountAfterTaxFinal();
                totalAmountNew = totalAmountNew.setScale(0, RoundingMode.HALF_UP);
                Date dateFeature =
                    Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getReactiveDate()) ? reactiveReqDTO.getReactiveDate()
                        : null;
                billNew = billsService.createOverPriceBillClone(sub, cycleType, payment, formulaResDTO, totalAmountNew,
                    BillStatusEnum.INIT.value, dateFeature);
                billsService.saveBillDetailReactive(billNew, formulaResDTO, CalculateTypeEnum.PRICING, sub, null,
                    ChangeSubActionEnum.REACTIVE.value);
                if (Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getUseCycle())) {
                    billNew.setCurrentCycle(reactiveReqDTO.getUseCycle());
                } else {
                    billNew.setCurrentCycle(sub.getCurrentCycle() + 1);
                }

                if (Objects.nonNull(billNew.getTotalAmount()) && BigDecimal.ZERO.compareTo(billNew.getTotalAmount()) == 0) {
                    billNew.setStatus(BillStatusEnum.PAID.value);
                    reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token, TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO,
                        true);
                    billNew.setEndDate(sub.getEndCurrentCycle());
                    billsRepository.save(billNew);
                    subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);
                    return new ReActiveSubDTO(null, subscriptionId);
                }

                //Tính toán credit note
                if (!CollectionUtils.isEmpty(formulaResDTO.getCreditNoteListNew())) {
                    subscriptionFormula.calculateCreditNote(sub, bills, formulaResDTO.getCreditNoteListNew(), null);
                }
            }
            if (billsOutOfDate != null) {
                billsOutOfDate.setStatus(BillStatusEnum.WAITING.value);
            } else if (bills.getStatus() == BillStatusEnum.OUT_OF_DATE.value) {
                bills.setStatus(BillStatusEnum.INIT.value);
            }

        }
        reActiveSubNotPayment(payment, cycleType, sub, pricingDB, token,
            billsOutOfDate != null ? TypeReActiveEnum.OUT_OF_DATE.value : TypeReActiveEnum.IN_PAYMENT.value, portal, reactiveReqDTO, false);
        if (billNew != null) {
            billNew.setActionType(4);
            billNew.setEndDate(sub.getEndCurrentCycle());
            billsRepository.save(billNew);
        }

//        bills.setCurrentCycle(sub.getCurrentCycle());
//        billsRepository.save(bills);

        // thêm lịch sử khi SME kích hoạt lại thuê bao
        subscriptionHistoryService.addSubscriptionHistory(subscriptionId, hisContent, hisType);

        return new ReActiveSubDTO(null, subscriptionId);
    }

    private void updateBillingDate(Subscription subscription, Bills billNew, Pricing pricing, PricingMultiPlan pricingMultiPlan) {
        Date startDateReactive = DateUtil.toDate(DateUtil.convertDateToLocalDateTime(subscription.getStartCurrentCycle()));
        Date endDateNewRenewal = DateUtil.toDate(DateUtil.convertDateToLocalDateTime(subscription.getEndCurrentCycle()));
        billNew.setBillingDate(
            DateUtil.toDate(
                DateUtil.calculateCycleDate(
                    startDateReactive,
                    Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getPaymentCycle().intValue() : pricing.getPaymentCycle(),
                    CycleTypeEnum.valueOf(Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getCircleType() : pricing.getCycleType()),
                    false,
                    1)
            )
        );
        billNew.setEndDate(
            DateUtil.toDate(
                DateUtil.calculateCycleDate(
                    endDateNewRenewal,
                    Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getPaymentCycle().intValue() : pricing.getPaymentCycle(),
                    CycleTypeEnum.valueOf(Objects.nonNull(pricingMultiPlan) ? pricingMultiPlan.getCircleType() : pricing.getCycleType()),
                    false,
                    1)
            )
        );
    }

    // Kích hoạt lại dịch vụ:
    private void reActiveSubNotPayment(Integer payment, Integer cycleType, Subscription sub, Pricing pricingDB, String token, Integer type,
        PortalType portal, SubReactiveReqDTO reactiveReqDTO, boolean isZero) {
        //khi gửi từ SME
        boolean reactiveFeature = Boolean.TRUE;
        Date currentDateReactive = DateUtil.toDate(LocalDate.now());
        if (Objects.nonNull(reactiveReqDTO) && Objects.nonNull(reactiveReqDTO.getReactiveDate()) &&
            reactiveReqDTO.getReactiveDate().compareTo(currentDateReactive) > 0) {
            reactiveFeature = Boolean.FALSE;
            sub.setReactiveStatus(1);
        }
        if (reactiveReqDTO != null && reactiveReqDTO.getUseCycle() != null && reactiveFeature) {
            sub.setCurrentCycle(reactiveReqDTO.getUseCycle());
            if (Objects.equals(sub.getIsSwap(), SWAP)) {
                sub.setCurrentCycleSwap(Objects.nonNull(reactiveReqDTO.getUseCycle()) ? reactiveReqDTO.getUseCycle() : null);
            }
        }
        if (portal.equals(PortalType.SME)) {
            sub.setStatus(SubscriptionStatusEnum.ACTIVE.value);
            if (TypeReActiveEnum.OUT_OF_DATE.value.equals(type) || (TypeReActiveEnum.IN_PAYMENT.value.equals(type)
                && pricingDB.getTypeActiveInPaymentType() == 1)) {
                if (Objects.isNull(sub.getCurrentCycle()) && Objects.equals(sub.getRegType(), RegTypeEnum.OFFICIAL.value)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Subscription.CURRENT_CYCLE);
                }
                sub.setCurrentCycle(sub.getCurrentCycle() + 1);
                if (Objects.equals(sub.getIsSwap(), SWAP)) {
                    sub.setCurrentCycleSwap(Objects.nonNull(sub.getCurrentCycleSwap()) ? sub.getCurrentCycleSwap() + 1 : null);
                }
            }
            if ((TypeReActiveEnum.IN_PAYMENT.value.equals(type)
                && pricingDB.getTypeActiveInPaymentType() == 1) || TypeReActiveEnum.OUT_OF_DATE.value.equals(type)) {

                sub.setStartCurrentCycle(new Date());
                sub.setStartedAt(new Date());
                if (Objects.isNull(payment)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.PAYMENT_CYCLE);
                }
                if (Objects.isNull(cycleType)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.CYCLE_TYPE);
                }

                LocalDate newEndCurrentCycle = DateUtil
                    .calculateCycleDate(new Date(), payment,
                        CycleTypeEnum.valueOf(cycleType), true, 1);
                sub.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));

                sub.setCurrentPaymentDate(
                    PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType()) ?
                        new Date() :
                        DateUtil.toDate(
                            DateUtil.calculateCycleDate(
                                new Date(),
                                payment,
                                CycleTypeEnum.valueOf(cycleType),
                                false,
                                1)
                        )
                );

                sub.setNextPaymentTime(
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(new Date(),
                        payment, CycleTypeEnum.valueOf(cycleType), false,
                        1)));
                // trả sau gói 0đ
                if (!PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType()) && isZero) {
                    if (isZero) {
                        sub.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(new Date(),
                                payment, CycleTypeEnum.valueOf(cycleType), false,
                                2)));
                    } else {
                        sub.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(new Date(),
                                payment, CycleTypeEnum.valueOf(cycleType), false,
                                1)));
                    }

                }
            } else {
                sub.setTypeReactive(null);
            }
            //khi từ Admin/Dev
        } else if (reactiveFeature) {
            if (reactiveReqDTO.getTypeActiveInPaymentType().equals(ChangeContinueEnum.CHANGE)) {
                sub.setStartedAt(reactiveReqDTO.getReactiveDate());
            }
            if (reactiveReqDTO.getReactiveDate().getTime() > new Date().getTime()) {
                sub.setStatus(SubscriptionStatusEnum.CANCELED.value);
            } else {
                sub.setStatus(SubscriptionStatusEnum.ACTIVE.value);
            }
            if (TypeReActiveEnum.OUT_OF_DATE.value.equals(type)) {
                if (Objects.isNull(sub.getCurrentCycle()) && Objects.equals(sub.getRegType(), RegTypeEnum.OFFICIAL.value)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Subscription.CURRENT_CYCLE);
                }
            }
            if ((TypeReActiveEnum.IN_PAYMENT.value.equals(type)
                && reactiveReqDTO.getTypeActiveInPaymentType().equals(ChangeContinueEnum.CHANGE)) ||
                TypeReActiveEnum.OUT_OF_DATE.value.equals(type)) {

                sub.setStartCurrentCycle(reactiveReqDTO.getReactiveDate());
                if (Objects.isNull(payment)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.PAYMENT_CYCLE);
                }
                if (Objects.isNull(cycleType)) {
                    throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.CYCLE_TYPE);
                }

                LocalDate newEndCurrentCycle = DateUtil
                    .calculateCycleDate(reactiveReqDTO.getReactiveDate(), payment,
                        CycleTypeEnum.valueOf(cycleType), true, 1);
                sub.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));

                sub.setCurrentPaymentDate(
                    PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType()) ?
                        new Date() :
                        DateUtil.toDate(
                            DateUtil.calculateCycleDate(
                                reactiveReqDTO.getReactiveDate(),
                                payment,
                                CycleTypeEnum.valueOf(cycleType),
                                false,
                                1)
                        )
                );

                sub.setNextPaymentTime(
                    DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(reactiveReqDTO.getReactiveDate(),
                        payment, CycleTypeEnum.valueOf(cycleType), false,
                        1)));
                if (!Objects.isNull(sub.getNumberOfCycles()) && sub.getNumberOfCycles() != -1L) {
                    LocalDate newExpiredTime = DateUtil
                        .calculateCycleDate(reactiveReqDTO.getReactiveDate(), pricingDB.getPaymentCycle(),
                            CycleTypeEnum.valueOf(sub.getCycleType()), true, sub.getNumberOfCycles() - reactiveReqDTO.getUseCycle() + 1);
                    sub.setExpiredTime(DateUtil.toDate(newExpiredTime));

                }

                // trả sau gói 0đ
                if (!PricingTypeEnum.PREPAY.value.equals(pricingDB.getPricingType()) && isZero) {
                    sub.setNextPaymentTime(
                        DateUtil.toDate(
                            DateUtil.calculateCycleDate(
                                reactiveReqDTO.getReactiveDate(),
                                payment,
                                CycleTypeEnum.valueOf(cycleType),
                                false,
                                2)
                        ));
                }

            }

            if (reactiveReqDTO.getUseCycle() != null) {
                sub.setCurrentCycle(reactiveReqDTO.getUseCycle());
                if (Objects.equals(sub.getIsSwap(), SWAP)) {
                    sub.setCurrentCycleSwap(Objects.nonNull(sub.getCurrentCycleSwap()) ? reactiveReqDTO.getUseCycle() : null);
                }
            } else {
                sub.setCurrentCycle(sub.getCurrentCycle() + 1);
                if (Objects.equals(sub.getIsSwap(), SWAP)) {
                    sub.setCurrentCycleSwap(Objects.nonNull(sub.getCurrentCycleSwap()) ? sub.getCurrentCycleSwap() + 1 : null);
                }
            }
        }
        sub.setModifiedBy(AuthUtil.getCurrentUserId());

        sub = subscriptionRepository.save(sub);

        User userSub = userService.findByIdAndDeletedFlag(AuthUtil.getCurrentUserId(), DeletedFlag.NOT_YET_DELETED.getValue());
        ServicePricingInfo servicePricingInfo = serviceRepository.getServicePricingName(sub.getId());

        boolean isPersonal = userService.isPersonal(AuthUtil.getCurrentUserId());
        User userUseSub = userRepository.findUserById(sub.getUserId()).get();
        User receiverDev = userRepository.getProviderByService(sub.getId());
        List<User> receiverAdmin = userRepository.getListAdminProvince(userUseSub.getProvinceId());
        Pricing pricing = pricingService.findByIdAndDeletedFlag(sub.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
        ServiceEntity subPricingService = servicesService.findByIdAndDeletedFlag(pricing.getServiceId(), DeletedFlag.NOT_YET_DELETED.getValue());
        List<User> userDevAdmin = userRepository.getAllDevAdmin(userSub.getId());
        List<User> userSmeAdmin = userRepository.getAllSmeAdmin(userSub.getId());
        if (portal.equals(PortalType.SME)) {
            String actorName;
            String[] paramsTitle;
            String[] paramsContent;
            String title;
            String content;
            String userName;

            //gửi thông báo cho SME khi SME tự kích hoạt lại thuê bao
            paramsContent = new String[]{servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName()};
            title = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB32.getTitle(), null)
                : NotifyUtil.getContent(ActionNotificationEnum.SC32.getTitle(), null);
            content = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB32.getContent(), paramsContent)
                : NotifyUtil.getContent(ActionNotificationEnum.SC32.getContent(), paramsContent);
            sendNotify(
                title, content,
                isPersonal ? ActionNotificationEnum.TB32 : ActionNotificationEnum.SC32,
                PortalType.SME, sub.getId(),
                AuthUtil.getCurrentUserId()
            );

            //gửi thông báo cho nhà cung cấp khi SME tự kích hoạt lại thuê bao
            actorName = isPersonal ? "Khách hàng" : "Doanh nghiệp";
            paramsTitle = new String[]{actorName};
            title = NotifyUtil.getContent(ActionNotificationEnum.SC35.getTitle(), paramsTitle);
            userName = CustomerOrgTypeEnum.CN.getValue().equals(userUseSub.getCustomerType()) ? userUseSub.getLastName() + " " +
                userUseSub.getFirstName() : userUseSub.getName();
            paramsContent = new String[]{actorName, userName, servicePricingInfo.getServiceName()};
            content = NotifyUtil.getContent(ActionNotificationEnum.SC35.getContent(), paramsContent);
            sendNotify(title, content, ActionNotificationEnum.SC35, PortalType.DEV, sub.getId(), receiverDev.getId());

            if (Objects.nonNull(sub.getAssigneeId())) {
                User customer = userRepository.findUserById(sub.getUserId()).orElse(null);
                Subscription reactiveSub = sub;
                userRepository.findByIdAndDeletedFlag(sub.getAssigneeId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .ifPresent(assignee -> actionNotificationService.send(new SCD07(assignee, customer, reactiveSub)));
            }
            //gửi thông báo cho admin cùng tỉnh vs sme
            for (User user : receiverAdmin) {
                sendNotify(title, content, ActionNotificationEnum.SC32, PortalType.ADMIN, sub.getId(), user.getId());
            }

            emailService.sendMailAdminProvinceReactive(userSmeAdmin, sub, userSub, subPricingService, pricing, portal.getValue(), isPersonal,
                isPersonal ? EmailCodeEnum.TB32 : EmailCodeEnum.SC32, SME);
            // gửi cho nhà cung cấp dịch vụ
            subscriptionHelperService.sendMailReactive(userSub, null, receiverDev, sub,
                isPersonal ? EmailCodeEnum.TB35 : EmailCodeEnum.SC35, PortalType.SME.getValue(), isPersonal, DEV);
        } else {
            if (portal.equals(PortalType.DEV)) {
                emailService.sendMailAdminProvinceReactive(userDevAdmin, sub, userSub, subPricingService, pricing, portal.getValue(), isPersonal,
                    isPersonal ? EmailCodeEnum.TB33 : EmailCodeEnum.SC33, DEV);
            }

            String actorName;
            String[] paramsContent;
            String title;
            String content;

            //gửi thông báo cho dev/admin khi Dev/Admin kích hoạt lại thuê bao
            title = NotifyUtil.getContent(ActionNotificationEnum.TB33.getTitle(), null);
            //kiểm tra người kích hoạt lại có phải devAdmin ko
            List<User> lstUserDevAdmin = userRepository.getAllDevAdmin(AuthUtil.getCurrentUserId());
            if (!lstUserDevAdmin.isEmpty()) {
                actorName = "Quản trị viên";
            } else {
                User userCurent = userRepository.findFirstById(AuthUtil.getCurrentUserId());
                actorName = CustomerOrgTypeEnum.CN.getValue().equals(userCurent.getCustomerType()) ? "Bạn" : "Doanh nghiệp của bạn";
            }
            paramsContent = new String[]{actorName, servicePricingInfo.getServiceName(), servicePricingInfo.getPricingName(),
                CustomerOrgTypeEnum.CN.getValue().equals(userUseSub.getCustomerType()) ? userUseSub.getLastName() + " " +
                    userUseSub.getFirstName() : userUseSub.getName()};

            content = NotifyUtil.getContent(ActionNotificationEnum.TB33.getContent(), paramsContent);
            //gửi cho Dev trực tiếp kích hoạt lại thuê bao
            sendNotify(title, content, ActionNotificationEnum.TB33, PortalType.DEV, sub.getId(), AuthUtil.getCurrentUserId());
            //gửi cho Devadmin
            if (!lstUserDevAdmin.isEmpty()) {
                for (User userDevAdminNoti : lstUserDevAdmin) {
                    sendNotify(title, content, ActionNotificationEnum.TB33, PortalType.DEV, sub.getId(), userDevAdminNoti.getId());
                }
            }
            //gửi cho SME khi đc kích hoạt lại thuê bao
            String[] paramTitle = new String[]{portal.equals(PortalType.DEV) ? "Nhà cung cấp" : "Quản trị viên"};
            title = NotifyUtil.getContent(ActionNotificationEnum.TB34.getTitle(), paramTitle);
            paramsContent = new String[]{portal.equals(PortalType.DEV) ? "Nhà cung cấp" : "Quản trị viên", servicePricingInfo.getServiceName()};
            content = isPersonal ? NotifyUtil.getContent(ActionNotificationEnum.TB34.getContent(), paramsContent)
                : NotifyUtil.getContent(ActionNotificationEnum.SC34.getContent(), paramsContent);
            sendNotify(title, content, ActionNotificationEnum.TB34, PortalType.SME, sub.getId(), AuthUtil.getCurrentUserId());

            subscriptionHelperService.sendMailReactive(userSub, null, null, sub,
                isPersonal ? EmailCodeEnum.TB33 : EmailCodeEnum.SC33, PortalType.SME.getValue(), isPersonal, DEV);
            // gửi cho SME
            subscriptionHelperService.sendMailReactive(userSub, null, userUseSub, sub,
                isPersonal ? EmailCodeEnum.TB34 : EmailCodeEnum.SC34, portal.getValue(), isPersonal, SME);
        }
        if (receiverAdmin != null) {
            emailService.sendMailAdminProvinceReactive(receiverAdmin, sub, userSub, subPricingService, pricing, portal.getValue(), isPersonal,
                isPersonal ? EmailCodeEnum.TB35 : EmailCodeEnum.SC35, ADMIN);
        }

        // Lưu lịch sử thay đổi trạng thái thuê bao ( gọi sang SPDV)
        subscriptionHistoryService.saveStatusHistory(sub);
        if (!StringUtils.isEmpty(token)) {
            integrationService.transactionOneSME(token, sub, pricingDB,
                IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION, null, null, null, false, null);
        }
        // Call API update Subscription DHSXKD
        if (executiveProducerService.checkCallApiDHSXKD(sub)) {
            executiveProducerService.callApiUpdateSubDHSXKD(subscriptionRepository.getProvinceIdByCreateBy(sub.getUserId()),
                sub, 1, sub.getSubscriptionContractId(), null);
        }
    }

    private void sendNotify(String title, String content, ActionNotificationEnum notificationEnum, PortalType portalType, Long subId,
        Long receiverUserId) {
        NotificationDTO notificationDTO = new NotificationDTO(title,
            content,
            notificationEnum.getScreentId(),
            receiverUserId,
            portalType.getType(), subId);
        NotifyUtil.sendNotify(notificationDTO, notificationEnum.getCode());
    }


    @Override
    public Boolean activateSubscriptionIntegration(Subscription sub, Pricing pricing,
        IntegrationActionTypeEnum actionType, IntegrationOneSmeDTO paramDTO,
        KafkaMessageResponseDTO kafkaMessageResponseDTO) {
        log.info("==============START REACTIVE SUBSCRIPTION KAFKA==============");
        if (!validateActiveSubIntegrate(sub, actionType, paramDTO,
            kafkaMessageResponseDTO)) {
            return false;
        }
        sub.setStatus(SubscriptionStatusEnum.ACTIVE.value);
        if (Objects.isNull(sub.getCurrentCycle())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("CURRENT_CYCLE_FIELD_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        sub.setCurrentCycle(sub.getCurrentCycle() + 1);
        sub.setStartCurrentCycle(new Date());

        if (Objects.isNull(pricing.getNumberOfCycles())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("NUMBER_OF_CYCLE_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        if (Objects.isNull(pricing.getPaymentCycle())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("PAYMENT_CYCLE_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        if (Objects.isNull(pricing.getCycleType())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("CYCLE_TYPE_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }

        if (pricing.getNumberOfCycles().compareTo(-1) != 0) {
            Integer cycle = pricing.getNumberOfCycles() - sub.getCurrentCycle();
            LocalDate newEndCurrentCycle = DateUtil
                .calculateCycleDate(new Date(), pricing.getPaymentCycle(),
                    CycleTypeEnum.valueOf(pricing.getCycleType()), true, cycle);
            sub.setEndCurrentCycle(DateUtil.toDate(newEndCurrentCycle));
        } else {
            sub.setEndCurrentCycle(null);
        }
        sub = subscriptionRepository.save(sub);
        // Lưu lịch sử thay đổi trạng thái thuê bao
        subscriptionHistoryService.saveStatusHistory(sub);
        log.info("==============END REACTIVE SUBSCRIPTION KAFKA==============");
        return true;
    }

    @Override
    public Map<String, Object> activateSubscriptionIntegrate(Long subscriptionId, KafkaSecretParamDTO secretParams, PortalType portal) {
        Map<String, Object> objectMap = new LinkedHashMap<>();
        Optional<Subscription> subscription = getSubscriptionFromParam(secretParams);
        activateSubscription(subscription.get().getId(), portal, null, null);
        Pricing pricing = pricingService.findByIdAndDeletedFlag(subscription.get().getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
        if (Objects.isNull(pricing.getNumberOfCycles())) {
            throw throwSubscriptionBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Pricing.NUMBER_OF_CYCLE);
        }

        objectMap.put("id", subscription.get().getId());
        if (pricing.getNumberOfCycles().compareTo(-1) == 0) {
            objectMap.put("periodRemain", -1);
        } else {
            objectMap.put("periodRemain", pricing.getNumberOfCycles() - subscription.get().getCurrentCycle());
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
        objectMap.put("reActiveDate", sdf.format(subscription.get().getStartCurrentCycle()));
        objectMap.put("expiredDate", Objects.isNull(subscription.get().getEndCurrentCycle())
            ? "Unlimited" :
            sdf.format(subscription.get().getEndCurrentCycle()));

        return objectMap;
    }

    @Override
    public RegisterDHSXKDResponseDTO activateSubscriptionIntegrateOneBSS(ShoppingCartDHSXKDReqDTO reqDTO, PortalType portal) {
        RegisterDHSXKDResponseDTO response = new RegisterDHSXKDResponseDTO(HttpStatus.OK.value());
        // check secureCode để đảm bảo dữ liệu toàn vẹn và hợp lệ
        subscriptionService.validateSecureCode(reqDTO);

        String dhsxkdSubCode = reqDTO.getUserDHSXKDReqDTO().getSubCode();
        // check và get thông tin thuê bao
        Subscription subscription = subscriptionRepository.findSubscriptionByDHSXKDSubCode(
            ObjectUtil.getOrDefault(dhsxkdSubCode, CharacterConstant.BLANK)).orElse(null);
        // check input sub và trả về mã lỗi tương ứng
        if (Objects.isNull(subscription)) {
            response.setStatus(-1);
            response.setErrorCode(String.valueOf(HttpStatus.BAD_REQUEST.value()));
            response.setErrorMsg(MessageKeyConstant.SUBSCRIPTION_CODE_NOT_VALID);
            return response;
        } else if (!Objects.equals(subscription.getStatus(), SubscriptionStatusEnum.CANCELED.value)) {
            response.setStatus(-1);
            response.setErrorCode(String.valueOf(HttpStatus.BAD_REQUEST.value()));
            response.setErrorMsg(MessageKeyConstant.SUBSCRIPTION_ACTIVATED);
            return response;
        }

        // lấy thông tin khách hàng
        User user = userRepository.findFirstByCorporateTaxCode(reqDTO.getUserDHSXKDReqDTO().getTin().replace(CharacterConstant.HYPHEN, ""));

        // kích hoạt lại thuê bao tương ứng
        activateSubscription(subscription.getId(), portal, null, user.getId());

        return response;
    }

    /**
     * Throw subscription bad request bad request exception.
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     * @return the bad request exception
     */
    public BadRequestException throwSubscriptionBadRequest(String messageKeyConstant, String errorKey) {
        return exceptionFactory.badRequest(messageKeyConstant, Resources.SUBSCRIPTION, errorKey);
    }

    /**
     * Validate province admin.
     *
     * @param userId the user id
     */
    private void validateProvinceAdmin(Long userId) {
        Set<String> roles = AuthUtil.getLoggedInUser()
            .getAuthorities().stream().map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());
        if (roles.contains(RoleType.ADMIN.getValue()) || roles.contains(RoleType.FULL_ADMIN.getValue())) {
            Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
            Long provinceIdSub = departmentsRepository.getProvinceIdByUserId(userId);

            // Trong case ADMIN tinh tao
            if (Objects.nonNull(provinceIdSub)) {
                //=> Admin tong khong duoc phep chinh sua
                //=> Admin tỉnh khac khong duoc phep chỉnh sua
                if (Objects.isNull(provinceId) || !Objects.equals(provinceId, provinceIdSub)) {
                    throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
                }
            }

            // Trong case ADMIN tong tao
            //=> Admin tinh khong duoc chinh sua
            if (Objects.isNull(provinceIdSub) && Objects.nonNull(provinceId)) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }
    }

    /**
     * Throw addon exception.
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     */
    private BadRequestException throwBillingBadRequest(String messageKeyConstant, String errorKey) {
        String message = messageSource.getMessage(messageKeyConstant, new Object[]{errorKey},
            LocaleContextHolder.getLocale());
        return new BadRequestException(message, Resources.BILL, errorKey, messageKeyConstant);
    }

    /**
     * validate active sub
     */
    private Boolean validateActiveSubIntegrate(Subscription subscription,
        IntegrationActionTypeEnum actionType, IntegrationOneSmeDTO paramDTO,
        KafkaMessageResponseDTO kafkaMessageResponseDTO) {
        if (billsRepository.checkBillsOutOfDate(subscription.getId())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("BILL_OUT_OF_DATE");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        Integer activeDate = pricingRepository.getActiveDateBySubscription(subscription.getId());
        if (Objects.isNull(activeDate)) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("ACTIVE_DATE_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        if (Objects.isNull(subscription.getCancelledTime())) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("CANCEL_TIME_MUST_BE_NOT_NULL");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        LocalDate cancelTime = subscription.getCancelledTime().toLocalDate();
        Period diff = Period.between(cancelTime, LocalDate.now());
        if (activeDate != -1 && diff.getDays() > activeDate) {
            paramDTO.setActionType(actionType.value);
            paramDTO.setStatus(400);
            paramDTO.setMessage("NOT_ACTIVATE_AGAIN");
            sendMessageKafka(kafkaMessageResponseDTO, paramDTO);
            return false;
        }
        return true;
    }

    /**
     * send message kafka
     */
    private void sendMessageKafka(KafkaMessageResponseDTO kafkaMessageResponseDTO,
        IntegrationOneSmeDTO paramDTO) {
        IntegrationKafkaDTO integrationKafkaDTO = IntegrationKafkaDTO.builder()
            .kafkaMessageId(kafkaMessageResponseDTO.getKafkaMessageId())
            .body(paramDTO).build();
        subscriptionProducer.transactionSubscription(integrationKafkaDTO,
            kafkaMessageResponseDTO.getBody().getTopic(), null);
    }

    /**
     * validate subscription by api key and secret key
     *
     * @return Subscription optional
     */
    private Optional<Subscription> getSubscriptionFromParam(KafkaSecretParamDTO secretParams) {
        Long subParamId = secretParams.getSubscriptionId();
        String apiKey = secretParams.getApiKey();
        String secretKey = secretParams.getSecretKey();
        Optional<Subscription> subscriptionParam = subscriptionFormula.validateAndGetSubscriptionFromParam(subParamId, apiKey, secretKey, null);
        if (!subscriptionParam.isPresent()) {
            String msg = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                new Long[]{subParamId}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.SUBSCRIPTION,
                ErrorKey.Subscription.ID, MessageKeyConstant.NOT_FOUND);
        }
        return subscriptionParam;
    }
}
