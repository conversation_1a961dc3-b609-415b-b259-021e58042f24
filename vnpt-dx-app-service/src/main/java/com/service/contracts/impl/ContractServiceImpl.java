package com.service.contracts.impl;

import com.dto.contracts.ContractDTO;
import com.dto.contracts.ContractParamDTO;
import com.dto.contracts.ContractViewDTO;
import com.entity.contracts.Contract;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.mapper.ContractMapper;
import com.repository.contracts.ContractRepository;
import com.service.contracts.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR> KienND2
 * @version    : 1.0
 * 19/1/2021
 */
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ContractMapper contractMapper;

    @Autowired
    private MessageSource messageSource;

    @Override
    public void deleteContract(Long id) {
        Optional<Contract> contract = contractRepository.findById(id);
        if (!contract.isPresent()) {
            final String [] contractMessage = {"contract"};
            String message = messageSource.getMessage("error.object.not.found", contractMessage, null);
            throw new ResourceNotFoundException(message, Resources.CONTRACT, ErrorKey.ID, "error.object.not.found");
        }
        contractRepository.deleteContract(id);
    }

    @Override
    public ContractDTO save(ContractDTO contractDTO) {
        return contractMapper.toDto(contractRepository.save(contractMapper.toEntity(contractDTO)));
	}
    
    @Override
    public void extendContract(Long id) {
    	Optional<Contract> contract = contractRepository.findById(id);
        if (!contract.isPresent()) {
            final String [] contractMessage = {"contract"};
            String message = messageSource.getMessage("error.object.not.found", contractMessage, null);
            throw new ResourceNotFoundException(message, Resources.CONTRACT, ErrorKey.ID, "error.object.not.found");
        }
        contractRepository.extendContract(id);
    }

    @Override
    public Page<ContractViewDTO> getContracts(ContractParamDTO contractParamDTO, Pageable pageable) {
        return contractRepository.findContractByTypeOrAmount(contractParamDTO, pageable);
    }
}
