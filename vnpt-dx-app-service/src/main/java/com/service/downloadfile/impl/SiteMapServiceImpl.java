package com.service.downloadfile.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.common.props.SiteMapChildProps;
import com.common.props.SiteMapConst;
import com.common.props.SiteMapConst.DynamicWeb;
import com.onedx.common.constants.values.CharacterConstant;
import com.constant.SeoTypeCodeConstant;
import com.dto.site_map.SiteMapDTO;
import com.repository.categories.CategoryRepository;
import com.repository.combo.ComboRepository;
import com.repository.services.ServiceRepository;
import com.service.downloadfile.SiteMapService;
import lombok.extern.slf4j.Slf4j;

import static com.onedx.common.helpers.sendSms.SendSmsUtils.replaceVietnamese;

/**
 * <AUTHOR> HuyLD
 * @version : 1.0 11/10/2021
 */
@Service
@Slf4j
public class SiteMapServiceImpl implements SiteMapService {

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ServiceRepository serviceRepository;

    @Autowired
    private ComboRepository comboRepository;

    @Override
    public List<SiteMapChildProps> getListDynamicWebSystem() {
        List<SiteMapChildProps> propsList = new ArrayList<>();

        List<SiteMapDTO> siteMapServicesAndCategories = categoryRepository.getAllObjectIdAndSeoId();
        siteMapServicesAndCategories.forEach(e -> {
            String url = "";
            String freq = SiteMapConst.ATTRIBUTE_NEVER;
            switch (e.getObjectType()) {
                case SeoTypeCodeConstant.CAU_HINH_TRANG_CHU:
                case SeoTypeCodeConstant.CAU_HINH_DANG_NHAP:
                case SeoTypeCodeConstant.CAU_HINH_DANG_KY:
                case SeoTypeCodeConstant.CAU_HINH_TAT_CA_SAN_PHAM:
                case SeoTypeCodeConstant.CAU_HINH_TAT_CA_COMBO:
                case SeoTypeCodeConstant.CAU_HINH_LIEN_HE:
                    url = Objects.isNull(e.getPlanUrl()) ? "" :  CharacterConstant.SLASH + e.getPlanUrl();
                    freq = SiteMapConst.ATTRIBUTE_ALWAYS;
                    break;
                case SeoTypeCodeConstant.CAU_HINH_DANH_MUC:
                    freq = SiteMapConst.ATTRIBUTE_ALWAYS;
                    url = MessageFormat.format(DynamicWeb.CATEGORY_URL, e.getObjectId(), e.getPlanUrl());
                    break;
                case SeoTypeCodeConstant.CAU_HINH_DICH_VU:
                    freq = SiteMapConst.ATTRIBUTE_ALWAYS;
                    url = MessageFormat.format(DynamicWeb.SERVICE_URL, e.getObjectId(), e.getPlanUrl()).toLowerCase().concat(SiteMapConst.SUFFIX_SERVICE);
                    break;
                case SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU:
                    freq = SiteMapConst.ATTRIBUTE_ALWAYS;
                    url = MessageFormat.format(DynamicWeb.PRICING_URL, e.getServiceId(), e.getObjectId(), e.getPlanUrl()).toLowerCase();
                    break;
                default:
                    break;
            }
            propsList.add(new SiteMapChildProps(replaceVietnamese(url), freq));
        });
        return propsList;
    }
}
