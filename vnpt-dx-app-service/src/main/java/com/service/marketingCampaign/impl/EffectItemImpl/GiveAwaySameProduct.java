package com.service.marketingCampaign.impl.EffectItemImpl;

import static com.constant.enums.marketingCampaign.McPromotionScopeEnum.WHOLE;
import static com.constant.enums.marketingCampaign.McPurchaseTimeTypeEnum.NEXT;
import static com.constant.enums.marketingCampaign.McRejectCauseEnum.SUCCESS;

import com.constant.enums.marketingCampaign.McDetailEffectTypeEnum;
import com.constant.enums.marketingCampaign.McPurchaseTimeTypeEnum;
import com.constant.enums.marketingCampaign.McRejectCauseEnum;
import com.dto.marketingCampaign.effect.McEffectInfo;
import com.dto.marketingCampaign.effect.McGiveAwayEffect;
import com.service.marketingCampaign.impl.ApplyEffectResult;
import com.service.marketingCampaign.impl.McApplyCampaignDetailInfo;
import com.service.marketingCampaign.impl.McPaymentResultDetailInfo;
import com.service.marketingCampaign.impl.McPurchaseItemDetailInfo;
import com.service.utils.values.ValueManager;

public class GiveAwaySameProduct extends EffectItemBase {

    private final ValueManager.CGiveAwayType giveAwayType;
    private final ValueManager.CNumberGift numberGift;
    private final ValueManager.CNumberUnitGift numberUnitGift;
    private final ValueManager.CUseGiftTime useGiftTime;
    private final ValueManager.CPurchaseTime purchaseTime;

    public GiveAwaySameProduct(Long id,
        ValueManager.CGiveAwayType giveAwayType,
        ValueManager.CNumberGift numberGift,
        ValueManager.CNumberUnitGift numberUnitGift,
        ValueManager.CUseGiftTime useGiftTime,
        ValueManager.CListCouponMC combineCouponMc,
        int combineCouponMcOperator,
        ValueManager.CApplyTime applyTime,
        ValueManager.CNumberApplyMC maxNumberApply,
        ValueManager.CPurchaseTime purchaseTime) {
        super(id, combineCouponMc, combineCouponMcOperator, applyTime, maxNumberApply);
        this.giveAwayType = giveAwayType;
        this.numberGift = numberGift;
        this.numberUnitGift = numberUnitGift;
        this.useGiftTime = useGiftTime;
        this.purchaseTime = purchaseTime;
    }

    @Override
    public ApplyEffectResult apply(McApplyCampaignDetailInfo mcApplyCampaignDetailInfo) {
        ApplyEffectResult applyEffectResult = new ApplyEffectResult();
        try {
            if (mcApplyCampaignDetailInfo.scope == WHOLE) {
                return applyEffectResult;
            }

            McRejectCauseEnum check = checkApplySatisfied(mcApplyCampaignDetailInfo);
            if (check == SUCCESS) {
                // When user get discount ?
                McPurchaseTimeTypeEnum applyPurchaseTime = purchaseTime.value;
                if (applyPurchaseTime == NEXT) {
                    applyPurchaseTime = determineWhenApplyPromotion(mcApplyCampaignDetailInfo.customerId,
                            mcApplyCampaignDetailInfo.subscriptionId);
                }

                Long numUnit;
                if (numberUnitGift != null && numberUnitGift.value != null) {
                    numUnit = numberUnitGift.value;
                } else {
                    numUnit = mcApplyCampaignDetailInfo.appliedItem.numUnit;
                }

                McGiveAwayEffect giveAwayEffect = new McGiveAwayEffect(giveAwayType.value,
                    applyPurchaseTime,
                    numberGift.value,
                    numUnit,
                    useGiftTime == null ? null : useGiftTime.value);

                applyEffectResult.effectItemSetId = super.itemSetId;
                applyEffectResult.effectItemId = super.id;
                applyEffectResult.giveAwayInfo = giveAwayEffect;
                applyEffectResult.allowableCombineCoupon = super.allowCombineCouponList;
                applyEffectResult.allowableCombineMc = super.allowCombineMcList;
                applyEffectResult.rejectCause = SUCCESS;
            } else {
                applyEffectResult.rejectCause = check;
            }
            return applyEffectResult;
        } catch (Exception e) {
            e.printStackTrace();
            return applyEffectResult;
        }
    }

    @Override
    public McEffectInfo getEffectForPurchaseItem(McPurchaseItemDetailInfo purchaseItemDetailInfo) {
        try {
            if (purchaseItemDetailInfo.getScope() == WHOLE) {
                return null;
            }

            boolean check = checkPurchaseItemSatisfied(purchaseItemDetailInfo);
            if (check) {
                McGiveAwayEffect giveAwayEffect = new McGiveAwayEffect(giveAwayType.value,
                    null,
                    numberGift.value,
                    null,
                    useGiftTime == null ? null : useGiftTime.value);
                return new McEffectInfo(giveAwayEffect);
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void confirmApplied(McPaymentResultDetailInfo paymentResultDetailInfo) {
        super.confirmWithApplyPurchaseTime(paymentResultDetailInfo, purchaseTime);
    }

    @Override
    public McDetailEffectTypeEnum getDetailEffectType() {
        return McDetailEffectTypeEnum.GIVE_AWAY_SAME_PRODUCT;
    }

    @Override
    public String getDisplayText() {
        StringBuilder builder = new StringBuilder();
        if(numberGift != null) {
            builder.append("tặng thêm ").append(numberGift).append(" sản phẩm cùng loại");
        }

        if (purchaseTime.value == McPurchaseTimeTypeEnum.NEXT) {
            builder.append(", Khuyến mại được áp dụng cho lần mua hàng tiếp theo, ");
        }

        if (numberUnitGift != null && numberUnitGift.value != null) {
            builder.append(", ").append("tặng ").append(numberGift).append(" đơn vị");
        }

        if (useGiftTime != null && useGiftTime.value != null) {
            builder.append(", ").append("sử dụng dịch vụ tặng trong ").append(useGiftTime.value).append(" chu kỳ");
        }

        String couponMcDisplayText = getCombineCouponMCDisplayText();
        if (couponMcDisplayText != null) {
            builder.append(", ").append(couponMcDisplayText);
        }

        if(applyTimeValue != null && !applyTimeValue.isDefault()) {
            builder.append(", ").append(applyTimeValue);
        }

        if (maxNumberApply != null && maxNumberApply.value != null) {
            builder.append(", ").append("khuyến mại được áp dụng ").append(maxNumberApply.value).append(" lần");
        }

        return builder.toString();
    }

    @Override
    public String getEffectContentEmail() {
        StringBuilder builder = new StringBuilder();
        if(numberGift != null) {
            builder.append("tặng thêm ").append(numberGift).append(" sản phẩm cùng loại");
        } else {
            return null;
        }

        if (purchaseTime.value == McPurchaseTimeTypeEnum.NEXT) {
            builder.append("& khuyến mại được áp dụng cho lần mua hàng tiếp theo, ");
        }

        if (numberUnitGift != null && numberUnitGift.value != null) {
            builder.append("& ").append("tặng ").append(numberGift).append(" đơn vị");
        }

        if (useGiftTime != null && useGiftTime.value != null) {
            builder.append("& ").append("sử dụng dịch vụ tặng trong ").append(useGiftTime.value).append(" chu kỳ");
        }

        String couponMcDisplayText = getCombineCouponMCDisplayText();
        if (couponMcDisplayText != null) {
            builder.append("& ").append(couponMcDisplayText);
        }

        if(applyTimeValue != null && !applyTimeValue.isDefault()) {
            builder.append("& ").append(applyTimeValue);
        }

        if (maxNumberApply != null && maxNumberApply.value != null) {
            builder.append("& ").append("khuyến mại được áp dụng ").append(maxNumberApply.value).append(" lần");
        }

        return builder.toString();
    }
    
    @Override
    public String getBriefPromotionText() {
        return String.format("Tặng %s sản phẩm cùng loại", numberGift.toString());
    }
}
