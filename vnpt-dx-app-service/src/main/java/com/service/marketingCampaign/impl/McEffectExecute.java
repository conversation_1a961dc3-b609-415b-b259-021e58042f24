package com.service.marketingCampaign.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import com.constant.enums.marketingCampaign.McDetailEffectTypeEnum;
import com.constant.enums.marketingCampaign.McEffectTypeEnum;
import com.dto.marketingCampaign.effect.McEffectInfo;
import com.dto.marketingCampaign.jsonObject.McEffectGroupDTO;
import org.apache.logging.log4j.util.Strings;

public class McEffectExecute {

    private final List<McEffectGroupDTO> lstEffectItemSetDTO;
    private final List<McEffectItemSetExecute> lstEffectItemSetExecute;
    private final long ruleId;
    private final long activityId;
    private final long mcId;

    public McEffectExecute(Long mcId, Long activityId, Long ruleId, List<McEffectGroupDTO> lstEffectItemSet) {
        this.lstEffectItemSetDTO = lstEffectItemSet;
        this.mcId = mcId;
        this.activityId = activityId;
        this.ruleId = ruleId;
        this.lstEffectItemSetExecute = new ArrayList<>();

        parseJson();
    }

    private void parseJson() {
        for (McEffectGroupDTO itemSetJson : lstEffectItemSetDTO) {
            McEffectItemSetExecute mcItemSet = new McEffectItemSetExecute(mcId, activityId, ruleId, itemSetJson);
            lstEffectItemSetExecute.add(mcItemSet);
        }
        // Sort by id
        lstEffectItemSetExecute.sort(Comparator.comparingLong(McEffectItemSetExecute::getId));
    }

    public List<McEffectInfo> getEffectForPurchaseItem(McPurchaseItemDetailInfo purchaseItemDetailInfo) {
        try {
            List<McEffectInfo> effectInfoList = new ArrayList<>();
            // Apply all sets
            for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
                McEffectInfo effectInfo = itemSet.getEffectForPurchaseItem(purchaseItemDetailInfo);
                if (effectInfo != null) {
                    effectInfoList.add(effectInfo);
                }
            }

            return effectInfoList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ApplyEffectResult> apply(McApplyCampaignDetailInfo mcApplyCampaignDetailInfo) {
        try {
            List<ApplyEffectResult> applyEffectResultList = new ArrayList<>();
            // Apply all sets
            for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
                ApplyEffectResult effectResult = itemSet.apply(mcApplyCampaignDetailInfo);
                if (effectResult != null) {
                    applyEffectResultList.add(effectResult);
                }
            }
            return applyEffectResultList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void confirmApplied(McPaymentResultDetailInfo paymentResultDetailInfo) {
        for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
            if (itemSet.getId() == paymentResultDetailInfo.effectItemSetId) {
                itemSet.confirmApplied(paymentResultDetailInfo);
                break;
            }
        }
    }

    public List<List<String>> getDisplayText() {
        List<List<String>> andDisplayText = new ArrayList<>();
        for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
            List<String> orDisplayText = itemSet.getDisplayText();
            if (orDisplayText != null && orDisplayText.size() > 0) {
                andDisplayText.add(orDisplayText);
            }
        }
        return andDisplayText;
    }

    public List<List<String>> getEffectContentEmail() {
        List<List<String>> andDisplayText = new ArrayList<>();
        for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
            List<String> orDisplayText = itemSet.getEffectContentEmail();
            if (orDisplayText != null && orDisplayText.size() > 0) {
                andDisplayText.add(orDisplayText);
            }
        }
        return andDisplayText;
    }

    public McDetailEffectTypeEnum getDetailEffectType() {
        for (McEffectItemSetExecute itemSetExecute : lstEffectItemSetExecute) {
            McDetailEffectTypeEnum type = itemSetExecute.getDetailEffectType();
            if (type != null) {
                return type;
            }
        }
        return null;
    }

    public String getDiscountTag() {
        for (McEffectItemSetExecute itemSetExecute : lstEffectItemSetExecute) {
            String discountInfo = itemSetExecute.getDiscountTag();
            if (discountInfo != null) {
                return discountInfo;
            }
        }
        return null;
    }

    public String getGiftTag() {
        for (McEffectItemSetExecute itemSetExecute : lstEffectItemSetExecute) {
            String freeInfo = itemSetExecute.getGiftTag();
            if (freeInfo != null) {
                return freeInfo;
            }
        }
        return null;
    }

    public CombineCouponMc getCombineCouponMc(Long effectItemSetId, Long effectItemId) {
        for (McEffectItemSetExecute itemSetExecute : lstEffectItemSetExecute) {
            if (effectItemSetId.equals(itemSetExecute.getId())) {
                return itemSetExecute.getCombineCouponMc(effectItemId);
            }
        }
        return null;
    }

    public McEffectTypeEnum getPromotionTypeOfEffect(Long effectItemSetId, Long effectItemId) {
        for (McEffectItemSetExecute itemSetExecute : lstEffectItemSetExecute) {
            if (effectItemSetId.equals(itemSetExecute.getId())) {
                return itemSetExecute.getPromotionTypeOfEffect(effectItemId);
            }
        }
        return null;
    }

    public String getBriefPromotionText() {
        StringBuilder builder = new StringBuilder();

        for (McEffectItemSetExecute itemSet : lstEffectItemSetExecute) {
            List<String> orDisplayText = itemSet.getBriefPromotionText();
            if (orDisplayText != null && orDisplayText.size() > 0) {
                if (builder.length() > 0) {
                    builder.append(" VÀ ");
                }
                builder.append(Strings.join(orDisplayText, ';'));
            }
        }
        return builder.toString();
    }
}
