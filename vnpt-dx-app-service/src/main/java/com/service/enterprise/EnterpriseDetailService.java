package com.service.enterprise;

import com.entity.file.attach.FileAttach;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.dto.enterprise.detail.*;
import com.dto.customerGroup.EnterprisesInGroupResponseDTO;
import com.onedx.common.constants.enums.enterprise.AccountTypeEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.dto.customerContact.ContactMessageDTO;
import com.onedx.common.dto.interestProduct.InterestProductDTO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface EnterpriseDetailService {
    /**
     * <PERSON><PERSON>y danh sách khách hàng hiện hữu
     */
    Page<CurrentEnterpriseRequestDTO> getListCurrentEnterprise(EnterpriseRequestDTO request);

    /**
     * Lấy danh sách khách hàng rời bỏ
     *
     * @param request  Thông tin sử dụng để lọc các khách hàng rời bỏ cần trả về
     * @param pageable Thông tin phân trang của dữ liệu trả về
     * @return Page<GetListEnterpriseResponseDTO>
     */
    Page<GetListEnterpriseResponseDTO> getListLeftEnterprise(GetListLeftEnterpriseRequestDTO request, Pageable pageable);

    /**
     * Lấy thông tin chi tiết doanh nghiệp
     *
     * @param enterpriseId ID của doanh nghiệp
     * @return GetEnterpriseBusinessDetailResponseDTO
     */
    EnterpriseBusinessDetailDTO getEnterpriseBusinessDetail(Long enterpriseId, CrmObjectTypeEnum objectTypeEnum);

    /**
     * Lấy thông tin liên hệ của doanh nghiệp
     *
     * @param enterpriseId ID của doanh nghiệp
     * @return GetEnterpriseContactDetailResponseDTO
     */
    GetEnterpriseContactDetailResponseDTO getEnterpriseContactDetail(Long enterpriseId);
    
    /**
     * Lấy thông tin tài khoản SME của doanh nghiệp
     *
     * @param enterpriseId ID của doanh nghiệp
     * @return GetEnterpriseSmeDetailResponseDTO
     */
    GetEnterpriseSmeDetailResponseDTO getEnterpriseSmeDetail(Long enterpriseId);

    /**
     * Xem danh sách chi tiết lịch sử thuê bao
     */
    Page<SubHistoryResponseDTO> getEnterpriseSubscriptionHistory(Long enterpriseId, Set<Long> lstServiceId, Set<Integer> lstSubsStatus, Integer createdSourceMigration, Pageable pageable);

    /**
     * Lấy thông tin lịch sử hóa đơn của doanh nghiệp
     *
     * @param enterpriseId  ID của doanh nghiệp
     * @param lstBillId     ID các hóa đơn cần lọc
     * @param lstServiceId  ID các dịch vụ cần lọc
     * @param lstBillStatus Các trạng thái hóa đơn cần lọc
     * @param pageable      Thông tin phân trang
     * @return Page<GetEnterpriseBillHistoryResponseDTO>
     */
    Page<GetEnterpriseBillHistoryResponseDTO> getEnterpriseBillHistory(Long enterpriseId, List<Long> lstBillId,
                                                                       List<Long> lstServiceId, List<Long> lstBillStatus,
                                                                       CreatedSourceMigrationEnum createdSourceMigration,
                                                                       Pageable pageable);

    /**
     * Xem danh sách khách hàng tiềm năng
     */
    Page<PotentialEnterpriseRequestDTO> getListPotentialEnterprise(EnterpriseRequestDTO request);

    /**
     * Xem chi tiết thông tin người đại diện
     */
    RepresentativeDetailDTO getRepresentativeDetail(Long enterpriseId);

    /**
     * Xem chi tiết nhật ký hoạt động
     */
    Page<EnterpriseActionHistoryDTO> getActionHistory(Long enterpriseId, Long userId, Set<Long> lstServiceId, Set<Integer> lstActionId,
        Set<String> lstContent, Date startDate, Date endDate, Integer actionType, Pageable pageable);

    /**
     * Lấy danh sách doanh nghiệp của nhóm
     */
    Page<EnterprisesInGroupResponseDTO> getListEnterpriseInGroup(Set<Long> lstEnterpriseId, Pageable pageable);

    Page<ListEnterpriseHaveAccountResponseDTO> getLstEnterpriseHaveAccount(Integer name, Integer email, Integer phone, Integer taxCode,
        Integer personalCertNumber, String value, Integer isLeakPWFilter, CustomerTypeEnum customerType, Long partitionId, Long assigneeId, AccountTypeEnum accountType,
        Date startDate, Date endDate, Date startDateSub, Date endDateSub, Pageable pageable);

    Page<IGetNotificationHistoryDTO> getListEmailSmsHistory(Long enterpriseId, Integer type, Date startDate, Date endDate, Pageable pageable);

    InputStreamResource exportCurrentEnterprise(EnterpriseRequestDTO request) throws SQLException, IOException;

    ByteArrayInputStream exportEnterpriseHaveAccount(ExportAccountRequestDTO request) throws SQLException, IOException;

    List<InterestProductDTO> getInterestProductInfo(Long enterpriseId);

    List<ContactMessageDTO> getMessageInfo(Long enterpriseId);

    List<FileAttach> getEnterpriseFileAttach(Long enterpriseId);
}
