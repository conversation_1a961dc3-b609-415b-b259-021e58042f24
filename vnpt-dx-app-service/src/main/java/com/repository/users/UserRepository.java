package com.repository.users;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.context.annotation.Description;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.constant.sql.SQLAccount;
import com.constant.sql.SQLAdminReport;
import com.constant.sql.SQLCombo;
import com.constant.sql.SQLComboBox;
import com.constant.sql.SQLUser;
import com.dto.affiliate.AffiliateCommonDTO;
import com.dto.bos.IProviderSearchResponseDTO;
import com.dto.common.CBBGetSmeValueDTO;
import com.dto.common.ICommonIdName;
import com.dto.enterprise.ComboboxResponseDTO;
import com.dto.notification.IFCMTokenDTO;
import com.dto.marketingCampaign.smePortal.McActivityIdxCoupon;
import com.dto.report.dashboardSme.AccountRegisterServiceByYearDTO;
import com.dto.report.dashboardSme.AccountSMERatioResponseDTO;
import com.dto.report.dashboardSme.SMERatioResponse;
import com.dto.report.dashboardSme.ServiceDevRegisReportResDTO;
import com.dto.report.dashboardSme.SmeEmployeeCountDTO;
import com.dto.report.dashboardSme.SmeEmployeeResDTO;
import com.dto.report.dashboardSme.SmePreviewEmployeeStatResDTO;
import com.dto.subscriptionUser.SubscriptionCustomerDTO;
import com.dto.subscriptions.CouponPopupItfDTO;
import com.dto.subscriptions.CustomerInfoDTO;
import com.dto.users.IDupplicateUserDTO;
import com.dto.users.IInvoiceUser;
import com.dto.users.IUserForUpdatePassword;
import com.dto.users.UserAdminDTO;
import com.dto.users.UserFilterInfoDTO;
import com.dto.users.UserSendMailDTO;
import com.dto.users.UserTransactionDTO;
import com.model.dto.UserInfoAddressDefault;
import com.model.dto.UserInfoReciverDHSX;
import com.model.dto.UserReceiveEmailSubsTemplateDTO;
import com.model.entity.security.User;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.onedx.common.dto.integration.backend.ClueInfoDTO;
import com.onedx.common.dto.integration.backend.IntegrationEmployeeDTO;
import com.onedx.common.dto.integration.backend.IntegrationRepresentDTO;
import com.onedx.common.dto.integration.backend.IntegrationSmeDTO;
import com.onedx.common.dto.users.INameAndEmailDTO;
import com.onedx.common.dto.users.UserDepartmentDTO;

/**
 * <AUTHOR> KienND2
 * @version : 1.0 19/1/2021
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    @Query(value = SQLUser.GET_PERMISSION_BY_ROLE_NAME, nativeQuery = true)
    List<String> getPermissionByRoleName (List<String> roleName);

    @Query(nativeQuery = true, value = SQLUser.GET_LST_EMPLOYEE)
    List<Long> getLstEmployee(Long currentUserId);
    @Query(value = SQLUser.CHECK_IS_ADMIN, nativeQuery = true)
    boolean isAdmin(Long userId);

    @Query(value = SQLUser.CHECK_IS_DEV, nativeQuery = true)
    boolean isDev(Long userId);

    @NonNull
    Page<User> findAll(@NonNull Pageable pageable);

    User findFirstById(Long id);

    Optional<User> findByIdAndDeletedFlag(@NonNull Long userId, Integer deletedFlag);

    Integer countByIdIn(List<Long> lstUserId);

    @Description("Hàm kiểm tra danh sach userId có user nào bị tắt hoạt động hay ko")
    boolean existsByIdInAndStatus(List<Long> lstUserId, Integer status);

    @Query(value = SQLUser.EXIST_BY_PHONE_AFF, nativeQuery = true)
    boolean existsByPhoneOfAff(String phone, Long userId, List<Long> roleAdminDevIds);

    @Query(value = SQLUser.EXIST_BY_TIN_AFF, nativeQuery = true)
    boolean existsByTinOfAff(String tin, Long userId);

    @Query(value = SQLUser.EXIST_BY_REP_PERSONAL_CERT_NUMBER_AFF, nativeQuery = true)
    boolean existsByRepPersonalCertNumberOfAff(String repPersonalCertNumber, Integer repPersonalCertTypeId, Long userId);

    @Query(value = SQLUser.EXIST_BY_BUSINESS_LICENSE_NUMBER_AFF, nativeQuery = true)
    boolean existsByBusinessLicenseNumberOfAff(String businessLicense, Long userId);

    @Query(value = SQLUser.FIND_USER_BY_DELETE_FLAG, nativeQuery = true)
    Optional<UserTransactionDTO> findByUserIdAndDeletedFlag(@NonNull Long userId, Integer deletedFlag);

    @Modifying
    @Query(value = SQLUser.DELETE_USER_ROLE_BY_USER_ID, nativeQuery = true)
    void deleteUserRoleByUserId (Long id);

    List<User> findAllByIdInAndDeletedFlag(Set<Long> userId, Integer deletedFlag);

    Optional<User> findByIdAndDeletedFlagAndStatus(Long id, Integer active, Integer status);

    @Query(value = "SELECT u.id FROM User u WHERE u.parentId = :parentId OR u.id = :parentId AND u.deletedFlag = 1")
    Set<Long> getIdsInCompany(@Param("parentId") Long parentId);

    Integer countByStatus(Integer status);

    @Query(value = SQLUser.COUNT_BY_ROLE, nativeQuery = true)
    Integer countByRoleId(@Param("roleId") Long roleId, @Param("status") Integer status);

    @Query(value = SQLUser.GET_USER_IN_LIST_IDS, nativeQuery = true)
    List<User> getAllUserInList(Set<Long> listIds);

    @Query(nativeQuery = true, value = SQLUser.REPORT_ALL_SME_EMPLOYEE_BY_IDS)
    List<SmeEmployeeResDTO> reportAllSmeEmployeeByIds(@Param("smeIds") List<Long> smeIds,
        boolean getFull);

    @Query(value = SQLUser.COUNT_USER_BETWEEN_CREAT_AT)
    Integer countUserBetweenCreateAt(@Param("firstDay") Date firstDay, @Param("endDay") Date endDay,
        @Param("status") Integer status);

    @Query(nativeQuery = true, value = SQLUser.GET_RATIO_REPORT_BY_MONTH_AND_PROVINCE_ID)
    SMERatioResponse getRatioReport(@Param("date") Date date, @Param("provinceId") Long provinceId);

    @Query(value = SQLUser.GET_ACCOUNT_RATIO_BY_YEAR, nativeQuery = true)
    AccountSMERatioResponseDTO getTotalAccountActiveByYear(@Param("year") String year,
        @Param("provinceId") Long provinceId);

    @Query(nativeQuery = true, value = SQLUser.REPORT_RATIO_DEVELOPER_REGISTER_BY_QUARTER)
    ServiceDevRegisReportResDTO ratioAccountDevRegisterServiceByQuarter(
        @Param("date") LocalDate date, @Param("provinceId") Integer provinceId
    );

    @Query(value = SQLUser.REPORT_RATIO_DEVELOPER_REGISTER_BY_YEAR, nativeQuery = true)
    AccountRegisterServiceByYearDTO ratioAccountDevRegisterServiceByYear(
        @Param("firstDay") Date firstDay,
        @Param("endDay") Date endDay, @Param("provinceId") Long provinceId);

    @Query(nativeQuery = true, value = SQLAdminReport.GET_TOTAL_ACCOUNT)
    BigDecimal getTotalAccount(@Param("month") String month,
        @Param("provinceId") String provinceId);

    @Query(nativeQuery = true, value = SQLAdminReport.GET_TOTAL_UNREGISTER)
    BigDecimal getTotalUnRegister(@Param("month") String month,
        @Param("provinceId") String provinceId);

    @Query(nativeQuery = true, value = SQLAdminReport.GET_TOTAL_UNRELEASE)
    BigDecimal getTotalUnRelease(@Param("month") String month,
        @Param("provinceId") String provinceId);

    @Query(nativeQuery = true, value = SQLAdminReport.GET_TOTAL_RELEASE)
    BigDecimal getTotalRelease(@Param("month") String month,
        @Param("provinceId") String provinceId);

    @Query(value = SQLUser.GET_ROLE, nativeQuery = true)
    List<String> getRole(@Param("userId") Long userId);

    @Query(value = SQLUser.GET_ROLE_DISPLAY_NAME, nativeQuery = true)
    List<String> getRoleDisplayName(@Param("userId") Long userId);

    @Query(value = SQLUser.GET_EMPLOYEE_PARENT_USER_SUBSCRIPTION, nativeQuery = true)
    Set<Long> getUserIdByParentIdAndServiceId(@Param("parentId") Long parentId);

    @EntityGraph(User.USER_WITH_FCM_TOKENS_GRAPH)
    @NonNull
    Optional<User> findById(@NonNull Long id);

    List<User> findByIdInAndDeletedFlag(List<Long> ids, Integer deletedFlag);

    Integer countByDepartmentIdAndStatus(Long departmentId, Integer status);

    @Query("UPDATE User u SET u.departmentId = null WHERE u.departmentId = :id")
    @Modifying
    void resetDepartmentId(@Param("id") Long id);

    @Query("UPDATE User u SET u.departmentId = :departmentId WHERE u.id IN :ids")
    @Modifying
    void updateDepartmentId(@Param("departmentId") Long departmentId, @Param("ids") List<Long> ids);

    @Query("UPDATE User u SET u.createdBy = :createdBy WHERE u.id IN :id")
    @Modifying
    void updateCreatedBy(@Param("createdBy") Long createdBy, @Param("id") Long id);

    @Query("SELECT COUNT(u.id) FROM User u WHERE u.id IN :ids AND u.status = 1")
    Integer countActiveUser(@Param("ids") List<Long> ids);

    @Query("SELECT COUNT(u.id) FROM User u WHERE u.id IN :ids AND u.status = 0")
    Integer countInactiveUser(@Param("ids") List<Long> ids);

    /**
     * Count by id in and parent id and status.
     *
     * @param ids    the ids
     * @param roleId the roleId
     * @return the int
     */
    @Query(value = SQLUser.GET_USER_IS_PARENT_BY_ID_ROLE_ID, nativeQuery = true)
    Set<User> getUserParentByIdsAndRoleId(Set<Long> ids, Long roleId);

    Optional<User> findByEmailAndPhoneNumberAndName(String email, String phoneNumber, String name);

    Integer countByEmail(String email);

    Integer countByName(String name);

    Integer countByPhoneNumber(String phoneNumber);

    Optional<List<User>> findByIdIn(Collection<Long> ids);

    @Query(SQLUser.GET_SME_FOR_INTEGRATION)
    IntegrationSmeDTO getSmeForIntegration(Long id);

    @Query(SQLUser.GET_SME_FOR_ERROR_INTEGRATION)
    IntegrationSmeDTO getSmeForErrorIntegration(Long id);

    @Query(SQLUser.GET_LIST_EMPLOYEE_FOR_INTEGRATION)
    List<IntegrationEmployeeDTO> listEmployeeSubscription(Long smeId, Long subscriptionId);

    @Query(SQLUser.GET_LIST_EMPLOYEE_OF_SME)
    List<IntegrationEmployeeDTO> listEmployee(Long smeId);

    @Query(value = SQLUser.GET_ADMIN_BY_SME_USER_ID, nativeQuery = true)
    Optional<User> getAdminBySmeUserId(Long id);

    @Query(value = SQLUser.GET_CUSTOMER_SME, nativeQuery = true)
    Optional<SubscriptionCustomerDTO> getCustomerSME(Long parentId, String phone);

    @Query(value = SQLUser.GET_CUSTOMER_SME_SUBSCRIPTION, nativeQuery = true)
    Optional<SubscriptionCustomerDTO> getCustomerSMESubscription(Long parentId);

    @Query(value = SQLUser.CHECK_COMPANY_ACTIVE, nativeQuery = true)
    boolean checkCompanyActive(Long companyId);

    @Query(value = SQLUser.GET_EMPLOYEE_USER_SUBSCRIPTION)
    List<IntegrationEmployeeDTO> getUsersByIdAndServiceIdAndSubscriptionId(@Param("uid") Set<Long> userIds,
            Long serviceId, Long subscriptionId);

    @Query(value = SQLUser.GET_INFO_CLUE, nativeQuery = true)
    Optional<ClueInfoDTO> getClueInfoOfSME(Long userId);

    @Query(value = SQLUser.GET_REPRESENT_INFO, nativeQuery = true)
    IntegrationRepresentDTO getRepresentInfo(Long userId);

    @Query(value = "SELECT u.provinceId FROM User u where u.id = :userId")
    Long getProvinceIdByUserId(Long userId);

    @Query(nativeQuery = true, value = SQLUser.GET_PROVINCE_ID_OF_DEPARTMENT_BY_USER_ID)
    Long getProvinceIdOfDepartmentByUser(@Param("userId") Long userId);

    Optional<User> findByIdAndStatus(Long id, Integer status);

    @Query(value = SQLUser.GET_PROVINCE_BY_USER_ID, nativeQuery = true)
    Optional<UserDepartmentDTO> getDepartmentSubByUserId(Long userId);

    @Deprecated
    @Query(value = SQLUser.GET_PARENT_ID, nativeQuery = true)
    Long getParentId(Long userId);

    @Query(value = SQLUser.GET_USER_ADMIN, nativeQuery = true)
    List<UserAdminDTO> getAllAdmin();

    @Query(value = SQLUser.GET_ALL_USER_ROLE_FULL_ADMIN, nativeQuery = true)
    List<UserAdminDTO> getAllUserRoleFullAdmin();

    @Query(value = SQLUser.GET_LST_FULL_ADMIN_DETAIL, nativeQuery = true)
    List<UserAdminDTO> getLstFullAdminDetail();

    @Query(value = SQLUser.GET_LST_ID_FULL_ADMIN, nativeQuery = true)
    Set<Long> getLstFullAdminId();

    @Query(value = SQLUser.GET_LST_NOTIF_PW03_FULL_ADMIN_USER_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail>  findLstNotifPW03FullAdminUserDetail(Set<Long> lstUserId);

    @Query(value = SQLUser.GET_LST_NOTIF_PW02_USER_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifPW02UserDetail(Set<Long> lstUserId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_ACCOUNT_USER_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCAccountUserDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_ASSIGNEE_USER_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCAssigneeDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_OTHER_USER_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCOtherUserDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_ADMIN_PARTITION_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCAdminPartitionDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_AM_PARTITION_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCAMPartitionDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_NOTIF_SC_ADMIN_AND_AM_PARTITION_DETAIL, nativeQuery = true)
    List<ICommonIdNameEmail> findLstNotifSCAdminAndAMPartitionDetail(Set<Long> lstReceiverId, Set<Long> lstSubId);

    @Query(value = SQLUser.GET_LIST_DETAIL_USER_FOR_ACTION_HISTORY, nativeQuery = true)
    List<ICommonIdNameEmail> getLstDetailUserForActionHistory(Set<Long> lstUserId);

    @Query(value = SQLUser.GET_NAME_DEV_BY_ID, nativeQuery = true)
    String getNameDevById(Long id);

    @Query(value = SQLUser.GET_PARENT_NAME_DEV_BY_USER_ID, nativeQuery = true)
    String getParentNameByUserId(Long userId);

    @Query(value = SQLUser.GET_NAME_ADMIN_BY_ID, nativeQuery = true)
    String getNameAdminById(Long id);

    @Query(value = SQLUser.CHECK_CAN_SEND_EMAIL, nativeQuery = true)
    boolean checkCanSendEmail(String code);

    @Query(value = SQLUser.GET_ALL_USER_BY_ROLE, nativeQuery = true)
    List<User> getAllUserByRoleNameIn(List<String> names);

    @Query(value = SQLUser.GET_ALL_VALID_USER_FOR_COUPON_EMAIL, nativeQuery = true)
    List<User> getAllValidUserForCouponEmail();

    @Query(nativeQuery = true, value = SQLUser.GET_ENTERPRISE_BY_COUPON_ID)
    List<User> getEnterpriseByCoupon(@Param("couponId")Long couponId);

    @Query(nativeQuery = true, value = SQLUser.GET_SUPPLIER_BY_COUPON_ID)
    List<User> getSupplierByCouponId(@Param("couponId") Long couponId);

    @Query(nativeQuery = true, value = SQLUser.GET_COUPON_OWNER)
    Optional<User> getCouponOwner(@Param("userId")Long userId);

    @Query(value = SQLUser.GET_LIST_USER_WITH_SAME_COMPANNY_BY_USERID, nativeQuery = true)
    List<User> getListUserWithSameCompannyByUserId(@Param("userId") Long userId);

    @EntityGraph(attributePaths = "roles")
    Optional<User> findUserById(Long id);

    @Query(value = SQLUser.GET_ALL_USER_FOR_SEND_NOTIFY_SUB, nativeQuery = true)
    List<UserReceiveEmailSubsTemplateDTO> getAllUserForSendNotifySub(
        @Param("provinceId") Long provinceId, @Param("userDevId") Long userDevId, @Param("departmentCode") String departmentCode);

    @Query(value = SQLUser.GET_DEV_USER_FOR_SEND_NOTIFY_SUB, nativeQuery = true)
    List<UserReceiveEmailSubsTemplateDTO> getDevUserForSendNotifySub(
        @Param("userDevId") Long userDevId);

    @Query(value = SQLUser.GET_ALL_PROVINCE_ADMIN_FOR_SEND_NOTIFY_SUB, nativeQuery = true)
    List<UserReceiveEmailSubsTemplateDTO> getAllProvinceAdminForSendNotifySub(
        @Param("provinceId") Long provinceId, @Param("departmentCode") String departmentCode);

    @Query(value = SQLUser.GET_USER_SME_FOR_SEND_NOTIFY_SUB, nativeQuery = true)
    UserReceiveEmailSubsTemplateDTO getUserSmeForSendNotifySub(@Param("userId") Long userId);

    @Query(value = SQLUser.GET_LIST_ADMIN_BY_PROVINCE_ID, nativeQuery = true)
    List<User> getListAdminByProvinceId(@Param("provinceId") Long provinceId, @Param("departmentCode")String departmentCode);

    @Query(value = SQLUser.GET_LIST_ADMIN_BY_PROVINCE_IDS)
    List<User> getListAdminByProvinceId(@Param("provinceIds") Long provinceIds);

    @Query(value = SQLUser.GET_SME_ADMIN_BY_ID, nativeQuery = true)
    List<User> getSmeAdminById(Long userId);

    @Query(nativeQuery = true, value = SQLCombo.GET_SERVICE_OWNER_BY_SUBS_ID)
    List<User> getServiceOwnerBySubsId(@Param("subsId") Long subsId);

    @Query(nativeQuery = true, value = SQLCombo.GET_COMBO_OWNER_BY_SUBS_ID)
    List<User> getComboOwnerBySubsId(@Param("subsId") Long subsId);

    @Query(nativeQuery = true, value = SQLUser.GET_USER_SME_TO_SUB)
    Page<CustomerInfoDTO> getInfoSMEtoSub(String name, String adminName, String tin, String repPersonalCertNumber, String customerType, Pageable listRequest);
    
    @Query(nativeQuery = true, value = SQLUser.GET_USER_PROVINCE_SME_TO_SUB)
    Page<CustomerInfoDTO> getProvinceSMEtoSub(String provinceName, Pageable pageable);

    Optional<User> findFirstByCorporateTaxCodeOrderByIdDesc(String corporateTaxCode);

    Optional<User> findFirstByCorporateTaxCodeAndDeletedFlagOrderByIdDesc(String corporateTaxCode, Integer deletedFlag);
    
    Optional<User> findFirstByCorporateTaxCodeAndDeletedFlagAndAffiliateTypeIsNullOrderByIdDesc(String corporateTaxCode, Integer deletedFlag);

    @Query(nativeQuery = true, value = "SELECT email FROM {h-schema}users WHERE department_id IN :departmentIds")
    Set<String> listEmailOfListDepartment(Set<Long> departmentIds);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_USER_FILTER_INFO)
    List<UserFilterInfoDTO> listUserFilterInfo(Set<Long> userIds, String username, Set<Long> userIdsNotIn, Set<Long> departmentIds);

    List<User> findByPhoneNumber(String phoneNumber);

    @Query(nativeQuery = true, value = SQLUser.GET_EMPLOYEE_STAT)
    List<SmeEmployeeCountDTO> getEmployeeStat(Long smeId, Date fromDate, Date toDate);

    @Query(nativeQuery = true, value = SQLUser.GET_EMPLOYEE_STAT_BY_MONTH)
    List<SmePreviewEmployeeStatResDTO> getEmployeeStat(Long smeId, String month);

    @Query(nativeQuery = true, value = SQLUser.GET_EMPLOYEE_STAT_BY_MONTH)
    Page<SmePreviewEmployeeStatResDTO> getEmployeeStatPage(Long smeId, String month, Pageable pageable);
    @Query(nativeQuery = true, value = SQLUser.GET_ALL_TIN_OF_USER)
    List<String> findAllTin(String tin, Integer portalType, Long userId, Long provinceId);

    @Query(nativeQuery = true, value = SQLUser.GET_ALL_TIN_OF_SUB_COMBO)
    List<String> findAllTinSubCombo(String tin, Integer portalType, Long userId, Long provinceId);

    @Query("UPDATE User u SET u.status = :statusUser WHERE u.id IN :userIds")
    @Modifying
    @Transactional
    void updateStatus(@Param("statusUser") Integer status, @Param("userIds") List<Long> userIds);

    User findFirstByCorporateTaxCode(String tin);

    User findFirstByCorporateTaxCodeAndDeletedFlag(String tin, Integer deleteFlag);

    Boolean existsByCorporateTaxCode(String corporateTaxCode);

    Boolean existsByEmail(String email);

    Boolean existsByPhoneNumber(String phone);

    @Query(value = SQLComboBox.GET_SME_EMAIL, nativeQuery = true)
    Page<CBBGetSmeValueDTO> getSmeEmail(String filter, Pageable pageable, List<String> lstCustomerType);

    @Query(value = SQLComboBox.GET_SME_CUSTOMER_CODE, nativeQuery = true)
    Page<CBBGetSmeValueDTO> getSmeCustomerCode(String filter, Pageable pageable, List<String> lstCustomerType);

    @Query(value = SQLComboBox.GET_SME_FIRST_NAME, nativeQuery = true)
    Page<CBBGetSmeValueDTO> getSmeFirstName(String filter, Pageable pageable, List<String> lstCustomerType);

    @Query(value = SQLComboBox.GET_SME_LAST_NAME, nativeQuery = true)
    Page<CBBGetSmeValueDTO> getSmeLastName(String filter, Pageable pageable, List<String> lstCustomerType);

    @Query(value = SQLComboBox.GET_SME_WEBSITE, nativeQuery = true)
    Page<CBBGetSmeValueDTO> getSmeWebsite(String filter, Pageable pageable, List<String> lstCustomerType);

    @Query(value = SQLUser.GET_LIST_USER_NOT_ACTIVATED_AND_BEFORE_DATE, nativeQuery = true)
    List<User> getListUserNotActivatedAndBeforeDate(String date);

    @Query(value = SQLUser.GET_LIST_USER_NOT_ACTIVATED_BY_TINS, nativeQuery = true)
    List<User> getListNoActiveUserByTin(List<Long> userIds);

    @Query(value = SQLUser.GET_LIST_USER_SME_INACTIVE, nativeQuery = true)
    List<User> getListUserSmeInactive(String date);

    @Query(value = SQLUser.GET_LIST_USER_DEV_INACTIVE, nativeQuery = true)
    List<User> getListUserDevInactive(String date);

    @Modifying
    @Transactional
    @Query(value = SQLUser.CHANGE_STATUS_USER, nativeQuery = true)
    void changeStatusUser(Integer status, Set<Long> ids);

    @Query(value = SQLUser.GET_LIST_USER_ID_BY_ENTERPRISE_ID, nativeQuery = true)
    List<Long> getListUserIdByEnterpriseId(Set<Long> enterpriseIds);

    @Query(value = "select concat(last_name,' ', first_name) from {h-schema}users where id =:userId", nativeQuery = true)
    String getFullName(@Param("userId") Long userId);

    @Query(value = SQLUser.GET_LIST_USER_ACTIVE_BY_IDS, nativeQuery = true)
    List<User> getListUserActiveByTin(List<Long> userIds);

    List<User> findByEmailAndDeletedFlag(String email, Integer deletedFlag);

    @Query(value = SQLUser.GET_USER_PERSONAL_BY_EMAIL, nativeQuery = true)
    User getPersonalUserByEmail(String email);

    /* List<User> findByEmailAndDeletedFlagAndCustomerType(String email, Integer deletedFlag, String customerType); */

    @Query(value = SQLUser.FIND_USER_BY_EMAIL_OR_PHONE, nativeQuery = true)
    Optional<User> findOneByEmailOrPhone(String email, String phone, String customerType);

    @Query(value = SQLUser.FIND_USER_BY_EMAIL_DB, nativeQuery = true)
    List<UserAdminDTO> findByEmailDB(String emailOrPhone);

    Boolean existsByEmailAndDeletedFlag(String email, Integer deleteFlag);

    @Query(nativeQuery = true, value = SQLAccount.FIND_USER_BY_ENTERPRISE_EMAIL_OR_PHONE)
    User findOneByEmailOrPhoneOrTinAndCustomerCode(String email, String phone, String tin, String customerType);

    @Query(nativeQuery = true, value = SQLAccount.COUNT_USER_BY_PHONE_NUMBER_NOT_CURRENT_PHONE)
    Integer countAllByPhoneNumberNotCurrentPhoneNumber(String phone, String phoneSms, String currentPhone);

    @Query(nativeQuery = true, value = SQLAccount.GET_CUSTOMER_TYPE_BY_ID)
    String getCustomerTypeById(Long id);

    @Query(value = "SELECT u FROM User u WHERE u.activationKey = :activationKey AND u.deletedFlag = 1")
    User finByActivationKeyNotDeleted(String activationKey);

    @Query(value = "SELECT u FROM User u WHERE u.id = :id AND u.activationKey = :activationKey AND u.deletedFlag = 1")
    User finByIdAndActivationKeyNotDeleted(Long id, String activationKey);

    @Query(nativeQuery = true, value = SQLAccount.FIND_ALL_PHONE_NUMBER)
    Slice<String> findSlicePhoneNumber(Pageable pageable, String name);

    @Query(nativeQuery = true, value = SQLAccount.FIND_ALL_TAX_CODE)
    Slice<String> findSliceTaxCode(Pageable pageable, String name);

    @Query(nativeQuery = true, value = SQLAccount.FIND_ALL_EMAIL)
    Slice<String> findSliceEmail(Pageable pageable, String name);

    @Query(nativeQuery = true, value = SQLAccount.FIND_ALL_CUSTOMER_NAME)
    Slice<ComboboxResponseDTO> findSliceCustomerName(Pageable pageable, String name, List<String> lstCustomerType);

    @Query(value = SQLUser.FIND_USER_BY_EMAIL_AND_CUSTOMER_TYPE, nativeQuery = true)
    User findFirstByEmailAndCustomerTypes(String email, Set<String> customerTypes);

    @Query(value = SQLUser.FIND_USER_BY_PHONE_AND_CUSTOMER_TYPE, nativeQuery = true)
    User findFirstByPhoneNumberAndCustomerTypes(String phone, Set<String> customerTypes);

    @Query(value = SQLUser.GET_LIST_DEV_OR_ADMIN_EMAIL, nativeQuery = true)
    Set<String> getListDevOrAdminEmail(List<Long> roleDevOrAdminIds);

    @Query(value = SQLUser.GET_LIST_DEV_OR_ADMIN_PHONE, nativeQuery = true)
    Set<String> getListDevOrAdminPhone(List<Long> roleDevOrAdminIds);

    void deleteById(@NonNull Long id);

    Integer deleteByIdIn(List<Long> ids);

    @Query(nativeQuery = true, value = SQLAccount.FIND_USER_BY_REP_PERSONAL_CERT_NUMBER)
    User findFirstByRepPersonalCertNumberAndCustomerType(String personalCertNumber, Long repIdentityType, String customerType);

    @Query(nativeQuery = true, value = SQLAccount.FIND_USER_BY_REP_PERSONAL_CERT_NUMBER_AND_TYPE)
    User findFirstByRepPersonalCertNumberAndCustomerType(String personalCertNumber, Long repIdentityType);

    Boolean existsByCorporateTaxCodeAndDeletedFlag(String corporateTaxCode, Integer deleteFlag);

    @Modifying
    @Query(value = SQLUser.UPDATE_USER_SUBSCRIPTION_STATUS, nativeQuery = true)
    void updateUserSubscriptionStatus();

    @Modifying
    @Query(value = SQLUser.REFRESH_ENTERPRISE_USER_CLASSIFICATION, nativeQuery = true)
    void refreshEnterpriseUserClassification();

    @Modifying
    @Query(value = SQLUser.REFRESH_ENTERPRISE_USER_CLASSIFICATION_CHANGE, nativeQuery = true)
    void refreshEnterpriseUserClassificationChange();

    @Modifying
    @Query(value = SQLUser.UPDATE_ENTERPRISE_USER_MAPPING_AND_STATE, nativeQuery = true)
    void updateEnterpriseUserMappingAndState();

    @Query(nativeQuery = true, value = SQLAccount.GET_TIN_AND_REP_PERSONAL_CERT_NUMBER)
    Slice<String> getTinAndRepPersonalCertNumber(Pageable pageable, String name, Long adminProvinceId);

    @Query(value = SQLUser.GET_LIST_USER_ADMIN_SUB, nativeQuery = true)
    List<UserTransactionDTO> getListSupAdmin(List<Long> lstRoleAdmin);

    @Query(value = SQLUser.FIND_BY_PORTAL_ID, nativeQuery = true)
    List<Long> findByPortalId(Integer portalId);

    @Query(value = SQLUser.GET_LIST_ADMIN_BY_USER_ID, nativeQuery = true)
    List<UserTransactionDTO> getListAdminByUserId(Long provinceId, List<Long> lstRoleAdmin);

    @Query(value =  SQLAccount.GET_ALL_IDENTITY_NO, nativeQuery = true)
    Slice<String> getSliceIdentityNo(Pageable pageable, String name);

    @Query(value = SQLUser.GET_LIST_USER_IN_BAN_KHDN, nativeQuery = true)
    List<UserTransactionDTO> getListUserInBanKHDN(String departmentName);

    @Query(value = SQLUser.GET_LIST_ADMIN_PROVINCE, nativeQuery = true)
    List<User> getListAdminProvince(Long provinceId);

    @Query(value = SQLUser.GET_PROVIDER_BY_SERVICE, nativeQuery = true)
    User getProviderByService(Long subId);

    @Query(value = SQLUser.GET_PROVIDER_BY_SERVICE_FOR_EMAIL, nativeQuery = true)
    String getProviderByServiceForEmail(Long subId);

    @Query(value = SQLUser.GET_ALL_SME_ADMIN_USER_ID, nativeQuery = true)
    List<User> getAllSmeAdmin(Long userId);

    @Query(value = SQLUser.GET_ALL_SME_ADMIN_BY_USER_ID_IN, nativeQuery = true)
    List<User> getAllSmeAdminByUserIdIn(Set<Long> userIds);

    @Query(value = SQLUser.GET_ALL_DEV_ADMIN_USER_ID, nativeQuery = true)
    List<User> getAllDevAdmin(Long userId);

    @Query(value = SQLUser.GET_USER_NAME_AND_ID_BY_ID, nativeQuery = true)
    ICommonIdName getUserNameAndIdById(Long id);

    @Query(nativeQuery = true, value = SQLAccount.GET_USER_INFO_FOR_INVOICE)
    IInvoiceUser getUserInfo(Long userId);

    @Query(nativeQuery = true, value = SQLAccount.GET_USER_BY_ID)
    Optional<UserSendMailDTO> getUserById(Long userId);

    @Query(nativeQuery = true, value = SQLUser.GET_DEV_EMPLOYEE_FOR_SEND_NOTIFY_SUB)
    UserReceiveEmailSubsTemplateDTO getDevEmployeeForSendNotifySub(Long userId, Long parentId);

    @Query(value = SQLUser.GET_USER_ADMIN_BY_USER_ID, nativeQuery = true)
    User getUserAdminByUserId (Long userId);

    @Query(value = SQLUser.GET_USER_INFO_RECEIVER_BY_ID_OF_SERVICE, nativeQuery = true)
    UserInfoReciverDHSX getUserInfoByIdOfService(Long userId, Long serviceId);

    @Query(value = SQLUser.GET_USER_INFO_RECEIVER_BY_ID_OF_COMBO, nativeQuery = true)
    UserInfoReciverDHSX getUserInfoByIdOfCombo(Long userId);

    @Query(value = SQLUser.GET_USER_INFO_ADDRESS_DEFAULT_BY_USER_ID, nativeQuery = true)
    UserInfoAddressDefault getUserInfoAddressDefault(Long userId);

    @Query(value = SQLUser.GET_DETAIL_ADDRESS, nativeQuery = true)
    List<UserInfoAddressDefault> getListDetailAddress(List<Long> ids);

    @Query(value = SQLUser.GET_LIST_ASSIGNEE_CONTACT, nativeQuery = true)
    List<ICommonIdName> getListAssignee(String assigneeName);

    @Query(value = SQLUser.IS_ROLE_ADMIN, nativeQuery = true)
    boolean isRoleAdmin(List<Long> roleAdminIds, Long userId);

    @Query(value = SQLUser.GET_LIST_ADMIN_BY_TARGET_ID, nativeQuery = true)
    Set<User> getListAdminByTargetId(Long targetId);

    @Query(value = SQLUser.GET_LIST_INCOMPLETE_TARGET_ADMIN, nativeQuery = true)
    Set<User> getListIncompleteTargetAdmin(Long targetId);

    @Query(value = SQLUser.GET_LIST_PARTITION_ADMIN_BY_TARGET_ID, nativeQuery = true)
    Set<User> getListPartitionAdminByTargetId(Long targetId);

    @Query(value = SQLUser.GET_LIST_INCOMPLETE_TARGET_PARTITION_ADMIN, nativeQuery = true)
    Set<User> getListIncompleteTargetPartitionAdmin(Long targetId);

    @Query(value = SQLUser.EXIST_USER_AFF, nativeQuery = true)
    boolean existsByUserIdOfAff(Long userId);

    @Query(value = SQLUser.EXIST_BY_EMAIL_IN_SYSTEM_AFF, nativeQuery = true)
    Long getIdUserExistsInAff(String email, Long userId, List<Long> roleAdminDevIds);

    @Query(value = SQLUser.FIND_LIST_USER_ID_BY_EMAIL_ID, nativeQuery = true)
    Set<AffiliateCommonDTO> findListUserIdByEmails(Set<String> emails);

    @Query(value = SQLUser.GET_ALL_TIN_AFFILIATE, nativeQuery = true)
    Set<String> getAllTinAffiliate();

    @Query(value = SQLUser.GET_ALL_AFFILIATE_PHONE_NUMBER, nativeQuery = true)
    Set<String> getAllAffiliatePhoneNumber();

    @Query(value = SQLUser.GET_ALL_DEV_OR_AFFILIATE_EMAIL, nativeQuery = true)
    Set<String> getAllDevOrAffiliateEmail();

    @Query(value = SQLUser.GET_ALL_ADMIN_EMAIL, nativeQuery = true)
    Set<AffiliateCommonDTO> getAllAdminEmail(List<Long> lstRoleIdAdmin);

    @Query(value = SQLUser.GET_ALL_IDENTITY_NO_AFFILIATE, nativeQuery = true)
    Set<String> getAllIdentityNoAffiliate();

    @Query(value = "select business_license_number from {h-schema}users where deleted_flag = 1 and business_license_number is not null", nativeQuery = true)
    Set<String> getAllBusinessNo();

    @Query(nativeQuery = true, value = SQLUser.FIND_FCM_TOKEN_BY_USER_ID)
    Set<String> findFcmTokenByUserId(Long userId);

    @Query(nativeQuery = true, value = SQLUser.FIND_FCM_TOKEN_DTO_BY_USER_ID)
    Set<IFCMTokenDTO> findFcmTokenDTOByUserId(Long userId);

    @Query(nativeQuery = true, value = SQLUser.FIND_FCM_TOKEN_BY_USER_ID_IN)
    Set<IFCMTokenDTO> findFcmTokenByUserIdIn(Set<Long> setUserId);

    @Query(value = SQLUser.FIND_LIST_USER_AFFILIATE_PARENT_BY_CODE, nativeQuery = true)
    Set<AffiliateCommonDTO> findListUserIdByAffiliateCode(Set<String> lstParentAffiliateCode);

    @Query(value = SQLUser.GET_LIST_USER_FOR_UPDATE_PASSWORD_REMINDER, nativeQuery = true)
    Set<IUserForUpdatePassword> getUserForUpdatePasswordReminder(LocalDateTime reminderStartDate, LocalDateTime lockAccountDate,
        LocalDateTime notificationDaysBeforeLock, String updateInterval, Integer passwordUpdateEmailInterval);

    @Query(value = SQLUser.GET_LIST_USER_FOR_ACCOUNT_LOCK_REMINDER, nativeQuery = true)
    Set<IUserForUpdatePassword> getUserForLockAccountReminder(String updateInterval, String lockAccountAfter, LocalDateTime lockAccountDate,
        LocalDateTime notificationDaysBeforeLock);

    @Query(value = SQLUser.GET_LIST_LOCKED_USER_TO_SEND_MAIL, nativeQuery = true)
    List<IUserForUpdatePassword> getLockedUserToSendMail(List<String> lstRole, String updateInterval, String lockAccountAfter);

    @Transactional
    @Modifying
    @Query(value = SQLUser.DELETED_BY_ID, nativeQuery = true)
    void deleteByUserId (Long id);

    boolean existsByAdminCode(String adminCode);

    @Query(value = SQLUser.FIND_NAME_AND_EMAIL_BY_ID, nativeQuery = true)
    INameAndEmailDTO findNameAndEmailById(Long userId);

    @Query(nativeQuery = true, value = SQLUser.GET_IS_PERSONAL_CUSTOMER_BY_USER_ID)
    boolean getIsPersonalCustomerByUserId(Long userId);

    @Query(nativeQuery = true, value = SQLUser.COMBOBOX_SME_NAME)
    Slice<CustomerInfoDTO> comboboxSMEName(String name, String customerType, Pageable pageable);

    @Query(nativeQuery = true, value = SQLUser.COMBOBOX_SME_REP_PERSONAL_CERT_NUMBER)
    Slice<CustomerInfoDTO> comboboxSMERepPersonalCertNumber(String repPersonalCertNumber, String customerType, Pageable pageable);
    
    @Description("Combobox tìm kiếm người đại diện. Chỉ KHDN và HKD có")
    @Query(nativeQuery = true, value = SQLUser.COMBOBOX_SME_REP_NAME)
    Slice<CustomerInfoDTO> comboboxSMERepName(String repName, Pageable pageable);

    @Query(nativeQuery = true, value = SQLUser.COMBOBOX_SME_TIN)
    Slice<CustomerInfoDTO> comboboxSMETin(String tin, String customerType, Pageable pageable);

    @Query(nativeQuery = true, value = SQLUser.IS_EMPLOYEE)
    boolean isEmployee(Long smeId, Long employeeId);

    @Query(nativeQuery = true, value = SQLUser.GET_MENTEE_BY_USER_ID)
    Set<Long> getMenteeByUserId(Long userId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = SQLUser.INSERT_OAUTH_CLIENT_DETAIL)
    void saveAppClientDetail(String apiKey, String clientSecret, Integer tokenValidity, Integer refreshTokenValidity);

    @Query(nativeQuery = true, value = SQLUser.FIND_ALL_COUPONS_FOR_SUPPLIER)
    List<CouponPopupItfDTO> findAllCouponsForSupplier(Long supplierId, String customerType);

    @Query(nativeQuery = true, value = SQLUser.FIND_ALL_MC_ACTIVITY_COUPON_FOR_SUPPLIER)
    List<McActivityIdxCoupon> findAllMcActivityCouponForSupplier(List<Long> lstCouponId);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_NOTIF_USER_DETAIL)
    List<ICommonIdNameEmail> findLstNotifUserDetail(Set<Long> lstUserId);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_NOTIF_ADMIN_USER_DETAIL)
    List<ICommonIdNameEmail> findLstNotifAdminUserDetail(Set<Long> lstReceiverId, Set<Long> lstUserId);

    @Query(nativeQuery = true, value = SQLUser.EXIST_BY_EMAIL_AND_CUSTOMER_TYPE)
    boolean existsByEmailAndCustomerType(String customerType, String email);

    @Query(value = SQLUser.EXIST_USER_BY_EMAIL_AND_TAX_CODE_ONEBSS, nativeQuery = true)
    boolean existsUserByEmailAndCorporateTaxCodeOneBSS(String email, String taxCode);

    @Query(value = SQLUser.EXIST_USER_BY_TAX_CODE_PARTNER, nativeQuery = true)
    boolean existsUserByCorporateTaxCodePartner(String taxCode);

    @Query(value = SQLUser.EXIST_USER_BY_EMAIL_ONEBSS, nativeQuery = true)
    boolean existsUserByEmailOneBSS(String email, String taxCode);

    @Query(value = SQLUser.GET_LIST_DUPPLICATE_USER_INFO, nativeQuery = true)
    List<IDupplicateUserDTO> getListDupplicateUserInfo(Set<Long> setUserId);

    @Query(value = SQLUser.EXIST_USER_BY_TAX_CODE_ONEBSS, nativeQuery = true)
    boolean existsUserByTaxCodeOneBSS(String email, String taxCode);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_PROVIDER_BY_IDS_IN)
    Slice<IProviderSearchResponseDTO> searchPageProviders(String keyword, Pageable pageable);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_CHILD_USER_ID_BY_PARENT_ID)
    Set<Long> getListChildUserIdByParentId(Long parentId);

    @Query(nativeQuery = true, value = SQLUser.GET_LIST_USER_NOT_LOCKED_BEFORE_RECEIVING_MAIL)
    Set<Long> getLstUserNotLockedBeforeReceivingMail(List<Long> lstMailHistoryId, String mailTemplateCode);

    @Query(nativeQuery = true, value = "select email, phone_number as phone from {h-schema}users where users.id = :id")
    ICommonIdNameEmail getEmailAndPhoneById(Long id);


    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = SQLUser.UPDATE_IS_PASSWORD_EXPIRED_AFTER_RECEIVING_MAIL)
    void updateIsPasswordExpiredAfterReceivingMail(List<Long> lstMailHistoryId, String mailTemplateCode);
}
