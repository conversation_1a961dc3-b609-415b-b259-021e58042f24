package com.repository.customerGroup;

import java.util.Set;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.entity.customerGroup.UploadedCustomer;
import com.onedx.common.repository.CustomJpaRepository;

@Repository
public interface UploadedCustomerRepository extends CustomJpaRepository<UploadedCustomer, Long> {

    @Modifying
    @Transactional
    @Query(value = "delete from {h-schema}uploaded_customer where id in (:lstRemovingUploadedCustomer) ", nativeQuery = true)
    void deleteAllById(Set<Long> lstRemovingUploadedCustomer);
    Boolean existsByEmail(String email);
}
