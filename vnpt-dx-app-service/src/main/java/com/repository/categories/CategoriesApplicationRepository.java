package com.repository.categories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.dto.categories.CategoriesApplicationDTO;
import com.entity.categories.CategoriesApplication;

@Repository
public interface CategoriesApplicationRepository extends JpaRepository<CategoriesApplication, Long> {

    @Query(value = "SELECT id, name, description FROM {h-schema}categories_application WHERE status = 1 and (:name = '' or name ilike '%' || :name || '%') ORDER BY name ASC", nativeQuery = true)
    List<CategoriesApplicationDTO> searchAllCategoriesApplication(String name);
}
