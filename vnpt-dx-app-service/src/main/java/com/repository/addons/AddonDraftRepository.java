package com.repository.addons;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.constant.sql.SQLAddon;
import com.entity.addons.AddonDraft;

/**
 * <AUTHOR> CuongLv2
 * @version	: 1.0
 * 02/12/2021
 */

@Repository
public interface AddonDraftRepository extends JpaRepository<AddonDraft, Long> {

    Optional<AddonDraft> findByIdAndDeletedFlag(Long id, Integer deletedFlag);

    Optional<AddonDraft> findByIdAndDeletedFlagAndProvinceId(Long id, Integer deletedFlag, Long provinceId);

    @Query(value = SQLAddon.GET_ADDON_ADMIN_ALL)
    Optional<AddonDraft> getAddonAdminAll(Long id);

    Optional<AddonDraft> findByIdAndDeletedFlagAndCreatedByIn(Long id, Integer deletedFlag, Set<Long> userIds);

    @Query(value = SQLAddon.UPDATE_DELETE_FLAG, nativeQuery = true)
    @Modifying
    void updateDeleteFlag(List<Long> ids);

    List<AddonDraft> findAllByIdInAndDeletedFlag(List<Long> ids, Integer deleteFlag);

    Optional<AddonDraft> findByCodeAndDeletedFlag(String code, Integer deleteFlag);

    Optional<AddonDraft> findByCodeAndDeletedFlagAndIdNot(String code, Integer value, Long id);

    List<AddonDraft> findAllByServiceIdAndDeletedFlag(Long serviceId , Integer deleteFlag);

    List<AddonDraft> findAllByServiceIdAndDeletedFlagAndApprove(Long serviceId , Integer deleteFlag , Integer approve);

    @Query(value = SQLAddon.GET_ADDON_BY_SERVICE_ID_AND_CUSTOMER_TYPECODE , nativeQuery = true)
    @Modifying
    List<AddonDraft> findAllByServiceIdAndCustomerTypeCode(Long serviceId , String customerTypeCode);
}
