package com.dto.product_variant;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VariantAttributesValueReqDTO {
    private Long attributesId;
    private String valueName;
    private Integer unit;
    private Integer status;
    private BigDecimal price;
    private String hexCode;
    private String key;
    private String uniqueCode;
}
