package com.dto.combo.detail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import com.dto.combo.PricingComboPlanDTO;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 25/06/2021 - 5:58 PM
 */
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class ComboPlanPricingDTO {

    @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
    Long id;

    @Schema(description = Service.SERVICE_CODE, example = Example.CODE)
    String serviceCode;

    @Schema(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
    String serviceName;

    @Schema(description = SwaggerConstant.Pricing.NAME, example = SwaggerConstant.Example.PRICING_NAME)
    String name;

    @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE) 
    BigDecimal price;

    BigDecimal totalPrice;

    @Schema(description = SwaggerConstant.Pricing.FREE_QUANTITY, example = SwaggerConstant.Example.FREE_QUANTITY)
    Long freeQuantity;

    @Schema(description = SwaggerConstant.Pricing.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    Long quantity;

    @Schema(description = SwaggerConstant.Unit.NAME, example = SwaggerConstant.Example.UNIT_NAME)
    String unitName;

    @Schema(description = SwaggerConstant.Pricing.PRICING_PLAN, example = SwaggerConstant.Example.PRICING_PLAN)
    String pricingPlan;

    @Schema(description = SwaggerConstant.Pricing.PAYMENT_CYCLE, example = SwaggerConstant.Example.PAYMENT_CYCLE)
    Integer periodValue;

    @Schema(description = SwaggerConstant.Pricing.CYCLE_TYPE, example = SwaggerConstant.Example.PERIOD)
    String periodType;

    @Schema(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
    Long serviceId;

    @Schema(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
    Long comboPricingId;

    @Schema(description = Pricing.MULTI_PLAN_ID, example = SwaggerConstant.Example.ID)
    Long pricingMultiPlanId;

    Boolean isHide;

    Set<CustomerTypeEnum> customerType;

    List<PricingComboPlanDTO.Formula> formulas;

    public ComboPlanPricingDTO(Long id, String serviceName, String serviceCode, String pricingName, BigDecimal price, Long freeQuantity,
            Long quantity, String unitName, String pricingPlan, Integer periodValue, String periodType, Long serviceId, Long comboPricingId) {
        this.id = id;
        this.serviceName = serviceName;
        this.serviceCode = serviceCode;
        this.name = pricingName;
        this.price = price;
        this.freeQuantity = freeQuantity;
        this.quantity = quantity;
        this.unitName = unitName;
        this.pricingPlan = pricingPlan;
        this.periodValue = periodValue;
        this.periodType = periodType;
        this.serviceId = serviceId;
        this.comboPricingId = comboPricingId;
    }

    public ComboPlanPricingDTO(Long id,
        String serviceName,
        String serviceCode,
        String name,
        BigDecimal price,
        Long freeQuantity,
        Long quantity,
        String unitName,
        String pricingPlan,
        Long periodValue,
        String periodType,
        Long serviceId,
        Long comboPricingId,
        Long pricingMultiPlanId) {
        this.id = id;
        this.serviceName = serviceName;
        this.serviceCode = serviceCode;
        this.name = name;
        this.price = price;
        this.freeQuantity = freeQuantity;
        this.quantity = quantity;
        this.unitName = unitName;
        this.pricingPlan = pricingPlan;
        this.periodValue = Objects.nonNull(periodValue) ? periodValue.intValue() : null;
        this.periodType = periodType;
        this.serviceId = serviceId;
        this.comboPricingId = comboPricingId;
        this.pricingMultiPlanId = pricingMultiPlanId;
    }

    public ComboPlanPricingDTO(Long id, String serviceCode, String serviceName, String name, BigDecimal price, Long freeQuantity,
        Long quantity, String unitName, String pricingPlan, Integer periodValue, String periodType, Long serviceId, Long comboPricingId,
        Long pricingMultiPlanId) {
        this.id = id;
        this.serviceCode = serviceCode;
        this.serviceName = serviceName;
        this.name = name;
        this.price = price;
        this.freeQuantity = freeQuantity;
        this.quantity = quantity;
        this.unitName = unitName;
        this.pricingPlan = pricingPlan;
        this.periodValue = periodValue;
        this.periodType = periodType;
        this.serviceId = serviceId;
        this.comboPricingId = comboPricingId;
        this.pricingMultiPlanId = pricingMultiPlanId;
    }

    public ComboPlanPricingDTO(Long id, String serviceName, String serviceCode, String name, BigDecimal price, Long freeQuantity,
        Long quantity, String unitName, String pricingPlan, Integer periodValue, String periodType, Long serviceId, Long comboPricingId,
        Long pricingMultiPlanId, Boolean isHide, Set<String> customerTypeCode) {
        this.id = id;
        this.serviceCode = serviceCode;
        this.serviceName = serviceName;
        this.name = name;
        this.price = price;
        this.freeQuantity = freeQuantity;
        this.quantity = quantity;
        this.unitName = unitName;
        this.pricingPlan = pricingPlan;
        this.periodValue = periodValue;
        this.periodType = periodType;
        this.serviceId = serviceId;
        this.comboPricingId = comboPricingId;
        this.pricingMultiPlanId = pricingMultiPlanId;
        this.isHide = isHide;
        this.customerType = customerTypeCode.stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
    }
}
