package com.dto.services;

import java.math.BigDecimal;
import java.util.Date;
import org.springframework.data.domain.Page;
import com.dto.mobile.ServiceDetailRespDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.onedx.common.constants.enums.BigDecimalSerializer;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceCouponFlashSaleDTO {

    Long couponId;

    String couponName;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date startDate;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date endDate;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
    Date currentTime;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_TYPE, example = SwaggerConstant.Example.COUPON_DISCOUNT_TYPE)
    PromotionTypeEnum promotionType;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_TYPE, example = SwaggerConstant.Example.COUPON_DISCOUNT_TYPE)
    DiscountTypeEnum discountType;

    @Schema(description = SwaggerConstant.Coupon.DISCOUNT_VALUE, example = SwaggerConstant.Example.NUMBER)
    @JsonSerialize(using = BigDecimalSerializer.class)
    BigDecimal discountValue;

    Page<ServiceDetailRespDTO> lstServiceCoupon;

}
