package com.dto.report.dashboardSme;

import java.math.BigDecimal;
import java.util.Date;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.constant.enums.report.SubscriptionReportTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.constants.enums.services.ServiceOwnerTypeEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.constants.values.SwaggerConstant.User;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> NghiaPT
 * @version    : 1.0
 * 01/08/2021
 */
public interface SubscriptionsReportResDTO {
    @Schema(description = SwaggerConstant.Subscription.ID, example = Example.ID)
    String getId();

    @Schema(description = SwaggerConstant.Province.NAME, example = Example.PROVINCE)
    String getProvinceName();

    @Schema(description = SwaggerConstant.Subscription.STATUS, example = Example.SUB_STATUS)
    SubscriptionStatusEnum getStatus();

    @Schema(description = SwaggerConstant.User.COMPANY_NAME, example = Example.COMPANY_NAME)
    String getSmeName();

    @Schema(description = SwaggerConstant.User.CUSTOMER_TYPE, example = Example.CUSTOMER_TYPE_NAME)
    String getCustomerType();

    @Schema(description = SwaggerConstant.Migration.CREATED_SOURCE, example = Example.CREATED_SOURCE)
    String getCreatedSource();

    @Schema(description = SwaggerConstant.Migration.MIGRATE_TIME, example = Example.TIME)
    Date getMigrateTime();

    @Schema(description = SwaggerConstant.Migration.MIGRATE_CODE, example = Example.DHSXKD_SUB_CODE)
    String getMigrateCode();

    @Schema(description = SwaggerConstant.User.SME_TAX_CODE, example = Example.TAX_CODE)
    String getTaxtNo();

    @Schema(description = SwaggerConstant.User.ADDRESS, example = Example.ADDRESS)
    String getAddress();

    @Schema(description = User.STREET_NAME, example = Example.STREET_NAME)
    String getStreet();

    @Schema(description = User.WARD_NAME, example = Example.WARD_NAME)
    String getWard();

    @Schema(description = User.DISTRICT_NAME, example = Example.DISTRICT)
    String getDistrict();

    @Schema(description = User.PROVINCE_NAME, example = Example.PROVINCE)
    String getProvince();

    @Schema(description = User.COUNTRY_NAME, example = Example.NATION)
    String getNation();

    @Schema(description = SwaggerConstant.User.PHONE_NUMBER, example = Example.PHONE)
    String getPhoneNo();

    @Schema(description = SwaggerConstant.User.EMAIL, example = Example.EMAIL)
    String getEmail();

    @Schema(description = SwaggerConstant.Service.NAME, example = Example.SERVICE_NAME)
    String getServiceName();

    @Schema(description = "Tên nhà cung cấp của dịch vụ")
    String getProvider();

    @Schema(description = SwaggerConstant.Pricing.NAME, example = Example.PRICING_NAME)
    String getPricingName();

    @Schema(description = SwaggerConstant.Subscription.CREATED_AT, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getRegistrationDate();

    @Schema(description = SwaggerConstant.Subscription.START_AT, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getStartAt();

    @Schema(description = Subscription.NUMBER_CYCLES, example = Example.NUMBER)
    Integer getNumberOfCycle();

    @Schema(description = Subscription.CYCLES_TYPE, example = Example.CYCLE_TYPE)
    TimeTypeEnum getCycleType();

    @Schema(description = Pricing.PAYMENT_CYCLE, example = Example.PAYMENT_CYCLE)
    Integer getPaymentCycle();

    @Schema(description = Subscription.PRE_AMOUNT_TAX, example = Example.AMOUNT)
    BigDecimal getPreAmountTax();

    @Schema(description = Subscription.AMOUNT, example = Example.AMOUNT)
    BigDecimal getAmountTax();

    @Schema(description = Subscription.AMOUNT, example = Example.AMOUNT)
    BigDecimal getAfterAmountTax();

    @Schema(description = "Loại đăng ký thuê bao", example = Example.SUBSCRIPTION_TYPE)
    SubscriptionReportTypeEnum getSubscriptionType();

    @Schema(description = "Thời gian cập nhật", example = Example.TIME)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getModifiedAt();

    @Schema(description = "Tổng KM của subscription", example = Example.AMOUNT)
    BigDecimal getPromotionAmount();

    @Schema(description = "Tổng tiền của gói, DVBS", example = Example.AMOUNT)
    BigDecimal getUnitAmount();

    @Schema(description = "OS/ON", example = Example.AMOUNT)
    ServiceOwnerTypeEnum getServiceOwnerType();

    @Schema(description = "Mã giao dịch bên điều hành sản xuất kinh doanh gửi vể", example = Example.CODE)
    String getDhsxkdCode();

    @Schema(description = "Mã giao dịch bên VNPT PAY gửi vể", example = Example.CODE)
    String getPayTransactionCode();

    @Schema(description = "Trạng thái của dịch vụ trong dịch vụ của tôi", example = "Đã cài đặt")
    String getInstalledStatus();

    @Schema(description = "Đơn hàng tạo bới", example = "Admin - <EMAIL>")
    String getCreator();

    @Schema(description = SwaggerConstant.BILLING_STATUS, example = Example.BILLING_STATUS)
    String getBillStatus();

    @Schema(description = "Mã nhân viên giới thiệu", example = Example.CODE)
    String getEmployeeCode();

    @Schema(description = "Mã thuê bao", example = Example.CODE)
    String getSubCode();

    @Schema(description = "Đk mới/Gia hạn", example = Example.CODE)
    String getSubscriptionState();

    @Schema(description = "Đk mới/Gia hạn", example = Example.CODE)
    String getState();

    @Schema(description = "Mã hóa đơn", example = Example.CODE)
    String getBillCode();

    @Schema(description = "Mã lắp đặt", example = Example.CODE)
    String getSetupCode();

    @Schema(description = "Ngày thanh toán", example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getPaymentDate();

    @Schema(description = "Ngày hủy", example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getCancelledTime();

    @Schema(description = "Ngày xuất hóa đơn điện tử", example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getCreatedExportInvoice();

    @Schema(description = "Số hóa đơn điện tử", example = Example.CODE)
    String getCodeInvoice();

    Integer getIsOneTime();

    String getAffiliateCode();

    //Bổ sung trường ngày hết hạn và số chứng thực xem báo cáo chi tiết thuê bao
    @Schema(description = "Ngày hết hạn", example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getEndCurrentCycle();
    @Schema(description = "Số chứng thực", example = Example.IDENTITY_NUMBER)
    String getIdentityNo();
    @Schema(description = "Ngày hết hạn trên DHSXKD", example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date getEndCurrentCycleContract();
    String getRenewStatus();

}
