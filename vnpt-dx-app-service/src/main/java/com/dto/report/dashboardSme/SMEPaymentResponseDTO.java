package com.dto.report.dashboardSme;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SMEPaymentResponseDTO {
    Double totalPaid;
    Double currentMonthPaid;
    Double currentMonthCost;
    Double nextMonthCost;
}
