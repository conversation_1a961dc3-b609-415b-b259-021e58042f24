package com.dto.report.dashboardDev.pricingServiceStatistic;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.springframework.context.annotation.Description;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Description("Đầu vào (bộ lọc) của các api ở các màn Thống kê trong Dashboard Dev  - Thống kê gói cước - sản phẩm")
@Data
@NoArgsConstructor
public class StatisticSubscriptionReqDTO {

    @Schema(description = "Loại thống kê (1: Tăng trưởng, 2: <PERSON><PERSON> kế)")
    private Integer statisticType = 1;

    @Schema(description = "Top (Top 5, Top 10, Top 20)")
    private Integer top = 10;

    @Schema(description = "<PERSON>hu vực (Mặc định -1 : tất cả)")
    private List<Long> lstProvinceId = Collections.singletonList(-1L);

    @Schema(description = "Danh sách id SPDV (Mặc định -1 : tất cả)")
    private List<Long> lstServiceId = Collections.singletonList(-1L);

    @Schema(description = "Loại dịch vụ (-1:Tất cả, 1:ON, 2:OS)")
    private Integer serviceType = -1;

    @Schema(description = "Chu kỳ báo cáo (0: Hàng ngày, 1: Hàng tháng, 2: Hàng năm)")
    private Integer reportCycle = 0;

    @Schema(description = "Thời gian bắt đầu")
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date startTime;

    @Schema(description = "Thời gian kết thúc")
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date endTime;

    @Schema(description = "Danh sách loại khách hàng (ALL: Tất cả, CN: Cá nhân, HKD: Hộ kinh doanh, KHDN: Doanh nghiệp)")
    private List<String> lstCustomerType = Collections.singletonList("ALL");

    private Integer page = 0;

    private Integer size = 10;
}
