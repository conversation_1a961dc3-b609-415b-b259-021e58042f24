package com.dto.rating.request;

import javax.validation.constraints.Size;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.annotation.TrimString;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 29/3/2021
 */
@Data
@NoArgsConstructor
@TrimString(fieldNames = "responseContent")
public class DevResponseCommentReqDTO {

	@Schema(description = "Nội dung phản hồi nhận xét", example = "Cảm ơn về đánh giá của bạn")
	@Size(min = 1, max = 500, message = MessageKeyConstant.Validation.LENGTH)
	private String responseContent;

}
