package com.dto.rating.request;

import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> DangNDH
 * @version    : 1.0
 * 26/3/2021
 */
@Data
public class VoteAndCmtUpdateReqDTO {
	@JsonIgnore
	private Long serviceId;

	private Long subId;

	@JsonIgnore
	private Long id;

	private Long userId;

	@NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
	private List<@Valid RatingUpdateReq> rating;

	@Schema(description = SwaggerConstant.Rating.COMMENT, example = SwaggerConstant.Example.COMMENT)
	@Size(max = 500, message = MessageKeyConstant.Validation.SIZE)
	private String comment;

	@Schema(description = SwaggerConstant.Rating.COMMENT_ID, example = SwaggerConstant.Example.ID)
	private Long commentId;

	@Schema(description = "Nội dung phản hồi nhận xét", example = "Vâng, chúng tôi xin ghi nhận đánh giá của bạn")
	private String responseContent;

	private Long responseId;

	@Data
	public static class RatingUpdateReq {

		@Schema(description = SwaggerConstant.Rating.ID, example = SwaggerConstant.Example.ID)
		@NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
		private Long ratingId;

		@Schema(description = SwaggerConstant.Rating.POINT, example = SwaggerConstant.Example.ID)
		@NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
		@Range(min = 1, max = 5, message = MessageKeyConstant.Validation.RANGE)
		private Integer ratingPoint;
	}
}
