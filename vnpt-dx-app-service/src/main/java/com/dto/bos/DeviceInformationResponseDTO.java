package com.dto.bos;

import java.math.BigDecimal;
import java.util.Date;
import org.springframework.beans.BeanUtils;
import com.dto.product_variant.VariantResExtraDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DeviceInformationResponseDTO {

    private Long serviceId;

    private String serviceName;

    private String fileName;

    private String filePath;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date createdAt;

    private BigDecimal price;

    VariantResExtraDTO variantDefaultResponseDTO;

    public DeviceInformationResponseDTO(IServiceResponseDTO serviceResponseDTO) {
        BeanUtils.copyProperties(serviceResponseDTO, this);
    }

}
