package com.dto.subscriptionUser;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;

import java.util.Date;

/**
 * <AUTHOR> Anh
 * @version    : 1.0
 * 30/1/2021
 */
public interface SubscriptionUserResponseDTO {
	Long getId();

	String getName();

	String getEmail();

	String getStatus();

	String getAvatar();

	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS,
			timezone = DateUtil.TIME_ZONE)
	Date getUpdatedTime();
}
