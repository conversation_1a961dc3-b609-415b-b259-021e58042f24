package com.dto.pricing;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.utils.DateUtil;

public interface IPricingSaaSResDTO {

    Long getId();

    Long getPricingDraftId();

    String getName();

    String getCode();

    String getDescription();

    BigDecimal getPriceValue();

    Integer getPricingPlan();

    Long getCurrencyId();

    String getCurrencyName();

    Long getUnitId();

    String getUnitName();

    Integer getCycleType();

    Integer getPaymentCycle();
    Integer getRecommendStatus();
    String getListFeatureId();

    Integer getTrialType();

    Integer getNumberOfTrial();

    Integer getPricingOrder();

    Integer getNumberOfCycles();

    Integer getPriority();

    BigDecimal getPreviewPrice();

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
    Date getCreatedAt();

    @JsonIgnore
    String getCustomerTypeCodeStr();

    default Set<String> getCustomerTypeCodes() {
        return Arrays.stream(this.getCustomerTypeCodeStr()
                .replace("[", CharacterConstant.BLANK)
                .replace("]", CharacterConstant.BLANK)
                .split(CharacterConstant.COMMA))
            .map(item -> item.replace("\"", CharacterConstant.BLANK))
            .collect(Collectors.toSet());
    }

    Long getServiceId();

    Integer getIsOneTime();

    String getServiceName();

    String getServiceIcon();
    String getServiceOnOsType();

    Integer getServiceAllowMultiSub();

    Integer getServiceProductType();

    Boolean getReaction();

    Integer getCalculateType();

}
