package com.dto.pricing;

import com.onedx.common.constants.enums.migration.RepeatPeriodicEnum;
import lombok.Data;

@Data
public class PricingCommitmentTimeDTO {
    private Integer intervalValue; // thời gian cam kết
    private RepeatPeriodicEnum intervalType; // đơn vị thời gian: 1 - <PERSON><PERSON><PERSON>, 3 - <PERSON><PERSON><PERSON><PERSON>, 4 - <PERSON>ăm
    private String terminationFee; //  Quy tắc phí chấm dứt sớm
    private String renewalPolicy; //  Ch<PERSON>h sách gia hạn sau cam kết
}
