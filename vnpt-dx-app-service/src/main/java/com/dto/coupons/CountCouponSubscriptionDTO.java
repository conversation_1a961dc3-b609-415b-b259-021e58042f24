package com.dto.coupons;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> HienNT
 * @version    : 1.0
 * 17/10/2022
 */
public interface CountCouponSubscriptionDTO {
    @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.ID)
    Long getId();

    @Schema(description = SwaggerConstant.Coupon.NAME, example = SwaggerConstant.Example.COUPON_NAME)
    Long getCountCouponUsed();

}
