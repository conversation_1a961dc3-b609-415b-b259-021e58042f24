package com.dto.coupons;

import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.PricingMultiPlan;
import com.onedx.common.constants.enums.TimeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> Halt3
 * @version : 1.0 24/09/2021
 */
public interface CouponPricingPlanResDTO {

    @Schema(description = Pricing.PAYMENT_CYCLE, example = Example.PAYMENT_CYCLE)
    Integer getPaymentCycle();

    @Schema(description = Pricing.CYCLE_TYPE, example = Example.CYCLE_TYPE)
    TimeTypeEnum getCycleType();

    @Schema(description = PricingMultiPlan.PAYMENT_CYCLE, example = Example.PRICING_PLAN_NAME)
    String getPricingPlanName();
}
