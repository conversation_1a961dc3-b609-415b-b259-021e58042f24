package com.dto.crm.automationRule;

import java.util.Date;
import java.util.List;
import com.dto.crm.assignmentRule.ConditionRuleDetailReqDTO;
import com.dto.crm.assignmentRule.RuleScanningPolicyDTO;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AutomationRuleUpdateDTO {

    Long id;

    String name;

    String code;

    String description;

    CrmObjectTypeEnum objectType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date dataValidFrom;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date dataValidTo;

    List<ConditionRuleDetailReqDTO> conditionGroup;

    RuleScanningPolicyDTO scanningPolicy;
}
