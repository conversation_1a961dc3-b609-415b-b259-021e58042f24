package com.dto.enterprise.update;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateContactDetailRequestDTO {
    private Long contactId;
    private String contactName;
    private List<String> contactPhones;
    private List<String> contactEmails;
    private Long contactProvinceId;
    private String contactTitle;
    private String contactAddress;
    private String contactOrganization;
    private String contactSocialNetworkUrls;
    private String contactMessage;
    private String contactGeneralDesc;
    private Long contactAvatarFileId;
    private Integer createdSource;
    private Integer contactStatus;
    private Set<Long> customRow;
    private String contactPosition;
    private Long assigneeId;
    private Set<Long> lstPartitionId;
}
