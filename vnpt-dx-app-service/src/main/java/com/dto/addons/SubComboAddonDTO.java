package com.dto.addons;

import java.math.BigDecimal;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Addon;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 09/12/2021 - 10:36 AM
 */
public interface SubComboAddonDTO {

    @Schema(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
    Long getId();

    @Schema(description = SwaggerConstant.Addon.USER_ID, example = SwaggerConstant.Example.ID)
    Long getUserId();

    @Schema(description = SwaggerConstant.Subscription.ADDON_NAME, example = SwaggerConstant.Example.ADDON_NAME)
    String getName();

    @Schema(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
    String getServiceName();

    @Schema(description = SwaggerConstant.Subscription.IS_REQUIRED, example = SwaggerConstant.Example.IS_REQUIRED)
    String getIsRequired();

    @Schema(description = SwaggerConstant.Addon.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    Long getQuantity();

    @Schema(description = SwaggerConstant.Subscription.PERIOD, example = SwaggerConstant.Example.PERIOD)
    String getBonusType();

    @Schema(description = SwaggerConstant.Addon.BONUS_VALUE, example = SwaggerConstant.Example.QUANTITY)
    Long getBonusValue();

    @Schema(description = SwaggerConstant.Addon.TYPE_ADDON, example = SwaggerConstant.Example.PERIOD)
    String getType();

    @Schema(description = SwaggerConstant.Addon.ADDON_CHECKED, example = SwaggerConstant.Example.TRUE)
    Boolean getChecked();

    @Schema(description = SwaggerConstant.Addon.PRICING_PLAN, example = SwaggerConstant.Example.PRICING_PLAN)
    String getPricingPlan();

    @Schema(description = SwaggerConstant.Addon.PRICE, example = SwaggerConstant.Example.PRICE)
    BigDecimal getPrice();

    @Schema(description = SwaggerConstant.Addon.HAS_CHANGE_QUANTITY, example = SwaggerConstant.Example.CHANGE_QUANTITY)
    String getAllowChangeQuantity();

    @Schema(description = SwaggerConstant.Addon.HAS_CHANGE_PRICE, example = SwaggerConstant.Example.YES_NO)
    String getHasChangePrice();

    @Schema(description = SwaggerConstant.Addon.FREE_QUANTITY, example = SwaggerConstant.Example.FREE_QUANTITY)
    Long getFreeQuantity();

    @Schema(description = Addon.ID_MULTI_PLAN, example = SwaggerConstant.Example.ID)
    Long getAddonMultiPlanId();

    String getUnitName();
    Integer getMinQuantity();
    Integer getMaxQuantity();
}
