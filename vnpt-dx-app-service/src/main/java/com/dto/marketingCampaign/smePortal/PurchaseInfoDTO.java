package com.dto.marketingCampaign.smePortal;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.constant.enums.marketingCampaign.McPaymentMethodTypeEnum;
import com.constant.enums.marketingCampaign.McPromotionScopeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.Data;

@Data
public class PurchaseInfoDTO {

    /**
     * ID of sme user
     */
    private Long customerId;

    /**
     * Scope of promotion: purchase item or whole
     */
    private McPromotionScopeEnum scope;

    /**
     * All items in this purchase
     */
    private List<PurchaseItemInfo> purchaseItemInfoList;

    /**
     * The item need get promotion. This is itemId in purchaseItemInfoList This field is ignored in case scope = WHOLE
     */
    private Long focusItem;

    /**
     * Purchase time
     */
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM, timezone = DateUtil.TIME_ZONE)
    private Date purchaseTime;

    /**
     * Payment method
     */
    private McPaymentMethodTypeEnum paymentMethod;

    /**
     * Amount of all items in the bill before tax
     */
    private BigDecimal preTaxAmount;

    /**
     * Amount of bill after tax
     */
    private BigDecimal totalPaidAmount;

    /**
     * Discount of whole bill
     */
    private BigDecimal discountWholeBill;
}
