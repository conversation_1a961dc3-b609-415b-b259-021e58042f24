package com.dto.marketingCampaign.smePortal;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.FormulaObject;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO.SetupFee;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class McPurchasingItemDTO {

    /**
     * INPUT
     */
    private CalculateTypeEnum type; // Type of item: Pricing, Combo_Plan or Addon
    private Long itemId; // ID of Pricing, ComboPlan or Addon
    private Long pmpId; // ID of Pricing_Multi_Plan in case type is Pricing
    @JsonIgnore
    private BigDecimal originalCost; // Origin price of item
    @JsonIgnore
    private BigDecimal afterPrivateDiscountCost; // Price after private discount
    @JsonIgnore
    private BigDecimal afterTotalDiscountCost; // Price after discount for whole bill
    @JsonIgnore
    private BigDecimal afterTaxCost; // Price after tax
    @JsonIgnore
    private BigDecimal originalSetupFee; // Origin price of setup fee; set null when not available
    @JsonIgnore
    private BigDecimal afterTotalDiscountSetupFee; // Price after discount for whole bill; set null when not available
    @JsonIgnore
    private BigDecimal afterTaxSetupFee; // Price after tax; set null when not available
    @JsonIgnore
    private Long numUnit; // Number of unit; available when pricing_plan is: UNIT, TIER, ...
    @JsonIgnore
    private Long quantity; // Number of item; available when pricing_plan is: QUANTITY, TIER, ...

    /**
     * OUTPUT
     */
    List<McCampaignPromotionDTO> lstPromotion = new ArrayList<>();

    public McPurchasingItemDTO(FormulaObject formulaObject, CalculateTypeEnum type) {
        this.type = type;
        this.itemId = formulaObject.getId();
        this.pmpId = formulaObject.getMultiPlanId();
        this.originalCost = formulaObject.getPreAmountTax();
        this.afterPrivateDiscountCost = formulaObject.getIntoAmountPreTax();
        this.afterTotalDiscountCost = formulaObject.getFinalAmountPreTax();
        this.afterTaxCost = formulaObject.getFinalAmountAfterTax();
        SetupFee setupFee = formulaObject.getSetupFee();
        if (setupFee != null) {
            this.originalSetupFee = setupFee.getPrice();
            this.afterTotalDiscountSetupFee = setupFee.getFinalAmountPreTax();
            this.afterTaxSetupFee = setupFee.getFinalAmountAfterTax();
        }
        this.numUnit = formulaObject.getQuantity();
    }
}
