package com.dto.marketingCampaign.smePortal;

import java.util.Date;
import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;

public interface McActivityIdxCoupon {

    Long getMcId();

    String getMcName();

    Long getActivityIdx();

    String getActivityName();

    Long getCouponId();

    @Schema(description = SwaggerConstant.Coupon.START_DATE, example = SwaggerConstant.Example.DATE)
    Date getStartTime();

    @Schema(description = SwaggerConstant.Coupon.END_DATE, example = SwaggerConstant.Example.DATE)
    Date getEndTime();
}
