package com.dto.subscriptions.responseDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> DangNDH
 * @version    : 1.0
 * 12/06/2021
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataCreateSubNotExistResDTO extends DataCreateSubExistResDTO {
    @JsonProperty("MAGOICUOC")
    String pricingId;

    @JsonProperty("TRANGTHAI")
    String status;
}
