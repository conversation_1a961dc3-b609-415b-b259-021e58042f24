package com.dto.subscriptions;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Coupon;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * * Subs Valid DTO
 *
 * <AUTHOR>
 * @since 7/5/2021 2:18 PM
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubsValidResDTO {

    List<ValidCouponResDTO> coupons = new ArrayList<>();
    List<ValidResDTO> addons = new ArrayList<>();
    List<ValidResDTO> pricings = new ArrayList<>();
    List<ValidResDTO> comboPlans = new ArrayList<>();

    @Data
    public static class ValidResDTO {
        @Schema(description = "ID", example = SwaggerConstant.Example.ID)
        private Long id;

        @Schema(description = "Tên", example = SwaggerConstant.Example.NAME_SME)
        private String name;

        @Schema(description = "Trạng thái tồn tại", example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum isExisted;

        @Schema(description = "Version", example = SwaggerConstant.Example.YES_NO)
        private YesNoEnum isNewVersion;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ValidCouponResDTO {
        @Schema(description = Coupon.ID, example = SwaggerConstant.Example.ID)
        Long id;

        @Schema(description = Coupon.NAME, example = SwaggerConstant.Example.NAME_SME)
        String name;

        @Schema(description = Coupon.IS_EXIST, example = SwaggerConstant.Example.YES_NO)
        YesNoEnum isExisted;

        @Schema(description = Coupon.IS_EXIST, example = SwaggerConstant.Example.YES_NO)
        YesNoEnum isLimited;

        List<SubscriptionPricingAddonDTO.PricingByCouponId> pricings;

        @Schema(description = Coupon.PROMOTION_TYPE, example = Example.PROMOTION_TYPE)
        PromotionTypeEnum promotionType;

        @Schema(description = Coupon.PROMOTION_TYPE, example = Example.PROMOTION_TYPE)
        BigDecimal discountValue;

        @Schema(description = Coupon.CODE, example = Example.COUPON_CODE)
        String code;

        @Schema(description = Coupon.PROMOTION_TYPE, example = Example.PROMOTION_TYPE)
        DiscountTypeEnum discountType;
    }
}
