package com.dto.subscriptions;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> Anh
 * @version    : 1.0
 * 18/2/2021
 */
@Getter
@Setter
public class SubscriptionUserParamDTO {
	@Schema(description = "Trạng thái hoạt động của người dùng" , example = "ACTIVE")
	private String status;
	@Schema(description = "Danh sách ID employee của SME admin" , example = "{1,2,3}")
	private List<Long> ids;

	public SubscriptionUserParamDTO() {
	}

	public SubscriptionUserParamDTO(String status, List<Long> ids) {
		this.status = status;
		this.ids = ids;
	}
}
