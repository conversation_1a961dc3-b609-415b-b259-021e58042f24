package com.dto.subscriptions;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> KhanhND2
 * @version    : 1.0
 * 11/6/2021
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubscriptionUpdateDevResDTO extends SubscriptionDevBaseDTO {

    @Schema(description = SwaggerConstant.Coupon.REQUIRED_PAYMENT_DATE, example = SwaggerConstant.Example.DATE)
    @JsonFormat(pattern = "dd/MM/yyyy")
    private LocalDate startChargeAt;

    @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
    BigDecimal price;

    @Schema(description = SwaggerConstant.Pricing.SETUP_FEE, example = SwaggerConstant.Example.SETUP_FEE)
    BigDecimal setupFee;

    @Schema(description = Subscription.PAYMENT_METHOD, example = Example.PAYMENT_METHOD)
    PaymentMethodEnum paymentMethod;

    private List<SubscriptionDevBaseDTO.AddonRegisterSub> addonsList = new ArrayList<>();

}
