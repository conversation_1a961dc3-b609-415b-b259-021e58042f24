package com.dto.subscriptions.detail;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 10/08/2021 - 10:11 AM
 */
public interface SubscriptionCheckedAddonCouponDTO {

    @Schema(description = SwaggerConstant.Addon.ID, example = SwaggerConstant.Example.ID)
    Long getId();

    @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.ID)
    Long getCouponId();

    @Schema(description = SwaggerConstant.Coupon.CHECKED, example = SwaggerConstant.Example.TRUE)
    boolean getChecked();
}
