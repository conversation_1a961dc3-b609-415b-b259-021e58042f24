package com.dto.subscriptions.detail;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import com.constant.enums.orders.SmeProgressEnum;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.subscription.RegTypeEnum;
import com.dto.combo.ComboDetailDTO;
import com.dto.pricing.PricingTaxRes;
import com.dto.subscriptions.calculate.UnitLimitedNewDTO.UnitLimitedNew;
import com.dto.subscriptions.formula.SubscriptionFormulaResDTO;
import com.dto.subscriptions.formula.fee.ShippingFee;
import com.dto.subscriptions.responseDTO.AmountOfCycleNext;
import com.enums.ProductTypeEnum;
import com.enums.SystemParamEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.PricingTypeEnum;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.migration.MigrationServiceTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerTypeEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeQuantityEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.subscriptions.TypeReActiveEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.ActionLog;
import com.onedx.common.constants.values.SwaggerConstant.Enterprise;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.constants.values.SwaggerConstant.User;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionCouponTotalBillDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionMcDetailDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionOneTimeFee;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionPricingAddonsDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionPricingCouponDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionSummaryDTO;
import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionTaxDTO;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubscriptionDetailDTO {

    @Schema(description = Subscription.STATUS, example = SwaggerConstant.Example.STATUS_ACTIVE)
    SubscriptionStatusEnum subscriptionStatus; // Trạng thái thuê bao

    @Schema(description = "Trạng thái gói", example = SwaggerConstant.Example.STATUS_ACTIVE)
    StatusEnum pricingStatus;

    @Schema(description = "Trạng thái dịch vụ",example = SwaggerConstant.Example.STATUS_ACTIVE)
    StatusEnum serviceStatus;

    @Schema(description = "Trạng thái pricing multi plan", example = SwaggerConstant.Example.STATUS_ACTIVE)
    StatusEnum pricingMultiPlanStatus;

    @Schema(description = Subscription.ORDER_STATUS, example = SwaggerConstant.Example.STATUS_ACTIVE)
    SmeProgressEnum orderServiceStatus; // Trạng thái order service

    @Schema(description = "ID của pricing multi plan", example = SwaggerConstant.Example.ID)
    Long pricingMultiPlanId;

    @Schema(description = "pricing multi plan ID mới nhất", example = SwaggerConstant.Example.ID)
    Long newestPricingMultiPlanId;

    @Schema(description = "pricing ID mới nhất", example = SwaggerConstant.Example.ID)
    Long newestPricingId;

    @Schema(description = SwaggerConstant.User.NAME_SME, example = SwaggerConstant.Example.NAME_SME)
    String smeName;

    @Schema(description = User.USER_CODE, example = Example.USER_CODE)
    String userCode;

    @Schema(description = SwaggerConstant.User.ID, example = SwaggerConstant.Example.ID)
    Long smeId;

    @Schema(description = User.NAME_SME, example = Example.NAME_SME)
    String smeFullName;

    @Schema(description = User.EMAIL, example = Example.EMAIL)
    String smeEmail;

    @Schema(description = User.SME_TAX_CODE, example = Example.TAX_CODE)
    String smeTaxCode;

    @Schema(description = Enterprise.IDENTITY_NUMBER, example = Example.IDENTITY_NUMBER)
    String smePersonalCertNumber;

    String smeSocialInsuranceNumber;

    @Schema(description = SwaggerConstant.Coupon.CUSTOMER_TYPE, example = SwaggerConstant.Example.CUSTOMER_TYPE)
    CustomerTypeEnum smeCustomerType;

    @Schema(description = User.PROVINCE_NAME, example = Example.PROVINCE)
    String smeProvince;

    @Schema(description = SwaggerConstant.User.NAME_SME, example = SwaggerConstant.Example.NAME_SME)
    String smeNameDefault;

    @Schema(description = User.SME_TAX_CODE, example = Example.TAX_CODE)
    String smeTaxCodeDefault;

    @Schema(description = User.ADDRESS, example = Example.ADDRESS)
    String smeAddressDefault;

    @Schema(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
    Long serviceId;

    @Schema(description = SwaggerConstant.Service.SERVICE_NAME, example = SwaggerConstant.Example.SERVICE_NAME)
    String serviceName;

    @Schema(description = SwaggerConstant.Pricing.ID, example = SwaggerConstant.Example.ID)
    Long pricingId;

    @Schema(description = SwaggerConstant.Pricing.NAME, example = SwaggerConstant.Example.PRICING_NAME)
    String pricingName;

    @Schema(description = SwaggerConstant.Subscription.START_CURRENT_CYCLE, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    Date startCurrentCycle;

    @Schema(description = SwaggerConstant.Subscription.USE_END_CYCLE, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    Date endCurrentCycle;

    @Schema(description = SwaggerConstant.Subscription.EXPIRED_TIME, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    Date expiredDate;

    @Schema(description = SwaggerConstant.Subscription.CAN_ACTIVE, example = SwaggerConstant.Example.YES_NO)
    YesNoEnum canActive;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_PRICE, example = SwaggerConstant.Example.YES_NO)
    YesNoEnum hasChangePrice;

    @Schema(description = SwaggerConstant.Pricing.HAS_CHANGE_QUANTITY, example = SwaggerConstant.Example.CHANGE_QUANTITY)
    ChangeQuantityEnum hasChangeQuantity;

    @Schema(description = SwaggerConstant.Pricing.PRICING_TYPE, example = SwaggerConstant.Example.PRICING_TYPE)
    PricingTypeEnum pricingType;

    @Schema(description = SwaggerConstant.Pricing.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    Long quantity;

    @Schema(description = SwaggerConstant.Pricing.PRICE, example = SwaggerConstant.Example.PRICE)
    List<UnitLimitedNew> listUnitLimited;

    @Schema(description = SwaggerConstant.Currency.NAME, example = SwaggerConstant.Example.CURRENCY)
    String currencyName;

    @Schema(description = SwaggerConstant.Pricing.SETUP_FEE, example = SwaggerConstant.Example.AMOUNT)
    BigDecimal setupFee;

    SubscriptionFormulaResDTO.SetupFee setupFeeDeviceObj;

    BigDecimal setupFeeDevice;
    // Thông tin phí vận chuyển
    ShippingFee shippingFee;

    @Schema(description = SwaggerConstant.Pricing.PRICING_PLAN, example = SwaggerConstant.Example.PRICING_PLAN)
    PricingPlanEnum pricingPlan;

    List<SubscriptionPricingCouponDTO> couponPricings;

    // Chi tiết khuyến mại từ CDQC dành cho gói
    List<SubscriptionMcDetailDTO> lstMcPrivatePrice = new ArrayList<>();
    List<SubscriptionMcDetailDTO> lstMcPrivatePercent = new ArrayList<>();
    List<SubscriptionMcDetailDTO> lstMcInvoicePrice = new ArrayList<>();
    List<SubscriptionMcDetailDTO> lstMcInvoicePercent = new ArrayList<>();

    Set<SubscriptionPricingAddonsDTO> addonsList;

    @Schema(description = SwaggerConstant.Pricing.TOTAL_AMOUNT, example = SwaggerConstant.Example.AMOUNT)
    BigDecimal totalAmount;

    BigDecimal totalAmountAfterRefund;

    @Schema(description = SwaggerConstant.Pricing.FREE_QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    Integer freeQuantity;

    @Schema(description = SwaggerConstant.Pricing.PAYMENT_CYCLE, example = SwaggerConstant.Example.QUANTITY)
    Integer paymentCycle;

    @Schema(description = SwaggerConstant.Pricing.CYCLE_TYPE, example = SwaggerConstant.Example.PERIOD)
    CycleTypeEnum cycleType;

    @Schema(description = SwaggerConstant.Pricing.CYCLE_TYPE, example = SwaggerConstant.Example.PERIOD)
    String cycleTypeTry;

    @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_CYCLES, example = SwaggerConstant.Example.QUANTITY)
    Integer numberOfCycles;

    Integer numberOfCyclesPricing;

    Integer numberOfCyclesPopup;

    @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_TRIAL, example = SwaggerConstant.Example.QUANTITY)
    Integer numberOfTrial;

    @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_TRIAL, example = SwaggerConstant.Example.QUANTITY)
    Long trialDay;

    @Schema(description = SwaggerConstant.Pricing.TRIAL_TYPE, example = SwaggerConstant.Example.PERIOD)
    CycleTypeEnum trialType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date createdAt;

    @Schema(description = SwaggerConstant.Subscription.START_AT, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    Date startedAt;

    @Schema(description = SwaggerConstant.Subscription.START_CHARGE_AT, example = SwaggerConstant.Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    Date startChargedAt;

    @Schema(description = SwaggerConstant.Subscription.IS_CONTRACT, example = Example.YES_NO)
    YesNoEnum isContract;

    @Schema(description = SwaggerConstant.Billing.CURRENT_CYCLE, example = SwaggerConstant.Example.PAYMENT_CYCLE)
    Integer currentCycle;

    @Schema(description = Subscription.REG_TYPE, example = Example.REG_TYPE)
    RegTypeEnum regType;

    @Schema(description = Subscription.PAYMENT_METHOD, example = Example.PAYMENT_METHOD)
    PaymentMethodEnum paymentMethod;

    @Schema(description = Pricing.IS_CHANGE_NOW, example = Example.CHANGE_PRICING)
    PricingCancelTimeActiveEnum changePricing;

    @Schema(description = Pricing.IS_CHANGE_NOW, example = Example.CHANGE_PRICING)
    PricingCancelTimeActiveEnum changeSubscription;

    @Schema(description = Pricing.CANCEL_DATE, example = Example.NOW)
    PricingCancelTimeActiveEnum cancelDate;

    @Schema(description = ActionLog.CREATED_BY, example = Example.NAME_DEV)
    PortalType roleCreatedBy;

    @Schema(description = Subscription.PROVINCE_CREATED_BY, example = Example.ID)
    Long provinceCreatedBy;

    @Schema(description = Subscription.DHSXKD_SUB_CODE, example = Example.CODE)
    String dhsxkdSubCode;

    @Schema(description = SwaggerConstant.Subscription.CAN_CHANGE, example = Example.YES_NO)
    YesNoEnum canChange;

    @Schema(description = Subscription.INSTALLED, example = Example.STATUS)
    String installed;

    @Schema(description = Subscription.INSTALLED, example = Example.STATUS)
    Boolean isAM;

    @Schema(description = Subscription.CHANGE_DATE, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    Date changeDate;

    @Schema(description = Subscription.CHANGE_DATE, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    Date installedTime;

    @Schema(description = Subscription.CHANGE_DATE, example = Example.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    Date migrateTime;

    String migrateCode;

    Integer installationStatus;

    String isFinalCycle;

    List<SubscriptionTaxDTO> taxList;

    List<PricingTaxRes> setupFeeTaxList;

    List<SubscriptionCouponTotalBillDTO> couponList;

    List<SubscriptionOneTimeFee> onceTimeFee;

    SubscriptionSummaryDTO summary;

    @Schema(description = SwaggerConstant.Combo.URL, example = SwaggerConstant.Example.EMBER_URL)
    Set<ComboDetailDTO> urlPreOrder;

    @Schema(description = Subscription.EMPLOYEE_CODE, example = Example.CODE)
    String employeeCode;

    @Schema(description = Service.SERVICE_OWNER, example = Example.SERVICE_OWNER)
    ServiceOwnerTypeEnum serviceOwner;

    @Schema(description = Service.SERVICE_OWNER, example = Example.SERVICE_OWNER)
    ServiceOwnerEnum serviceOwnerType;

    @Schema(description = "Cấu hình hệ thống cho chương trình khuyến mại", example = Example.SERVICE_OWNER)
    SystemParamEnum couponConfig;

    @Schema(description = SwaggerConstant.Pricing.HAS_COUPON, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasCouponList;

    @Schema(description = SwaggerConstant.Pricing.HAS_COUPON, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasCouponListPricing;

    @Schema(description = Pricing.HAS_RENEW, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum hasRenew;

    @Schema(description = "Thông tin mô tả chi phí đơn hàng")
    private SubscriptionFormulaResDTO formulaResDTO;
    
    private CreatedSourceMigrationEnum createdSourceMigration;

    private MigrationServiceTypeEnum migrationServiceType = MigrationServiceTypeEnum.UNSET;

    @Schema(description = Pricing.CAN_UPDATE, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum canUpdateSub;
    
    @Schema(description = Subscription.AWAITING_CANCEL, example = SwaggerConstant.Example.YES_NO)
    private YesNoEnum awaitingCancel;

    private CreatedSourceMigrationEnum createdSourceSubMigration;

    private Object simMetadata; // thông tin sim số

    private String errorContent;

    private String errorActivity;

    private TypeReActiveEnum typeReActiveEnum;

    private int awaitingReactive;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    private Date reActiveDate;

    private Integer activeDate;

    private Integer reactiveStatus;

    private Integer isOneTime;

    private Boolean isOnlyService;

    private Boolean isBuyService;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    private Date endCurrentCycleNew;

    private String smeSetupAddress;

    private String maDV;

    private List<CustomFieldValueDTO> lstCustomField;

    private String paymentCode;

    private String bankName;

    private Integer billingStatus;

    private Integer billingActionType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
        timezone = DateUtil.TIME_ZONE)
    private Date setupDate;

    Integer numSub;
    Integer allowMultiSub;

    @Schema(description = "Nhãn: là Phí lắp đặt đối với dịch vụ Internet truyền hình (trừ myTVMobile); là Phí thiết lập với các dịch vụ số")
    private String setupFeeLabel;

    private Long variantId;

    private String variantName;

    private BigDecimal priceVariant;

    private Long serviceDraftId;

    private Long quantityVariant;

    private Long serviceGroupId;

    private String serviceGroupName;

    private Long minimumQuantity;

    private Long maximumQuantity;

    private String canceledBy;

    private AmountOfCycleNext amountOfCycle;

    private Boolean isRated = false;

    private String icon;

    private String iconExternalLink;

    private String provider;

    private String subCode;

    private ProductTypeEnum productType = ProductTypeEnum.SERVICE;
}
