package com.dto.subscriptions;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Addon;
import com.onedx.common.constants.values.SwaggerConstant.CreditNote;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.onedx.common.constants.values.SwaggerConstant.Subscription;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.dto.integration.backend.subscription.AddonSubReqDTO;
import com.onedx.common.dto.integration.backend.subscription.CouponSubReqDTO;
import com.onedx.common.dto.integration.backend.subscription.McSubReqDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO.CalUnitLimitedCustomDTO;
import com.dto.subscriptions.SubscriptionDevBaseDTO.OtherFeeSubDev;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> DangNDH
 * @version    : 1.0
 * 2/2/2021
 */
@Data
public class SubEditorReqDTO {

    @Schema(description = SwaggerConstant.Subscription.QUANTITY, example = SwaggerConstant.Example.QUANTITY)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    @Range(min = 1, message = MessageKeyConstant.Validation.RANGE)
    private Long quantity;

    @Schema(description = Pricing.MULTI_PLAN_ID, example = Example.ID)
    @Range(min = 1, message = MessageKeyConstant.Validation.RANGE)
    private Long pricingMultiPlanId;

    @Schema(description = Pricing.ID, example = Example.ID)
    @Range(min = 1, message = MessageKeyConstant.Validation.RANGE)
    private Long pricingId;

    @Schema(description = Addon.PRICE, example = Example.PRICE)
    BigDecimal price;

    @Schema(description = Addon.SETUP_FEE, example = Example.SETUP_FEE)
    BigDecimal setupFee;

    @Schema(description = Subscription.PAYMENT_METHOD, example = Example.PAYMENT_METHOD)
    PaymentMethodEnum paymentMethod;

    @Schema(description = SwaggerConstant.Pricing.NUMBER_OF_CYCLES, example = SwaggerConstant.Example.NUMBER)
    private Integer numberOfCycles;

    List<CalUnitLimitedCustomDTO> unitLimitedList = new ArrayList<>();

    @Schema(description = CreditNote.ID, example = SwaggerConstant.Example.ID)
    private Set<Long> creditNoteIdList;

    private List<@Valid OtherFeeSubDev> otherFeeList = new ArrayList<>();

    private List<@Valid CouponSubReqDTO> couponList = new ArrayList<>();

    @Schema(description = "Danh sách CDQC áp dụng cho tổng hóa đơn")
    private List<@Valid McSubReqDTO> mcList = new ArrayList<>();

    private List<@Valid AddonSubReqDTO> addonsList = new ArrayList<>();

    private String configs;

    private String sourceApi;
}
