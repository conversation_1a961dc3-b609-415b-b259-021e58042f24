package com.dto.applyFee;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 09/12/2021 - 2:00 PM
 */
@Data
public class ApplyFeeDTO {

    @Schema(description = SwaggerConstant.ID, example = Example.ID)
    private Long applyFeeId;

    @Schema(description = SwaggerConstant.ID, example = Example.ID)
    private Long customFeeId;

}
