package com.dto.affiliate;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportCommissionAffiliateDetailDTO {
    Long userId;
    String name;
    String email;
    String code;
    Long affiliateId;
    Long affiliateLevel;
    String parentCode;
    String parentName;
    Long totalOrder;
    BigDecimal totalCommission;
    List<GetLstDetailOrderDTO> commissionOrderDetail;

    public ExportCommissionAffiliateDetailDTO(CommissionAffiliateDetailDTO affiliate) {
        BeanUtils.copyProperties(affiliate, this);
        commissionOrderDetail = new ArrayList<>();
    }
}
