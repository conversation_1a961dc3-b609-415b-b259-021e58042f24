package com.dto.serviceGroup;


import com.constant.enums.serviceGroup.ServiceGroupPricingObjectType;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;

public interface ServiceGroupPricingViewITF {

    Long getServiceGroupId();

    Long getServiceGroupPricingId();

    Long getServiceGroupDraftId();

    Long getServiceId();

    String getServiceName();

    Integer getStatus();

    Integer getDeletedFlag();

    Integer getAllowMultiSub();

    String getIcon();

    Long getPricingId();

    Long getPricingItemId();

    String getPricingName();

    Long getPricingMultiPlanId();

    Long getPaymentCycle();

    Integer getCircleType();

    Long getVariantId();

    Long getVariantItemId();

    String getVariantName();

    ServiceGroupPricingObjectType getObjectType();

    String getDeveloperName();

    OnOsTypeEnum getOnOsType();

    String getCustomerTypeCode();
}
