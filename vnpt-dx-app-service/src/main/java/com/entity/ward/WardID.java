package com.entity.ward;

import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class WardID implements Serializable {

    private Long id;
    private Long districtId;
    private String provinceCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WardID that = (WardID) o;
        return Objects.equals(this.id, that.getId()) &&
            Objects.equals(this.districtId, that.getDistrictId()) &&
            Objects.equals(this.provinceCode, that.getProvinceCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(23, id, districtId, provinceCode);
    }
}
