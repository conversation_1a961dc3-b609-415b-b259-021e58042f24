package com.entity.marketingCampaign;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "mc_send_coupon_code_history")
@NoArgsConstructor
public class McSendCouponCodeHistory implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long couponSetId;
    private Integer type;
    private String couponCode;
    private String email;
    private Integer totalExisted;

    public McSendCouponCodeHistory(Long couponSetId, Integer type, String couponCode) {
        this.couponSetId = couponSetId;
        this.type = type;
        this.couponCode = couponCode;
    }

    public McSendCouponCodeHistory(Long couponSetId, Integer type, String couponCode, Integer totalExisted) {
        this.couponSetId = couponSetId;
        this.type = type;
        this.couponCode = couponCode;
        this.totalExisted = totalExisted;
    }
}
