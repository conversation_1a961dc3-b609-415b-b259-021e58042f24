/**
 * <AUTHOR> VinhNT
 * @version	: 1.0
 *
 */

package com.entity.feature;

import com.component.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * tính năng
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "features")
@AllArgsConstructor
@NoArgsConstructor
public class Feature extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	/**
	 * tên tính năng
	 */
	@Column(name = "name")
	private String name;

	/**
	 * services.id
	 */
	@Column(name = "service_id")
	private Long serviceId;

	/**
	 * Mã tính năng
	 */
	@Column(name = "code")
	private String code;

	private Long smeFeatureId;

	/**
	 * <PERSON>ô tả tính năng
	 */
	@Column(name = "description")
	private String description;

	/**
	 * <PERSON>ại tính năng
	 */
	@Column(name = "type")
	private Integer type;

	/**
	 * Sắp xếp
	 */
	@Column(name = "priority_order")
	private Integer priorityOrder;

	/**
	 * file
	 */
	@Column(name = "file_id")
	private Long fileId;

	/**
	 * services.id
	 */
	@Column(name = "services_draft_id")
	private Long serviceDraftId;

	@Column(name = "base_id")
	private Long baseId;

	private String icon;

	public Feature(String name, String code, String description, Integer isDisplay, Integer priorityOrder, Long fileId, String icon) {
		this.name = name;
		this.code = code;
		this.description = description;
		this.priorityOrder = priorityOrder;
		this.fileId = fileId;
		this.icon = icon;
		this.type = isDisplay;
	}
}
