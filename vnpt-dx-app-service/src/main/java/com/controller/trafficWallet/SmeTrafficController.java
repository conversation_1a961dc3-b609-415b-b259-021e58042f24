package com.controller.trafficWallet;

import com.onedx.common.constants.values.SwaggerConstant;
import com.component.BaseController;
import com.dto.common.ValueDTO;
import com.onedx.common.dto.enterprise.CommonResponseDTO;
import com.dto.trafficWallet.*;
import com.entity.traffic_activity_log.WalletBss;
import com.onedx.common.constants.enums.PortalType;
import com.google.common.io.Files;
import com.service.trafficWallet.TrafficWalletService;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Validated
@RestController
@RequestMapping("/api/sme-portal/traffic")
@Slf4j
public class SmeTrafficController {

    @Autowired
    private TrafficWalletService trafficWalletService;

    /* ---------------------------------------------WALLET--------------------------------------------- */

    @Operation(description = "Api lấy thông tin ví lưu lượng")
    @PostMapping("/wallet/info")
    public Page<WalletBssDTO> getListTrafficWallet(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getListTrafficWallet(trafficWalletReqDTO, PortalType.SME);
    }


    @Operation(description = "Api lấy thông tin chia sẻ")
    @PostMapping("/share/list-detail-shared")
    public Page<SharedBssDTO> getListSharedDetailInfor(@Validated @RequestBody TrafficWalletHistoryReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getListSharedDetailInfor(trafficWalletReqDTO, PortalType.SME);
    }

    @Operation(description = "Api lấy chi tiết danh sách thông tin chia sẻ")
    @PostMapping("/share/list")
    public Page<SharedBssDTO> getListShared(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getListShared(trafficWalletReqDTO, PortalType.SME);
    }

    @Operation(description = "Api lấy thông tin chi tiết ví lưu lượng")
    @PostMapping("/wallet/detail/bss")
    public TrafficDetailDTO getDetailTrafficWallet(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getDetailTrafficWallet(trafficWalletReqDTO, PortalType.SME);
    }

    @Operation(description = "Api lấy thông tin chi tiết lịch sử ví lưu lượng")
    @GetMapping("/wallet/detail-history/bss")
    public ResponseEntity<List<TrafficActivityLogDTO>> getDetailActivityHistoryTrafficList(
            @RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970")
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date startDate,

            @RequestParam(name = "endDate", required = false, defaultValue = "01/01/3000")
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date endDate,

            @RequestParam(name = "walletCode", required = false, defaultValue = "") String walletCode,

            @Parameter(description = SwaggerConstant.TYPE_SEARCH_DATE)
            @RequestParam(name = "typeSearchDate", required = false, defaultValue = "0") Integer typeSearchDate
    ) {
        log.info("-------- Start get list activityHistory-----------");
        List<TrafficActivityLogDTO> result = trafficWalletService.getDetailActivityHistoryTrafficList(
                walletCode, startDate, endDate, typeSearchDate, PortalType.SME);
        log.info("-------- End get list activityHistory-----------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Api lấy thông tin chi tiết ví để chia sẻ lưu lượng")
    @PostMapping("/wallet/detail/bss-sharing")
    public TrafficDetailDTO getDetailTrafficWalletForSharing(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getDetailTrafficWalletForSharing(trafficWalletReqDTO, PortalType.ADMIN);
    }

    @Operation(description = "Api lấy thông tin chi tiết ví lưu lượng")
    @GetMapping("/wallet/detail")
    public WalletBss getDetailTrafficWalletInSystem(@RequestParam(name = "subCode", required = false, defaultValue = "") String subCode) {
        return trafficWalletService.getDetailTrafficWalletInSystem(subCode, PortalType.SME);
    }

    @Operation(description = "Api lấy thông tin chi tiết chia sẻ")
    @GetMapping("/share/detail")
    public SharedDetailDTO getDetailTrafficWallet(
            @RequestParam(name = "id") String id
    ) {
        return trafficWalletService.getDetailShare(id);
    }

    @Operation(description = "Api gửi mã OTP")
    @PostMapping("/wallet/send-otp")
    public ResponseEntity<SendOtpResDTO> sendOTP(@Validated @RequestBody OtpRequestDTO otpRequestDTO) {
        log.info("Start sendOTP ------");
        SendOtpResDTO result = trafficWalletService.sendOTP(otpRequestDTO);
        log.info("End sendOTP ------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Kiểm tra thuê bao người dùng ")
    @PostMapping("/wallet/check-participant")
    public ResponseEntity<CheckParticipantResDTO> checkParticipant(@Validated @RequestBody OtpRequestDTO otpRequestDTO) {
        log.info("Start sendOTP ------");
        CheckParticipantResDTO result = trafficWalletService.checkParticipant(otpRequestDTO);
        log.info("End sendOTP ------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Api xác thực thông tin ví")
    @PostMapping("/wallet/accuracy")
    public ResponseEntity<TrafficShareResDTO> accuracyInfo(@Validated @RequestBody AccuracyTrafficReqDTO accuracyTrafficReqDTO) {
        log.info("Start accuracyInfo ------");
        TrafficShareResDTO result = trafficWalletService.accuracyInfo(accuracyTrafficReqDTO, PortalType.SME);
        log.info("End accuracyInfo ------");
        return ResponseEntity.ok(result);
    }

    /* ---------------------------------------------SHARE--------------------------------------------- */

    @Operation(description = "Api chia sẻ lưu lượng")
    @PostMapping("/share/create")
    public ResponseEntity<TrafficShareResDTO> shareTraffic(@Validated @RequestBody TrafficShareReqDTO trafficShareReqDTO) {
        log.info("Start shareTraffic ------");
        TrafficShareResDTO result = trafficWalletService.shareTraffic(trafficShareReqDTO, PortalType.SME);
        log.info("End shareTraffic ------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Api thêm sdt trong chia sẻ lưu lượng")
    @PostMapping("/share/create/phone_receipt")
    public void createPhoneReceipt (@Validated @RequestBody TrafficSharingInforDTO TrafficSharingInforDTO) {
        log.info("Start createPhoneReceipt ------");
        TrafficShareResDTO result = trafficWalletService.createPhoneReceipt(TrafficSharingInforDTO, PortalType.SME);
        log.info("End createPhoneReceipt ------");
    }

    /**
     * Import thông tin chia sẻ từ file excel
     */
    @PostMapping("/share/import/phone_receipt")
    public ResponseEntity<InputStreamResource> importPhoneReceipt(@RequestPart("file") MultipartFile file)
            throws IOException {
        log.info("Start importSME");
        CommonResponseDTO response = new CommonResponseDTO();
        InputStreamResource result = trafficWalletService.importPhoneReceipt(file, response, PortalType.SME);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Access-Control-Expose-Headers","cause");
        headers.set("cause", String.valueOf(response.getCause()));
        StringBuilder filename = new StringBuilder(Files.getNameWithoutExtension(file.getOriginalFilename()));
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("ddMMyyyyHHmmss");
        filename.append("_Danh sách lỗi_").append(dateFormat.format(LocalDateTime.now())).append(".xlsx");
        headers.add("Content-Disposition", "attachment; filename=\""+ filename +"\"");
        log.info("End importSME");
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @Operation(description = "Api check sdt tồn tại trong chia sẻ lưu lượng")
    @PostMapping("/share/check_exist/phone_receipt/{phoneReceipt}")
    public Boolean checkExistPhoneReceipt (
            @PathVariable("phoneReceipt") String phoneReceipt
    ) {
        log.info("Start checkExistPhoneReceipt ------");
        Boolean result = trafficWalletService.checkExistPhoneReceipt(phoneReceipt, PortalType.SME);
        log.info("End checkExistPhoneReceipt ------");
        return result;
    }

    @Operation(description = "Api update sdt trong chia sẻ lưu lượng")
    @PostMapping("/share/update/phone_receipt")
    public ResponseEntity<TrafficShareResDTO> updatePhoneReceipt (@Validated @RequestBody TrafficSharingInforDTO TrafficSharingInforDTO) {
        log.info("Start updatePhoneReceipt ------");
        TrafficShareResDTO result = trafficWalletService.updatePhoneReceipt(TrafficSharingInforDTO, PortalType.SME);
        log.info("End updatePhoneReceipt ------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Api xóa sdt trong chia sẻ lưu lượng")
    @PostMapping("/share/delete/phone_receipt")
    public ResponseEntity<TrafficShareResDTO> deletePhoneReceipt (@Validated @RequestBody TrafficSharingInforDTO TrafficSharingInforDTO) {
        log.info("Start shareTraffic ------");
        TrafficShareResDTO result = trafficWalletService.deletePhoneReceipt(TrafficSharingInforDTO, PortalType.SME);
        log.info("End shareTraffic ------");
        return ResponseEntity.ok(result);
    }

    /* ---------------------------------------------HISTORY--------------------------------------------- */

    @Operation(description = "Api lấy danh sách nhật ký hoạt động")
    @GetMapping("/history/list")
    public ResponseEntity<Page<TrafficActivityLogDTO>> getActivityHistoryList(
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,

            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,

            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "id,asc") String sort,

            @RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970")
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date startDate,

            @RequestParam(name = "endDate", required = false, defaultValue = "01/01/3000")
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date endDate,

            @RequestParam(name = "walletCode", required = false, defaultValue = "") String walletCode,

            @Parameter(description = SwaggerConstant.TYPE_SEARCH_DATE)
            @RequestParam(name = "typeSearchDate", required = false, defaultValue = "0") Integer typeSearchDate
    ) {
        log.info("-------- Start get list activityHistory-----------");
        BaseController.ListRequest pageable = new BaseController.ListRequest(size, page, sort);
        Page<TrafficActivityLogDTO> result = trafficWalletService.getListActivityHistory(pageable.getPageable(),
                walletCode, startDate, endDate, typeSearchDate, PortalType.SME);
        log.info("-------- End get list activityHistory-----------");
        return ResponseEntity.ok(result);
    }

    @Operation(description = "Api xóa nhật ký lưu lượng ")
    @Transactional
    @DeleteMapping("/history/delete")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteLogTraffic(@Valid @RequestBody DeleteTrafficLogReqDTO ids) {
        log.info("-------- Start delete log traffic-----------");
        trafficWalletService.deleteLogTraffic(ids, PortalType.SME);
        log.info("-------- Start delete log traffic-----------");
    }

    @Operation(description = "Lấy danh sách gói cước của chia sẻ")
    @GetMapping("/combobox/package-name")
    public List<ValueDTO> getPackageNameList(
            @RequestParam(name = "packageName", required = false, defaultValue = "") String packageName,
            @RequestParam(name = "type", required = false, defaultValue = "") String type
    ) throws AccessDeniedException {
        log.info("--- Execute UpdateActivityLogProcessStatus method: Start ---");
        List<ValueDTO> respond =  trafficWalletService.getPackageNameList(packageName,type, PortalType.SME);
        log.info("--- Execute UpdateActivityLogProcessStatus method: End ---");
        return respond;
    }

    @Operation(description = "Lấy danh sách combox chia sẻ lưu lượng")
    @GetMapping("/combobox/shared-list")
    public List<SharedBssDTO> getComboboxSharedList(
    ) throws AccessDeniedException {
        log.info("--- Execute UpdateActivityLogProcessStatus method: Start ---");
        List<SharedBssDTO> respond =  trafficWalletService.getComboboxSharedList(PortalType.SME);
        log.info("--- Execute UpdateActivityLogProcessStatus method: End ---");
        return respond;
    }

    @Operation(description = "Lấy danh sách combobox số điện thoại")
    @PostMapping("/combobox/shared-list-phone")
    public List<WalletBssDTO> getComboboxPhone(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getComboboxPhone(trafficWalletReqDTO, PortalType.SME);
    }

    @Operation(description = "Api lấy thông tin chia sẻ")
    @PostMapping("/combobox/sharing/list-phone")
    public List<SharedBssDTO> getComboboxListPhone(@Validated @RequestBody TrafficWalletReqDTO trafficWalletReqDTO) {
        return trafficWalletService.getComboboxListPhone(trafficWalletReqDTO, PortalType.SME);
    }
}
