package com.controller.affiliate;

import com.component.BaseController;
import com.dto.affiliate_commission.*;
import com.dto.common.ValueDTO;
import com.onedx.common.constants.values.DatabaseConstant;
import com.service.affiliate.AffiliateCommissionBillService;
import com.onedx.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Slice;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/admin-portal/bill-affiliate-commission")
@Slf4j
public class AffiliateCommissionBillAdminController {

    private final AffiliateCommissionBillService affiliateCommissionBillService;

    @Autowired
    public AffiliateCommissionBillAdminController(AffiliateCommissionBillService affiliateCommissionBillService) {
        this.affiliateCommissionBillService = affiliateCommissionBillService;
    }

    @PostMapping("/create")
    @Description("Tạo hóa đơn thanh toán hoa hồng")
    public void createBillCommission(@Valid @RequestBody AffiliateBillDTO affiliateBillDTO) {
        affiliateCommissionBillService.createBillCommission(affiliateBillDTO);
    }

    @PostMapping("/get-page")
    @Description("Tìm kiếm danh sách hóa đơn thanh toán hoa hồng")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<AffiliateCommissionBillResDTO> getPage(@RequestBody GetListAffiliateCommissionBillReqDTO request){
        BaseController.ListRequest requestPageable = new BaseController.ListRequest(request.getSize(), request.getPage(), request.getSort());
        return affiliateCommissionBillService.getPage(request, requestPageable.getPageable());
    }

    @GetMapping("/detail/{id}")
    @Description("Lấy thông tin hóa đơn thanh toán hoa hồng")
    public AffiliateCommissionBillDetailDTO getDetail(@PathVariable Long id){
        return affiliateCommissionBillService.getDetailById(id);
    }

    @DeleteMapping("/delete/{id}/{affEventId}")
    @Description("Lấy thông tin hóa đơn thanh toán hoa hồng")
    public Long deleteAffComEvent(@PathVariable Long id, @PathVariable Long affEventId){
        return affiliateCommissionBillService.deleteAffComEvent(id, affEventId);
    }

    @GetMapping("/get-popup/list-affiliate-payment")
    @Description("Popup chọn thành viên")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<AffiliateBillItemDTO> getListAffiliatePayment(
            @RequestParam(required = false, defaultValue = "") String email,
            @RequestParam(required = false, defaultValue = "") String affiliateName,
            @RequestParam(required = false, defaultValue = "") String affiliateCode,
            @RequestParam(required = false, defaultValue = "-1") Integer affiliateLevel,
            @RequestParam(required = false, defaultValue = "ADMIN") String type,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "startTime", required = false, defaultValue = "01/01/1970") Date startTime,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "endTime", required = false, defaultValue = "01/01/3000") Date endTime,
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer size,
            @RequestParam(required = false, defaultValue = "affId,asc") String sort
    ) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        Page<AffiliateBillItemDTO> customerDTOS = affiliateCommissionBillService.getListAffiliatePayment(email, affiliateName, affiliateCode, affiliateLevel, type, startTime, endTime, listRequest.getPageable());
        return customerDTOS;
    }

    @GetMapping("/get-popup/check-affiliate/{userId}")
    @Description("Popup chọn thành viên")
    public CheckAffiliateUserDTO checkAffiliateUser(@PathVariable Long userId) {
        CheckAffiliateUserDTO checkAffiliateUser = affiliateCommissionBillService.checkAffiliateUser(userId);
        return checkAffiliateUser;
    }

    @GetMapping("/get-popup/list-commission-intro")
    @Description("Popup đơn hàng giới thiệu")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<IGetAffiliateBillIntroDTO> getListCommissionIntro(
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false, defaultValue= "ALL") String type,
            @RequestParam(required = false, defaultValue= "-1") List<Long> lstAffComEventId,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "startTime", required = false, defaultValue = "01/01/1970") Date startTime,
            @DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
            @RequestParam(name = "endTime", required = false, defaultValue = "01/01/3000") Date endTime,
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer size,
            @RequestParam(required = false, defaultValue = "affComEventId,asc") String sort
    ) {
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        Page<IGetAffiliateBillIntroDTO> response = affiliateCommissionBillService.getListCommissionIntro(id, userId, type, startTime, endTime, lstAffComEventId, listRequest.getPageable());
        return response;
    }

    @PutMapping("/update")
    @Description("Update hóa đơn thanh toán")
    public void changeAffiliateBill(@RequestBody AffiliateBillUpdateReqDTO affiliateBillUpdateReqDTO) {
        affiliateCommissionBillService.update(affiliateBillUpdateReqDTO);
    }

    @PutMapping("/update/commission-config-min")
    @Description("Update giá trị tối thiểu hóa đơn thanh toán")
    public void updateCommissionConfigMin(@RequestParam(required = true) BigDecimal config) {
        affiliateCommissionBillService.updateCommissionConfigMin(config);
    }

    @GetMapping("/get/commission-config-min")
    @Description("Lấy thông tin giá trị tối thiểu hóa đơn thanh toán")
    public BigDecimal getCommissionConfigMin() {
        return affiliateCommissionBillService.getCommissionConfigMin();
    }

    @GetMapping("/get-combobox/code")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListCode(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "type", required = false, defaultValue = "CURRENT") String type,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListCode(name, type, pageInfo.getPageable());
    }

    @GetMapping("/get-combobox/billing-code")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListBillingCode(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "type", required = false, defaultValue = "CURRENT") String type,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListBillingCode(name, type, pageInfo.getPageable());
    }

    @GetMapping("/get-combobox/affiliate-name")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListAffiliateName(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListAffiliateName(name, pageInfo.getPageable());
    }

    @GetMapping("/get-combobox/affiliate-email")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListAffiliateEmail(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListAffiliateEmail(name, pageInfo.getPageable());
    }

    @GetMapping("/get-combobox/affiliate-code")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListAffiliateCode(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListAffiliateCode(name, pageInfo.getPageable());
    }

    @GetMapping("/get-combobox/created-by")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Slice<ValueDTO> getListCreatedBy(
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(value = "type", required = false, defaultValue = "CURRENT") String type,
            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false, defaultValue = "id,asc") String sort ) {
        BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
        return affiliateCommissionBillService.getListCreatedBy(name, type, pageInfo.getPageable());
    }
}
