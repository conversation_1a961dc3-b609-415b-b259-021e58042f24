package com.controller.rating;

import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.dto.rating.DetailRatingResDTO;
import com.dto.rating.ServiceEvalFeedbackDTO;
import com.dto.rating.request.VoteAndCmtReqComboDTO;
import com.dto.rating.request.VoteAndCmtReqDTO;
import com.dto.rating.request.VoteAndCmtUpdateComboReqDTO;
import com.dto.rating.request.VoteAndCmtUpdateReqDTO;
import com.dto.rating.response.RatingDetailDTO;
import com.dto.rating.response.VoteAndCmtResComboDTO;
import com.dto.rating.response.VoteAndCmtResDTO;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.service.rating.RatingCombo;
import com.service.rating.RatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 26/03/2021
 */
@Slf4j
@RestController
@RequestMapping("/api/sme-portal/rating")
public class RatingSmeController {
	
	@Autowired
	private RatingService ratingService;


	@Autowired
	private RatingCombo ratingCombo;

	/**
	 * SME tạo đánh giá, nhận xét cho dịch vụ
	 */
	@Operation(description = "SME tạo đánh giá, nhận xét cho dịch vụ")
	@PostMapping("/{serviceId}")
	public ResponseEntity<VoteAndCmtResDTO> createRating(
			@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
			@PathVariable("serviceId") Long serviceId,
			@Valid @RequestBody VoteAndCmtReqDTO voteAndCmtReqDTO) {
		log.info("--- Execute createRating method: Start--");
		voteAndCmtReqDTO.setServiceId(serviceId);
		VoteAndCmtResDTO result = ratingService.create(voteAndCmtReqDTO);
		log.info("--- Execute createRating method: End--");
		return ResponseEntity.ok(result);
	}

	/**
	 * SME tạo đánh giá, nhận xét cho subscription
	 */
	@Operation(description = "SME tạo đánh giá, nhận xét cho subscription")
	@PostMapping("/subscription/{subId}")
	public ResponseEntity<VoteAndCmtResDTO> createRatingSub(
		@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
		@PathVariable("subId") Long subId,
		@Valid @RequestBody VoteAndCmtReqDTO voteAndCmtReqDTO) {
		log.info("--- Execute createRating subscription method: Start--");
		voteAndCmtReqDTO.setSubId(subId);
		VoteAndCmtResDTO result = ratingService.createRateSub(voteAndCmtReqDTO);
		log.info("--- Execute createRating subscription method: End--");
		return ResponseEntity.ok(result);
	}

	@Operation(description = "POP-UP nhận xét cho subscription")
	@GetMapping("/subscription/{subId}")
	public RatingDetailDTO getRatingSub(
		@PathVariable("subId") Long subId) {
		return ratingService.getRateSub(subId);
	}

	/**
	 * SME tạo đánh giá, nhận xét cho combo
	 */
	@Operation(description = "SME tạo đánh giá, nhận xét cho combo")
	@PostMapping("/{comboId}/combo")
	public ResponseEntity<VoteAndCmtResComboDTO> createRatingCombo(
		@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
		@PathVariable("comboId") Long comboId,
		@Valid @RequestBody VoteAndCmtReqComboDTO voteAndCmtReqComboDTO) {
		log.info("--- Execute createRatingCombo method: Start--");
		voteAndCmtReqComboDTO.setComboId(comboId);
		VoteAndCmtResComboDTO result = ratingCombo.create(voteAndCmtReqComboDTO);
		log.info("--- Execute createRatingCombo method: End--");
		return ResponseEntity.ok(result);
	}

	/**
	 * SME xem chi tiết đánh giá dịch vụ
	 */
	@GetMapping("/{id}")
	@Operation(description = "SME xem chi tiết đánh giá dịch vụ")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public ResponseEntity<DetailRatingResDTO> getDetailRatingOfSaas(
			@Parameter(description = "ID của SaaS", example = "1")
			@PathVariable("id") Long id) {
		log.info("--- Execute getDetailRatingOfSaas method: Start--");
		DetailRatingResDTO detailRatingResDTO = ratingService.getDetailRatingOfSaas(id);
		log.info("--- Execute getDetailRatingOfSaas method: End--");
		return ResponseEntity.ok(detailRatingResDTO);
	}

	/**
	 * SME xem chi tiết đánh giá combo
	 */
	@GetMapping("/{id}/combo")
	@Operation(description = "SME xem chi tiết đánh giá combo")
	public ResponseEntity<DetailRatingResDTO> getDetailRatingOfCombo(
		@Parameter(description = "ID của combo", example = "1")
		@PathVariable("id") Long id) {
		log.info("--- Execute getDetailRatingOfCombo method: Start--");
		DetailRatingResDTO detailRatingResDTO = ratingService.getDetailRatingOfCombo(id);
		log.info("--- Execute getDetailRatingOfCombo method: End--");
		return ResponseEntity.ok(detailRatingResDTO);
	}

	/**
	 * SME cập nhật trạng thái comment là hữu ích
	 */
	@PostMapping("/like-comment/{commentId}")
	@Operation(description = "SME cập nhật trạng thái comment là hữu ích")
	public ResponseEntity<Void> editCommentLikedStatus(
			@Parameter(description = "Id của comment", example = "1")
			@PathVariable("commentId") Long commentId) {
		log.info("--- Execute editCommentLikedStatus method: Start--");
		ratingService.editCommentLikedStatus(commentId);
		log.info("--- Execute editCommentLikedStatus method: End--");
		return ResponseEntity.status(HttpStatus.OK).build();
	}

	/**
	 * SME update rate
	 */
	@Operation(description = "SME cập nhật đánh giá, nhận xét cho dịch vụ")
	@PutMapping("/{serviceId}")
	public ResponseEntity<VoteAndCmtResDTO> updateRating(
			@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
			@PathVariable("serviceId") Long serviceId,
			@Valid @RequestBody VoteAndCmtUpdateReqDTO voteAndCmtUpdateReqDTO) {
		log.info("--- Execute updateRating method: Start--");
		voteAndCmtUpdateReqDTO.setServiceId(serviceId);
		VoteAndCmtResDTO result = ratingService.update(voteAndCmtUpdateReqDTO);
		log.info("--- Execute updateRating method: End--");
		return ResponseEntity.ok(result);
	}

	/**
	 * SME update rate
	 */
	@Operation(description = "SME cập nhật đánh giá, nhận xét cho subscription")
	@PutMapping("/subscription/{subId}")
	public ResponseEntity<VoteAndCmtResDTO> updateRatingSub(
		@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
		@PathVariable("subId") Long subId,
		@Valid @RequestBody VoteAndCmtUpdateReqDTO voteAndCmtUpdateReqDTO) {
		log.info("--- Execute updateRating subscription method: Start--");
		voteAndCmtUpdateReqDTO.setSubId(subId);
		VoteAndCmtResDTO result = ratingService.updateRateSub(voteAndCmtUpdateReqDTO);
		log.info("--- Execute updateRating subscription method: End--");
		return ResponseEntity.ok(result);
	}

	/**
	 * SME update  combo rate
	 */
	@Operation(description = "SME cập nhật đánh giá, nhận xét cho combo")
	@PutMapping("/{comboId}/update-combo")
	public ResponseEntity<VoteAndCmtResComboDTO> updateRatingCombo(
		@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
		@PathVariable("comboId") Long comboId,
		@Valid @RequestBody VoteAndCmtUpdateComboReqDTO voteAndCmtUpdateComboReqDTO) {
		log.info("--- Execute updateRatingCombo method: Start--");
		voteAndCmtUpdateComboReqDTO.setComboId(comboId);
		VoteAndCmtResComboDTO result = ratingService.updateCombo(voteAndCmtUpdateComboReqDTO);
		log.info("--- Execute updateRatingCombo method: End--");
		return ResponseEntity.ok(result);
	}

	/**
	 * SME lay danh sach nhan xet dich vu, phan hoi cua nhan xet tren he thong
	 *
	 * @param serviceId ma dich vu
	 * @param page page
	 * @param size size
	 * @return ServiceEvalFeedbackDTO
	 */
	@Operation(description = "SME lay danh sách nhận xét dịch vụ, phản hồi của nhận xét trên hệ thống")
	@GetMapping("/service/{serviceId}/comment")
	public Page<ServiceEvalFeedbackDTO> findServiceEvalFeedback(
		@Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
		@PathVariable Long serviceId,
		@Parameter(description = Service.SERVICE_TYPE, example = "SERVICE")
		@RequestParam(name = "type", required = false, defaultValue = "0") String type,
		@Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
		@RequestParam(name = "page", required = false, defaultValue = "0") int page,
		@Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
		@RequestParam(name = "size", required = false, defaultValue = "10") int size) {
		log.info("--- Execute findServiceEvalFeedback method: Start--");
		page = page < 0 ? 0 : page;
		size = size < 1 ? 1 : size;
		Pageable pageable = PageRequest.of(page, size, Sort.unsorted());
		Page<ServiceEvalFeedbackDTO> serviceEvalFeedback = ratingService.findServiceEvalFeedback(serviceId, type, true, pageable);
		log.info("--- Execute findServiceEvalFeedback method: End--");
		return serviceEvalFeedback;
	}
	/**
	 * SME xem chi tiết đánh giá dịch vụ
	 */
	@GetMapping("/combo/{id}")
	@Operation(description = "SME xem chi tiết đánh giá combo")
	public ResponseEntity<DetailRatingResDTO> getDetailRatingOfCombogetDetailRatingOfCombo(
			@Parameter(description = "ID của SaaS", example = "1")
			@PathVariable("id") Long id) {
		log.info("--- Execute getDetailRatingOfSaas method: Start--");
		DetailRatingResDTO detailRatingResDTO = ratingService.getDetailRatingOfCombo(id);
		log.info("--- Execute getDetailRatingOfSaas method: End--");
		return ResponseEntity.ok(detailRatingResDTO);
	}
}
