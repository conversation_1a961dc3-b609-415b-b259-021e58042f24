package com.controller.customerTicket;

import java.util.Date;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController;
import com.constant.enums.customerTicket.CustomerTicketStatusEnum;
import com.dto.customerTicket.TicketResponseReqDTO;
import com.dto.customerTicket.requestDTO.CustomerTicketCreateDTO;
import com.dto.customerTicket.requestDTO.CustomerTicketUpdateDTO;
import com.dto.customerTicket.requestDTO.TicketDescriptionUpdateDTO;
import com.dto.customerTicket.responseDTO.IGetListCustomerTicket;
import com.dto.customerTicket.responseDTO.TicketDetailsSmeDTO;
import com.dto.customerTicket.responseDTO.TicketSupportHistoryDTO;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.values.DatabaseConstant;
import com.service.customerTicket.CustomerTicketService;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> TinhNX
 * @version    : 1.0
 * 19/1/2021
 */
@RestController
@RequestMapping("/api/sme-portal/tickets")
@Slf4j
public class CustomerTicketSMEController {

	@Autowired
	private CustomerTicketService customerTicketService;

	/**
	 * Thêm phản hồi phiếu hỗ trợ
	 */
	@PostMapping("/add-respond")
	public void createTicketResponse(@RequestBody TicketResponseReqDTO requestDTO){
		customerTicketService.createTicketResponse(requestDTO, PortalType.SME);
	}

	/**
	 * Sửa phản hồi phiếu hỗ trợ
	 */
	@PostMapping("/update-response")
	public void updateTicketResponse(@RequestBody TicketResponseReqDTO requestDTO) {
		customerTicketService.updateTicketResponse(requestDTO);
	}

	/**
	 * Xóa phản hồi phiếu hỗ trợ
	 */
	@DeleteMapping("/delete-response")
	public void deleteTicketResponse(@RequestBody TicketResponseReqDTO requestDTO) {
		customerTicketService.deleteTicketResponse(requestDTO, PortalType.SME);
	}

	/**
	 * Tạo mới ticket với quyền SME
	 */
	@PostMapping("/create-ticket")
	public void createTicketSME(@RequestBody CustomerTicketCreateDTO createDTO){
		customerTicketService.createTicket(createDTO,PortalType.SME);
	}

	/**
	 * Lấy thông tin chi tiết ticket SME
	 */
	@GetMapping("/detail-ticket")
	public TicketDetailsSmeDTO getDetailsTicket(@RequestParam(name = "id") Long id)
	{
		log.info("--- Execute getDetailsTicket  method: Start ---");
		return customerTicketService.getTicketsDetail(id,PortalType.SME);
	}

	@GetMapping("/get-list")
	@Operation(description = "API xem danh sách phiếu hỗ trợ")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public Page<IGetListCustomerTicket> getListCustomerTicket(
		@RequestParam(value = "search", required = false, defaultValue = "") String search,
		@RequestParam(value = "lstStatus", required = false, defaultValue = "-1") Set<Integer> lstStatus,
		@RequestParam(value = "serviceName", required = false, defaultValue = "") String serviceName,
		@RequestParam(value = "lstSupportType", required = false, defaultValue = "-1") Set<Integer> lstSupportType,
		@RequestParam(name = "createAt", required = false, defaultValue = "01/01/1970")
		@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) Date createAt,
		@RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(value = "size", required = false, defaultValue = "50") Integer size,
		@RequestParam(value = "sort", required = false, defaultValue = "id,desc") String sort) {

		BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
		return customerTicketService.getListTicketSME(search, serviceName, lstStatus, lstSupportType, createAt, pageInfo.getPageable());
	}


	/**
	 * Cập nhật thông tin phiếu hỗ trợ SME Portal
	 */
	@PostMapping("/update-info/{id}")
	public void updateTicketInfos(@PathVariable(value = "id") Long id,
		@RequestBody CustomerTicketUpdateDTO ticketUpdateDTO){
		customerTicketService.updateTicketInfos(id,ticketUpdateDTO,PortalType.SME);
	}

	/**
	 * Cập nhật trạng thái phiếu hỗ trợ SME Portal
	 */
	@PostMapping("/update-status/{id}")
	public void updateTicketStatus(@PathVariable(name = "id") Long id) {
		customerTicketService.updateTicketStatus(id, CustomerTicketStatusEnum.RESOLVED, PortalType.SME);
	}

	/**
	 * Cập nhật mô tả phiếu hỗ trợ ( tiêu đề, mô tả, file đính kèm ) cho SME Portal
	 */
	@PostMapping("/update-description/{id}")
	public void updateTicketDescription(@PathVariable(name = "id") Long id,
		@RequestBody TicketDescriptionUpdateDTO updateDTO) {
		customerTicketService.updateDescription(id, updateDTO, PortalType.SME);
	}

	/**
	 * Lấy danh sách lịch sử hỗ trợ SME portal
	 */
	@GetMapping("/get-support-history")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public Page<TicketSupportHistoryDTO> getListTicketSupportHistory(@RequestParam(name = "ticketId") Long ticketId,
		@RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
		@RequestParam(value = "sort", required = false, defaultValue = "createdAt,desc") String sort) {
		BaseController.ListRequest pageInfo = new BaseController.ListRequest(size, page, sort);
		return customerTicketService.getListTicketResponse(ticketId, pageInfo.getPageable());
	}

	@GetMapping("check-exist-by-ticket-title")
	public boolean existByTicketTitle(@RequestParam(name = "ticketTitle") String ticketTitle) {
		return customerTicketService.existByTicketTitle(ticketTitle);
	}
}
