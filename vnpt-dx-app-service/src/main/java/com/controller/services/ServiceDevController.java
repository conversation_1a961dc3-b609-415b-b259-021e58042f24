package com.controller.services;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;

import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ProviderTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import com.component.BaseController;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.services.ServiceStatusEnum;
import com.dto.addons.AddonDeleteResDTO;
import com.dto.addons.AddonIdsReqDTO;
import com.dto.services.ApiAppDTO;
import com.dto.services.CreateStepOneDTO;
import com.dto.services.ServiceAddonDetailDTO;
import com.dto.services.ServiceCreateDTO;
import com.dto.services.ServiceCreateResponseDTO;
import com.dto.services.ServiceDTO;
import com.dto.services.ServiceFeatureDTO;
import com.dto.services.ServiceResponseDTO;
import com.dto.services.ServiceSearchDTO;
import com.dto.services.ServiceSnapDTO;
import com.dto.services.ServiceSuggestionUpdateDTO;
import com.dto.services.ServiceSupportDTO;
import com.dto.services.ServiceTopicUpdateRequest;
import com.dto.services.ServiceUpdateAllDTO;
import com.dto.services.ServiceUpdateDTO;
import com.dto.services.ServiceUpdateDescDTO;
import com.dto.services.UpdateServiceResDTO;
import com.entity.services.ServiceDraft;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.dto.base.ResponseDataConfiguration;
import com.service.services.ServicesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> KhanhND2
 * @version    : 1.0
 * 26/1/2021
 */
@RestController
@RequestMapping(value={"/api/dev-portal/services", "/api/dev-portal/support-delete"})
@Slf4j
@Validated
public class ServiceDevController extends BaseController {

    @Autowired
    private ServicesService servicesService;

    /**
     * Cap nhat services
     *
     * @return services
     */
    @PutMapping("/{id}/description")
    @Operation(description = "Cập nhật mô tả dịch vụ SaaS.")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ResponseEntity<Void> updateDescInfo(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID, required = true)
        @PathVariable Long id,
        @Valid @RequestBody ServiceUpdateDescDTO paramDto) {
        paramDto.setId(id);
        servicesService.updateDescInfo(paramDto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * Chi tiet co ban dich vu
     *
     */
    @GetMapping("/{id}/basic/{type}")
    @Operation(description = "Xem thông tin cơ bản của dịch vụ SaaS")
    public ResponseEntity<ServiceDTO> findBasicById(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID, required = true)
        @PathVariable Long id,
        @Parameter(description = Service.ID, example = Example.ID, required = true)
        @PathVariable PricingDetailInputEnum type) {
        ServiceDraft serviceDraft = servicesService.findByServiceId(id);
        ServiceDTO dataRespond = servicesService.findBasicById(id, type, serviceDraft, "DEV");
        return ResponseDataConfiguration.success(dataRespond);
    }

    /**
     * Chi tiet co ban dich vu
     *
     */
    @GetMapping("/{id}/basic/{type}/action/{serviceDraftId}")
    @Operation(description = "Xem thông tin cơ bản của dịch vụ SaaS")
    public ResponseEntity<ServiceDTO> findBasicById(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID, required = true)
        @PathVariable Long id,
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long serviceDraftId,
        @Parameter(description = Service.ID, example = Example.ID, required = true)
        @PathVariable PricingDetailInputEnum type) {
        ServiceDraft serviceDraft = servicesService.findById(serviceDraftId);
        ServiceDTO dataRespond = servicesService.findBasicById(id, type, serviceDraft, "DEV");
        return ResponseDataConfiguration.success(dataRespond);
    }

    /**
     * Cap nhat services
     *
     * @return services
     */
    @PutMapping("/{id}/basic")
    @Operation(description = "Cập nhật thông tin cơ bản của dịch vụ SaaS.")
    public ResponseEntity<Void> updateBaseInfo(
        @Valid @RequestBody ServiceUpdateDTO paramDto,
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID, required = true)
        @PathVariable Long id) {
        paramDto.setId(id);
        paramDto.setOnOsType(Objects.nonNull(paramDto.getServiceTypeApplication()) ? OnOsTypeEnum.ON
                : OnOsTypeEnum.fromValue(paramDto.getServiceOwner(), paramDto.getServiceOwnerPartner()));
        paramDto.setProviderType(Objects.nonNull(paramDto.getServiceTypeApplication()) ? ProviderTypeEnum.THIRD_PARTY
                : ProviderTypeEnum.fromValue(paramDto.getServiceOwner(), paramDto.getServiceOwnerPartner()));
        servicesService.updateBaseInfo(paramDto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * tao moi 1 service
     *
     * @param serviceDTO : doi tuong service DTO,
     *
     * @return : serviceDTO sau khi update
     */
    @PostMapping
    @Operation(description = "Đăng ký phát hành dịch vụ SaaS")
    public ResponseEntity<ServiceCreateResponseDTO> createService(@Valid @RequestBody ServiceCreateDTO serviceDTO) {
        ServiceCreateResponseDTO save = servicesService.create(serviceDTO, PortalType.DEV);
        return ResponseDataConfiguration.success(save);
    }

    @PostMapping("/create/application/step1")
    @Operation(description = "Đăng ký ứng dụng bước 1")
    public ResponseEntity<ServiceCreateResponseDTO> createApplicationStepOne(@Valid @RequestBody CreateStepOneDTO createStepOneDTO) {
        ServiceCreateResponseDTO save = servicesService.createApplicationStepOne(createStepOneDTO);
        return ResponseDataConfiguration.success(save);
    }

    /**
     * Lay danh sach services
     *
     */
    @GetMapping()
    @Operation(description = "Tìm kiếm dịch vụ SaaS quyền developer")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<ServiceResponseDTO>> findAll(
        @Valid ServiceSearchDTO searchDTO,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "updatedTime,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        Page<ServiceResponseDTO> dataRespond = servicesService.findAllForDev(searchDTO, listRequest.getPageable());
        return new ResponseEntity<>(dataRespond, HttpStatus.OK);
    }

    /**
     * Lay danh sach services
     *
     */
    @GetMapping("/get-list-api")
    @Operation(description = "Lấy danh sách API")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<ApiAppDTO>> getListApi() {
        List<ApiAppDTO> apiAppList = servicesService.getListApiChill();
        return new ResponseEntity<>(apiAppList, HttpStatus.OK);
    }

    /**
     * cap nhat trang thai cua service
     *
     * @param id      :serviceId
     * @param status: gia tri status
     *
     * @return : NO_CONTENT
     */
    @PutMapping("/{id}/displayed/{status}")
    @Operation(description = "Ngừng/tiếp tục cung cấp dịch vụ SaaS.")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ResponseEntity<Void> updateStatus(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long id,
        @Parameter(description = SwaggerConstant.Service.DISPLAY, example = SwaggerConstant.Example.DISPLAY)
        @PathVariable ServiceStatusEnum status) {
        servicesService.updateServiceStatus(id, status);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * API phe duyet yeu cau approve
     *
     * @param id :serviceId,
     *
     * @return : NO_CONTENT
     */
    @PutMapping("/{id}/request-for-approval")
    @Operation(description = "Gửi yêu cầu phê duyệt dịch vụ SaaS")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ResponseEntity<Void> approveRequest(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long id) {
        servicesService.requestApprove(id, false);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/request-for-approval")
    @Operation(description = "Gửi yêu cầu phê nhiều duyệt dịch vụ SaaS")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ResponseEntity<Void> approveMultiRequest(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @RequestBody List<Long> ids) {
        servicesService.requestMultiApprove(ids);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * Cap nhat thong tin ho tro
     *
     * @return services
     */
    @PutMapping("/{id}/support")
    @Operation(description = "Cập nhật thông tin hỗ trợ của dịch vụ")
    public ResponseEntity<Void> updateSupportInfo(
        @Valid @RequestBody ServiceSupportDTO paramDto,
        @PathVariable Long id) {
        paramDto.setId(id);
        servicesService.updateSupportInfo(paramDto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * Cap nhat thong tin bố cục
     *
     * @return services
     */
    @PutMapping("/{id}/snap")
    @Operation(description = "Cập nhật thông tin bố cục hiển thị")
    public ResponseEntity<Void> updateSnaps(
        @Valid @RequestBody ServiceSnapDTO paramDto,
        @PathVariable Long id) {
        paramDto.setId(id);
        paramDto.setType(FileAttachTypeEnum.CAPTURE.value);
        servicesService.updateSnaps(paramDto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * Cap nhat thong tin công nghệ nổi bật
     *
     * @return services
     */
    @PutMapping("/{id}/tech")
    @Operation(description = "Cập nhật thông tin công nghệ nổi bật")
    public ResponseEntity<Void> updateTechs(
        @Valid @RequestBody ServiceSnapDTO paramDto,
        @PathVariable Long id) {
        paramDto.setId(id);
        paramDto.setType(FileAttachTypeEnum.TECH.value);
        servicesService.updateSnaps(paramDto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * Cap nhat thong tính năng
     *
     * @return services
     */
    @PutMapping("/{id}/feature")
    @Operation(description = "Cập nhật thông tin tính năng")
    public ResponseEntity<Void> updateFeatures(
        @Validated @RequestBody ServiceFeatureDTO paramDto,
        @PathVariable Long id) {
        paramDto.setId(id);
        servicesService.updateFeatures(paramDto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @PutMapping("/{id}/serviceSuggestion")
    public void addServiceSuggestion(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long id,
        @RequestBody ServiceSuggestionUpdateDTO req) {
        servicesService.updateSeggestionService(id,  req);
    }

    @PutMapping("/{id}/topic")
    public void addTopic(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @PathVariable Long id,
        @RequestBody List<ServiceTopicUpdateRequest> reqs) {
        servicesService.updateTopic(id,  reqs);
    }

    /**
     * Tìm kiếm dịch vụ, combo SaaS quyền developer
     */
    @GetMapping("/combo")
    @Operation(description = "Tìm kiếm dịch vụ, combo SaaS quyền developer")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<ServiceAddonDetailDTO>> findServiceAndCombo(
        @Parameter String search,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "serviceName,asc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        Page<ServiceAddonDetailDTO> dataRespond = servicesService.findFullServiceForDev(search, listRequest.getPageable());
        return new ResponseEntity<>(dataRespond, HttpStatus.OK);
    }

    @DeleteMapping("/delete")
    @Operation(description = "xoa service")
    public AddonDeleteResDTO deleteService(
        @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
        @Validated(AddonIdsReqDTO.Delete.class) @RequestBody AddonIdsReqDTO ids) {
        return servicesService.deleteServices(ids);
    }

    @PutMapping("/{serviceId}")
    @Operation(description = "Cập nhật thông tin dịch vụ")
    public ResponseEntity<UpdateServiceResDTO> updateService(
        @Valid @RequestBody ServiceUpdateAllDTO serviceDTO,
        @PathVariable Long serviceId) {
        UpdateServiceResDTO res = servicesService.updateService(serviceDTO, serviceId, PortalType.DEV);
        return (res.getLstPricing().isEmpty() && res.getLstAddon().isEmpty()) ?
                ResponseEntity.status(HttpStatus.OK).body(res) : ResponseEntity.status(HttpStatus.BAD_REQUEST).body(res);
    }

    @GetMapping("/check-feature/{serviceId}/{featureId}")
    @Operation(description = "Kiểm tra tính năng đã được sử dụng trong dv hay chưa")
    public Integer checkDeleteFeatureInService(@PathVariable Long featureId, @PathVariable Long serviceId) {
        return servicesService.checkDeleteFeatureInService(Collections.singletonList(featureId), serviceId);
    }
}
