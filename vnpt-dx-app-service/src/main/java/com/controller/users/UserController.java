package com.controller.users;

import java.util.Date;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.component.BaseController;
import com.dto.actionHistory.UserActionHistoryResDTO;
import com.dto.subscriptions.CouponMcPopupDTO;
import com.dto.users.IDupplicateUserDTO;
import com.dto.users.UserAssigneePartitionUpdateDTO;
import com.entity.file.attach.FileAttach;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.dto.base.ResponseDataConfiguration;
import com.onedx.common.utils.DateUtil;
import com.service.users.AccountUpdateHistoryService;
import com.service.users.UserService;
import com.service.utils.jsonObject.McConditionItemGroupDTO;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> ToanCM
 * @version    : 1.0
 * 14/1/2021
 */
@RestController
@RequestMapping("/api/portal/user")
@Slf4j
public class UserController extends BaseController {

	@Autowired
	UserService userService;

	@Autowired
	AccountUpdateHistoryService accountUpdateHistoryService;
	
	/**
	 * Cap nhat developer role
	 * @return Trang thai cap nhat
	 */
	@PutMapping("/users/{id}/{userType}")
	public ResponseEntity<Object> updateDeveloperRole(@PathVariable Long id, @PathVariable Integer useType) {
		userService.updateDeveloperRole(id, useType);
		return ResponseEntity.ok().body(HttpStatus.OK);
	}

	/**
	 * Upload anh
	 *
     */
	@PostMapping(value = "/file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<Object> uploadImage(
			@RequestParam(value = "files") MultipartFile[] files,
			Long fileSize
	) {
		log.info("--- Execute uploadImage method: Start--");
		List<FileAttach> fileAttaches = userService.uploadFile(files, fileSize);
		log.info("--- Execute uploadImage method: End--");
		return ResponseDataConfiguration.success(fileAttaches);
	}

	@Description("Hàm upload ảnh và cập nhật luôn theo logic của Workplace")
	@PostMapping(value = "/file-workplace/update/{objectType}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public FileAttach updateFileWorkplace(
		@RequestParam(value = "file") MultipartFile file,
		@PathVariable FileAttachTypeEnum objectType
	) {
		return userService.updateFileWorkplace(file, objectType);
	}

	@GetMapping("/history/{id}")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public ResponseEntity<Page<UserActionHistoryResDTO>> updateHistory(
			@Parameter(description = "ID", example = "1") @PathVariable Long id,
			@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
			@RequestParam(name = "startDate", required = false, defaultValue = "01/01/1970") Date startDate,
			@DateTimeFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH)
			@RequestParam(name = "endDate", required = false, defaultValue = "01/01/1970") Date endDate,
			@Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
			@RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
			@Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
			@RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
			@Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
			@RequestParam(name = "sort", required = false, defaultValue = "createdAt,desc") String sortBy) {
		log.info("--- Execute updateHistory method: Start--");
		ListRequest listRequest = new ListRequest(size, page, sortBy);

		Page<UserActionHistoryResDTO> dataRespond = accountUpdateHistoryService.search(id, startDate, endDate, listRequest.getPageable());
		log.info("--- Execute searchAllService method: End --");
		return new ResponseEntity<>(dataRespond, HttpStatus.OK);
	}

	/**
	 * Cập nhật người hỗ trợ cho users
	 */
	@PostMapping("/assign-assignee")
	public void updateAssignee(@RequestBody UserAssigneePartitionUpdateDTO updateDTO) {
		userService.updateAssigneeAndPartition(updateDTO);
	}

	/**
	 * Cập nhật phân vùng cho users
	 */
	@PostMapping("/update-partition")
	public void updatePartition(@RequestBody UserAssigneePartitionUpdateDTO updateDTO) {
		userService.updateAssigneeAndPartition(updateDTO);
	}

	/**
	 * Thêm user vào chiến dịch quảng cáo
	 */
	@PostMapping("/add-to-mc")
	public void addUserToMc(@RequestParam(name = "lstUserId") Set<Long> lstUserId,
		@RequestParam(name = "lstMcId") Set<Long> lstMcId) {
		userService.addUserToMc(lstUserId, lstMcId);
	}

	@GetMapping("/{supplierId}/coupons")
	public List<CouponMcPopupDTO> findAllSupplierCoupon(
		@PathVariable(name = "supplierId") Long supplierId,
		@RequestParam(name = "customerType", required = false, defaultValue = "CN") String customerType
	) {
		return userService.findAllCouponForSupplier(supplierId, customerType);
	}

	@GetMapping("/email/validate")
	@Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
	public void validateUserEmail(
		@RequestParam(name = "customerType", required = false, defaultValue = "PERSONAL") CustomerTypeEnum customerType,
		@RequestParam(name = "email") String email
	) {
		userService.validateUserEmail(customerType, email);
	}

	@PreAuthorize("@dynamicAccessAspect.hasPermission('XEM_DANH_SACH_DOANH_NGHIEP_1')")
	@PostMapping("/dupplicate-detection")
	public Page<IDupplicateUserDTO> getDupplicateUsers(
		@RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
		@RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
		@RequestParam(value = "customerType", required = false, defaultValue = "ALL") String customerType,
		@RequestBody List<McConditionItemGroupDTO> conditions) {
		return userService.getDupplicateUsers(customerType, conditions, PageRequest.of(page, size));
	}
}
