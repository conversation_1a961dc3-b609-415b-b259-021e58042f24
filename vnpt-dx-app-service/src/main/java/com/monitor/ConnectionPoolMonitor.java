package com.monitor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.config.db.DataStackTrace;
import com.config.db.LoggingDataSource;
import com.config.db.RoutingDataSource;
import com.config.db.RoutingDataSource.DBRoute;
import com.enums.MessageLevelEnum;
import com.onedx.common.helpers.sendBizflyEmail.BizflySendMailNow;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@Profile({"prod", "bat"})
public class ConnectionPoolMonitor {

    public static final String HIKARI_CP_POOL_METRICS_WARNING_MSG = "The server has active connections reached %d%% of max pool.%nActive = %d, MaxPool = %d, Total = %d, Waiting = %d, Idle = %d";

    @Autowired
    private DataSource dataSource;

    @Autowired
    private BizflySendMailNow bizflySendMailNow;

    @Autowired
    private TelegramNotifyService telegramNotifyService;

    @Value("${warning-system.emails:<EMAIL>}")
    private String emails;

    @Value("${warning-system.tele-chat-ids-max-pool-size-connection:-*************}")
    private String teleChatIds;

    @Value("${warning-system.millisecond-warning-interval: 100000}")
    private int millisecondWarningInterval;

    private List<String> emailWarningLst;

    @PostConstruct
    public void init() {
        if (this.emailWarningLst == null) {
            this.emailWarningLst = new ArrayList<>();
            this.emailWarningLst.addAll(Arrays.asList(this.emails.split(",")));
        }
        this.telegramChatIds.addAll(Arrays.stream(teleChatIds.split(",")).collect(Collectors.toSet()));
    }

    private final Set<String> telegramChatIds = new HashSet<>();
    private long lastWarningTime = 0;

    private boolean shouldSendWarning() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastWarningTime > millisecondWarningInterval) {
            lastWarningTime = currentTime;
            return true;
        }
        return false;
    }

    @Scheduled(fixedDelayString = "${warning-system.monitor-period:10000}")  // Every 10 seconds
    public void monitorConnectionPool() {
        RoutingDataSource routingDataSource = (RoutingDataSource) dataSource;
        Map<Object, DataSource> resolvedDataSources = routingDataSource.getResolvedDataSources();
        resolvedDataSources.forEach((k, realDataSource) -> {
            if (realDataSource instanceof LoggingDataSource && Objects.equals(k, DBRoute.WRITEABLE)) {
                LoggingDataSource loggingDataSource = (LoggingDataSource) realDataSource;
                HikariDataSource hikariDataSource = (HikariDataSource) loggingDataSource.getDelegateDataSource();
                int maximumPoolSize = hikariDataSource.getMaximumPoolSize();
                HikariPoolMXBean hikariPoolMXBean = hikariDataSource.getHikariPoolMXBean();
                int activeConnections = hikariPoolMXBean.getActiveConnections();
                int idleConnections = hikariPoolMXBean.getIdleConnections();
                int totalConnections = hikariPoolMXBean.getTotalConnections();
                int threadsAwaitingConnection = hikariPoolMXBean.getThreadsAwaitingConnection();
                checkAndWarn(maximumPoolSize, activeConnections, idleConnections, totalConnections, threadsAwaitingConnection, loggingDataSource);
                // if send email, Must send email not in database because connection to database reach to full
               }
        });
    }

    private void checkAndWarn(int maxPool, int active, int idle, int total, int waiting, LoggingDataSource loggingDataSource) {
        boolean isMaxUsed = active == maxPool;
        int threshold = isMaxUsed ? 100 : (int) ((double) active / maxPool * 100 + 0.5);
        if (active >= maxPool * 0.8 && shouldSendWarning()) {
            MessageLevelEnum messageLevelEnum = isMaxUsed ? MessageLevelEnum.WARNING : MessageLevelEnum.INFO;
            String warningContent = String.format(HIKARI_CP_POOL_METRICS_WARNING_MSG,
                 threshold, active, maxPool, total, waiting, idle);
            telegramNotifyService.sendTelegramMessageWarning(warningContent, messageLevelEnum, this.telegramChatIds);
            writeStackTraceLog(loggingDataSource);
        }
    }

    private static void writeStackTraceLog(LoggingDataSource loggingDataSource) {
        StringBuilder stringBuilder = new StringBuilder();
        Map<String, DataStackTrace> mapDataStackTrace = loggingDataSource.mapDataStackTrace;
        mapDataStackTrace.forEach((key, value) -> {
            if (value.getNumOfConGetNotRel() > 0) {
                String logContent = String.format("Thread %s: %s %n", key, value);
                stringBuilder.append(logContent);
            }
        });
        String logStackTrace = stringBuilder.toString();
        log.warn(logStackTrace);
    }

    // bỏ bớt tính năng gửi email cảnh báo
    private void sendEmailsWarning(String subjectHighConnection, String warningContent) {
        for (String email : emailWarningLst) {
            sendAlertEmail(email, subjectHighConnection, warningContent);
        }
    }

    void sendAlertEmail(String email, String subject, String content) {
        bizflySendMailNow.sendMailWarningSystem(email, subject, content);
    }

}
