package com.constant.sql;

public class S<PERSON><PERSON><PERSON>mon {
    public static final String GET_ALL_SERVICE_COMBO_ADDON_INFO =
            "SELECT DISTINCT\n" +
                    "    service_combo_addon.uniqueId,\n" +
                    "    service_combo_addon.name,\n" +
                    "    service_combo_addon.type,\n" +
                    "    service_combo_addon.ID\n"+
                    "FROM (\n" +
                    "    SELECT\n" +
                    "        0::::INT2 AS type,\n" +
                    "        services.ID as ID,\n"+
                    "        services.service_name AS name,\n" +
                    "        concat ( services.ID, '0000' )::::bigint AS uniqueId,\n" +
                    "        services.categories_id AS categories_id,\n" +
                    "        CASE \n" +
                    "            WHEN services.service_owner IN (0, 1) THEN 0\n" +
                    "            ELSE 1\n" +
                    "        END AS service_type,\n" +
                    "        services.deleted_flag as deleted_flag\n "+
                    "    FROM {h-schema}services \n" +
                    "    WHERE \n" +
                    "        services.deleted_flag = 1 and services.status = 1 and services.approve = 1 and \n" +
                    "        (:customerTypeStr = 'ALL' OR services.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "    UNION ALL\n" +
                    "    SELECT\n" +
                    "        1::::INT2 AS type,\n" +
                    "        combo.ID as ID,\n"+
                    "        combo.combo_name AS name,\n" +
                    "        concat ( combo.ID, '0001' )::::bigint AS uniqueId,\n" +
                    "        CAST ( UNNEST ( string_to_array( combo.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
                    "        CASE \n" +
                    "            WHEN combo.combo_owner IN (0, 1) THEN 0\n" +
                    "            ELSE 1\n" +
                    "        END AS service_type,\n" +
                    "        combo.deleted_flag as deleted_flag \n"+
                    "    FROM\n" +
                    "        {h-schema}combo\n" +
                    "    WHERE \n" +
                    "        combo.deleted_flag = 1 and combo.status = 1 and combo.approve = 1 and \n" +
                    "        (:customerTypeStr = 'ALL' OR combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "    UNION ALL\n" +
                    "    SELECT \n" +
                    "        2::::INT2 as type,\n" +
                    "        addons.ID as ID,"+
                    "        addons.name AS name,\n" +
                    "        concat ( addons.ID, '0002' )::::bigint AS uniqueId,\n" +
                    "        null as categories_id,\n" +
                    "        null as service_type,\n" +
                    "        addons.deleted_flag as deleted_flag\n "+
                    "    FROM\n" +
                    "        {h-schema}addons \n" +
                    "    WHERE \n" +
                    "        addons.deleted_flag = 1 and addons.status = 1 and addons.approve = 1 and \n" +
                    "        (:customerTypeStr = 'ALL' OR addons.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    ") AS service_combo_addon\n" +
                    "WHERE \n" +
                     "(service_combo_addon.deleted_flag = 1 AND service_combo_addon.name ILIKE '%' || :filter || '%') AND\n" +
                     "(service_combo_addon.type = 2 OR -1 IN (:categoryList) OR service_combo_addon.categories_id IN (:categoryList) ) AND\n" +
                     "(service_combo_addon.type = 2 OR -1 IN (:serviceTypeList) OR (service_combo_addon.type = 1 AND 2 IN (:serviceTypeList)) OR service_combo_addon.service_type IN (:serviceTypeList))";

    public static final String SEARCH_PROVIDER =
        "select \n" +
            "    mUser.id, \n" +
            "    case \n" +
            "        when mUser.parent_id = -1 then coalesce(mUser.name, '') \n" +
            "        else concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
            "    end as value \n" +
            "from {h-schema}users mUser \n" +
            "    join {h-schema}view_role_dev mUserDev on mUser.id = mUserDev.user_id \n" +
            "where \n" +
            "    (\n" +
            "        :providerName = '' or \n" +
            "        (mUser.parent_id = -1 and coalesce(mUser.name, '') ilike ('%' || :providerName || '%')) or \n" +
            "        (mUser.parent_id != -1 and concat_ws(' ', mUser.last_name, mUser.first_name) ilike ('%' || :providerName || '%')) \n" +
            "    ) and \n" +
            "    mUser.deleted_flag = 1 and \n" +
            "    mUser.status = 1 \n" +
            "limit \n" +
            "   (CASE \n" +
            "     WHEN :size = -1 THEN\n" +
            "         null\n" +
            "	    ELSE\n" +
            "		    :size\n" +
            "   END)";

    public static final String GET_LIST_SUB_USER_MST =
        "SELECT\n" +
            "   DISTINCT (u.id) AS value,\n" +
            "   CASE \n" +
            "        WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number\n" +
            "        ELSE u.tin\n" +
            "   END  as label\n" +
            "FROM {h-schema}feature_view_get_all_sub sub\n" +
            "JOIN {h-schema}subscriptions sub_infor ON sub.sub_id = sub_infor.id\n" +
            "JOIN {h-schema}billings bill ON sub.sub_id = bill.subscriptions_id\n" +
            "LEFT JOIN {h-schema}users u ON sub.user_id = u.id \n" +
            "WHERE (:devId = -1 OR sub.provider_id = :devId ) \n" +
            "    AND (  '' = :filter \n" +
            "        OR (u.customer_type = 'CN' AND u.rep_personal_cert_number ILIKE ('%' || :filter || '%')) \n" +
            "        OR  (u.customer_type != 'CN' AND u.tin ILIKE ('%' || :filter || '%')))\n" +
            "    and (\n" +
            "        (u.customer_type = 'CN' AND u.rep_personal_cert_number is not null)\n" +
            "        or (u.customer_type != 'CN' AND u.tin is not null))";

    public static final String SEARCH_PROVIDER_FULL_DEV =
            "select \n" +
                    "	mUser.id, \n" +
                    "	case \n" +
                    "			when mUser.parent_id = -1 then coalesce(mUser.name, '') \n" +
                    "			else concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
                    "	end as value \n" +
                    "from {h-schema}users mUser \n" +
                    "join (\n" +
                    "	SELECT DISTINCT users_roles.user_id\n" +
                    "	FROM {h-schema}users_roles\n" +
                    "	JOIN {h-schema}role ON role.id = users_roles.role_id\n" +
                    "	WHERE role.name::::text = ANY (ARRAY['FULL_DEV'::::text])\n" +
                    "	) mUserDev on mUser.id = mUserDev.user_id \n" +
                    "where \n" +
                    "    (\n" +
                    "        :providerName = '' or \n" +
                    "        (mUser.parent_id = -1 and coalesce(mUser.name, '') ilike ('%' || :providerName || '%')) or \n" +
                    "        (mUser.parent_id != -1 and concat_ws(' ', mUser.last_name, mUser.first_name) ilike ('%' || :providerName || '%')) \n" +
                    "    ) and \n" +
                    "	mUser.deleted_flag = 1 and \n" +
                    "	mUser.status = 1 \n" +
                    "limit \n" +
                    " (CASE \n" +
                    "	 WHEN :size = -1 THEN\n" +
                    "			 null\n" +
                    "		ELSE\n" +
                    "			:size\n" +
                    " END)";

    public static final String SEARCH_CREATOR_ADDON =
        "--CTE chứa các user_id, portal của những tài khoản đã từng tạo addon \n" +
            "with mUserCreatedAddon as ( \n" +
            "    select distinct \n" +
            "        created_by as user_id, \n" +
            "        portal \n" +
            "    from {h-schema}addon_draft \n" +
            ") \n" +
            "select \n" +
            "    mUser.id, \n" +
            "    case \n" +
            "        when mUserCreatedAddon.portal = 1 then concat('Admin - ', mUser.email) \n" +
            "        else concat('Dev - ', mUser.email) \n" +
            "    end as value \n" +
            "from {h-schema}users mUser \n" +
            "    join mUserCreatedAddon on mUser.id = mUserCreatedAddon.user_id \n" +
            "where \n" +
            "    mUser.email ilike ('%' || :creatorEmail || '%') \n" +
            "limit \n" +
            "    :size";
        
    public static final String GET_ALL_COMBO_SERVICE_INFO =
        "SELECT DISTINCT\n" +
            "    service_combo_addon.uniqueId,\n" +
            "    service_combo_addon.name,\n" +
            "    service_combo_addon.type,\n" +
            "    service_combo_addon.ID\n"+
            "FROM (\n" +
            "    SELECT\n" +
            "        0::::INT2 AS type,\n" +
            "        services.ID as ID,\n"+
            "        services.service_name AS name,\n" +
            "        concat ( services.ID, '0000' )::::bigint AS uniqueId,\n" +
            "        services.categories_id AS categories_id,\n" +
            "        CASE \n" +
            "            WHEN services.service_owner IN (0, 1) THEN 0\n" +
            "            ELSE 1\n" +
            "        END AS service_type,\n" +
            "        services.deleted_flag as deleted_flag\n "+
            "    FROM {h-schema}services \n" +
            "    WHERE (:customerTypeStr = 'ALL' OR services.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
            "    UNION ALL\n" +
            "    SELECT\n" +
            "        1::::INT2 AS type,\n" +
            "        combo.ID as ID,\n"+
            "        combo.combo_name AS name,\n" +
            "        concat ( combo.ID, '0001' )::::bigint AS uniqueId,\n" +
            "        CAST ( UNNEST ( string_to_array( combo.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
            "        CASE \n" +
            "            WHEN combo.combo_owner IN (0, 1) THEN 0\n" +
            "            ELSE 1\n" +
            "        END AS service_type,\n" +
            "        combo.deleted_flag as deleted_flag \n"+
            "    FROM\n" +
            "        {h-schema}combo\n" +
            "      WHERE (:customerTypeStr = 'ALL' OR combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
            ") AS service_combo_addon\n" +
            "WHERE \n" +
            "(service_combo_addon.deleted_flag = 1 AND service_combo_addon.name ILIKE '%' || :filter || '%') AND\n" +
            "(service_combo_addon.type = 2 OR -1 IN (:categoryList) OR service_combo_addon.categories_id IN (:categoryList) ) AND\n" +
            "(service_combo_addon.type = 2 OR -1 IN (:serviceTypeList) OR (service_combo_addon.type = 1 AND 2 IN (:serviceTypeList)) OR service_combo_addon.service_type IN (:serviceTypeList))";

    public static final String GET_ALL_SERVICE_COMBO_ADDON_INFO_SELECTED =
            "SELECT DISTINCT\n" +
                    "    service_combo_addon.uniqueId,\n" +
                    "    service_combo_addon.name,\n" +
                    "    service_combo_addon.type,\n" +
                    "    service_combo_addon.ID\n"+
                    "FROM (\n" +
                    "    SELECT\n" +
                    "        0::::INT2 AS type,\n" +
                    "        services.ID as ID,\n"+
                    "        services.service_name AS name,\n" +
                    "        concat ( services.ID, '0000' )::::bigint AS uniqueId,\n" +
                    "        services.deleted_flag as deleted_flag\n "+
                    "    FROM {h-schema}services \n" +
                    "    UNION ALL\n" +
                    "    SELECT\n" +
                    "        1::::INT2 AS type,\n" +
                    "        combo.ID as ID,\n"+
                    "        combo.combo_name AS name,\n" +
                    "        concat ( combo.ID, '0001' )::::bigint AS uniqueId,\n" +
                    "        combo.deleted_flag as deleted_flag \n"+
                    "    FROM\n" +
                    "        {h-schema}combo\n" +
                    "    UNION ALL\n" +
                    "    SELECT \n" +
                    "        2::::INT2 as type,\n" +
                    "        addons.ID as ID,"+
                    "        addons.name AS name,\n" +
                    "        concat ( addons.ID, '0002' )::::bigint AS uniqueId,\n" +
                    "        addons.deleted_flag as deleted_flag\n "+
                    "    FROM\n" +
                    "        {h-schema}addons \n" +
                    ") AS service_combo_addon\n" +
                    "WHERE \n" +
                    "    service_combo_addon.uniqueId in (:lstSelectedId)";

    public static final String GET_ALL_SERVICE_COMBO_INFO_SELECTED =
            "SELECT DISTINCT\n" +
                    "    service_combo_addon.uniqueId,\n" +
                    "    service_combo_addon.name,\n" +
                    "    service_combo_addon.type,\n" +
                    "    service_combo_addon.ID\n"+
                    "FROM (\n" +
                    "    SELECT\n" +
                    "        0::::INT2 AS type,\n" +
                    "        services.ID as ID,\n"+
                    "        services.service_name AS name,\n" +
                    "        concat ( services.ID, '0000' )::::bigint AS uniqueId,\n" +
                    "        services.deleted_flag as deleted_flag\n "+
                    "    FROM {h-schema}services \n" +
                    "    UNION ALL\n" +
                    "    SELECT\n" +
                    "        1::::INT2 AS type,\n" +
                    "        combo.ID as ID,\n"+
                    "        combo.combo_name AS name,\n" +
                    "        concat ( combo.ID, '0001' )::::bigint AS uniqueId,\n" +
                    "        combo.deleted_flag as deleted_flag \n"+
                    "    FROM\n" +
                    "        {h-schema}combo\n" +
                    ") AS service_combo_addon\n" +
                    "WHERE \n" +
                    "    service_combo_addon.uniqueId in (:lstSelectedId)";

    public static final String GET_ALL_SERVICE_COMBO_INFO =
        "SELECT \n" +
            "    0 AS type,\n" +
            "    s.id AS ID,\n" +
            "    (concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
            "    s.service_name AS Name\n" +
            "FROM {h-schema}services AS s\n" +
            "WHERE (s.deleted_flag = 1) AND (s.service_name ILIKE '%' || :filter || '%')\n" +
            "      AND (:customerTypeStr = 'ALL' OR s.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
            "UNION ALL\n" +
            "SELECT \n" +
            "    1 AS type,\n" +
            "    co.id AS ID,\n" +
            "  (concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
            "  co.combo_name AS Name\n" +
            "FROM {h-schema}combo AS co\n" +
            "WHERE (co.deleted_flag = 1) AND (co.combo_name ILIKE '%' || :filter || '%')\n" +
            "      AND (:customerTypeStr = 'ALL' OR co.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n";
    public static final String GET_ALL_LASTEST_SERVICE_COMBO_INFO =
        "SELECT DISTINCT \n" +
            "    lastest_service_combo.provider AS userId, \n" +
            "    lastest_service_combo.type AS type,\n" +
            "    lastest_service_combo.ID AS ID,\n" +
            "    lastest_service_combo.UniqueId AS UniqueId,\n" +
            "    lastest_service_combo.Name AS Name\n" +
            "FROM ( \n" +
            "(SELECT DISTINCT ON ((COALESCE(s.services_draft_id, s.id)))\n " +
            "    0 AS type,\n" +
            "    s.id AS ID,\n" +
            "    (concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
            "    s.categories_id AS categories_id, \n" +
            "    s.deleted_flag AS deleted_flag, \n" +
            "    s.approve AS approve, \n" +
            "    s.status AS status, \n" +
            "    s.user_id AS provider, \n" +
            "    s.customer_type_code AS customer_type_code," +
            "    s.service_name AS Name\n" +
            "FROM {h-schema}services AS s\n" +
            "JOIN {h-schema}users AS provider ON provider.id = s.user_id " +
            "WHERE s.product_type is null or s.product_type <> 1 \n" +
            "ORDER BY COALESCE(s.services_draft_id, s.id) DESC, s.id DESC \n" +
            ")\n" +
            "UNION ALL\n" +
            "(SELECT DISTINCT ON ((COALESCE(co.combo_draft_id, co.id)))\n" +
            "   1 AS type,\n" +
            "   co.id AS ID,\n" +
            "  (concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
            "  CAST ( UNNEST ( string_to_array( co.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
            "  co.deleted_flag AS deleted_flag, \n" +
            "  co.approve AS approve, \n" +
            "  co.status AS status, \n" +
            "  co.user_id AS provider, \n" +
            "  co.customer_type_code AS customer_type_code," +
            "  co.combo_name AS Name\n" +
            "FROM {h-schema}combo AS co\n" +
            "JOIN {h-schema}users AS provider ON provider.id = co.user_id \n" +
            "ORDER BY COALESCE(co.combo_draft_id, co.id) DESC, co.id DESC \n" +
            ")) AS lastest_service_combo\n" +
            "WHERE ((lastest_service_combo.deleted_flag = 1 AND lastest_service_combo.approve = 1 AND lastest_service_combo.status = 1) AND (lastest_service_combo.Name ILIKE '%' || :filter || '%')\n" +
            "      AND (-1 IN (:categoryList) OR lastest_service_combo.categories_id IN (:categoryList)) \n" +
            "      AND (:userId = -1 OR :userId = lastest_service_combo.provider) \n" +
            "      AND (:customerTypeStr = 'ALL' OR lastest_service_combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%'))\n";

//    public static final String GET_ALL_SERVICE_COMBO =
//            "SELECT DISTINCT \n" +
//                    "    lastest_service_combo.provider AS userId, \n" +
//                    "    lastest_service_combo.type AS type,\n" +
//                    "    lastest_service_combo.ID AS ID,\n" +
//                    "    lastest_service_combo.UniqueId AS UniqueId,\n" +
//                    "    lastest_service_combo.Name AS Name\n" +
//                    "FROM ( \n" +
//                    "(SELECT DISTINCT ON (s.id)\n " +
//                    "    0 AS type,\n" +
//                    "    s.id AS ID,\n" +
//                    "    (concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
//                    "    s.categories_id AS categories_id, \n" +
//                    "    s.deleted_flag AS deleted_flag, \n" +
//                    "    s.approve AS approve, \n" +
//                    "    s.status AS status, \n" +
//                    "    s.user_id AS provider, \n" +
//                    "    s.customer_type_code AS customer_type_code," +
//                    "    s.service_name AS Name\n" +
//                    "FROM {h-schema}services AS s\n" +
//                    "JOIN {h-schema}users AS provider ON provider.id = s.user_id " +
//                    "LEFT JOIN (\n" +
//                    "   select count(id), service_id \n" +
//                    "   from (\n" +
//                    "       SELECT max(pricing.id) as id, pricing.service_id \n" +
//                    "       FROM {h-schema}pricing\n" +
//                    "       WHERE status = 1 and approve = 1 and deleted_flag = 1\n" +
//                    "       GROUP BY pricing.pricing_draft_id, pricing.service_id\n" +
//                    "   ) as cPricing  \n" +
//                    "   GROUP BY cPricing.service_id\n" +
//                    "   ) as p on p.service_id = s.id\n" +
//                    "where (s.product_type = 1 or ((s.product_type <> 1 or s.product_type is null) and p.count <> 0))" +
//                    "ORDER BY s.id DESC \n" +
//                    ")\n" +
//                    "UNION ALL\n" +
//                    "(SELECT DISTINCT ON (co.id)\n" +
//                    "   1 AS type,\n" +
//                    "   co.id AS ID,\n" +
//                    "  (concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
//                    "  CAST ( UNNEST ( string_to_array( co.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
//                    "  co.deleted_flag AS deleted_flag, \n" +
//                    "  co.approve AS approve, \n" +
//                    "  co.status AS status, \n" +
//                    "  co.user_id AS provider, \n" +
//                    "  co.customer_type_code AS customer_type_code," +
//                    "  co.combo_name AS Name\n" +
//                    "FROM {h-schema}combo AS co\n" +
//                    "JOIN {h-schema}users AS provider ON provider.id = co.user_id \n" +
//                    "LEFT JOIN ( \n" +
//                    "         select count(id), combo_id  \n" +
//                    "         from ( \n" +
//                    "            SELECT max(combo_plan.id) as id, combo_plan.combo_id\n" +
//                    "            FROM {h-schema}combo_plan\n" +
//                    "            WHERE combo_plan.deleted_flag = 1 AND combo_plan.status = 1 and combo_plan.approve = 1\n" +
//                    "            GROUP BY combo_plan.combo_plan_draft_id, combo_plan.combo_id\n" +
//                    "         ) as cPricing   \n" +
//                    "         GROUP BY cPricing.combo_id\n" +
//                    "      ) cpCount on cpCount.combo_id = co.id \n" +
//                    "Where cpCount.count <> 0\n" +
//                    "ORDER BY co.id DESC \n" +
//                    ")) AS lastest_service_combo\n" +
//                    "WHERE ((lastest_service_combo.deleted_flag = 1 AND lastest_service_combo.approve = 1 AND lastest_service_combo.status = 1) AND (lastest_service_combo.Name ILIKE '%' || :filter || '%')\n" +
//                    "      AND (-1 IN (:categoryList) OR lastest_service_combo.categories_id IN (:categoryList)) \n" +
//                    "      AND (:userId = -1 OR :userId = lastest_service_combo.provider) \n" +
//                    "      AND (:customerTypeStr = 'ALL' OR lastest_service_combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%'))\n";

    public static final String GET_ALL_SERVICE_COMBO =
            "select s.service_name as name, s.id, s.provider AS userId, s.type, s.service_unique_id as UniqueId from (\n" +
                    "   select * from {h-schema}combobox_feature_view_shopping_cart_get_spdv_info_new \n" +
                    "   where (service_name ILIKE '%' || :filter || '%')\n" +
                    "       AND (-1 IN (:categoryList) OR categories_id IN (:categoryList)) \n" +
                    "       AND (:userId = -1 OR :userId = provider) \n" +
                    "       AND (:customerTypeStr = '' or (string_to_array(:customerTypeStr, ',') && lst_customer_type)) \n" +
                    "       AND (:customerTypeStr = '' or (string_to_array(:customerTypeStr, ',') && pricing_lst_customer_type)) \n" +
                    "       AND (:customerTypeStr = '' or (string_to_array(:customerTypeStr, ',') && pricing_multi_plan_lst_customer_type)) \n" +
                    ") as s \n" +
                    "GROUP BY s.id, s.service_name, s.provider, s.id, s.type, s.service_unique_id order by s.service_name";

    public static final String GET_ALL_LASTEST_SERVICE_COMBO_INFO_SELECTED =
            " SELECT * FROM \n" +
            "((SELECT DISTINCT ON ((COALESCE(s.services_draft_id, s.id)))\n " +
            "    0 AS type,\n" +
            "    s.id AS ID,\n" +
            "    (concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
            "    s.service_name AS Name\n" +
            "FROM {h-schema}services AS s\n" +
            "ORDER BY COALESCE(s.services_draft_id, s.id) DESC, s.id DESC \n" +
            ")\n" +
            "UNION ALL\n" +
            "(SELECT DISTINCT ON ((COALESCE(co.combo_draft_id, co.id)))\n" +
            "   1 AS type,\n" +
            "   co.id AS ID,\n" +
            "  (concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
            "  co.combo_name AS Name\n" +
            "FROM {h-schema}combo AS co\n" +
            "ORDER BY COALESCE(co.combo_draft_id, co.id) DESC, co.id DESC \n" +
            ")) AS lastest_service_combo \n" +
            "WHERE lastest_service_combo.uniqueId in (:lstSelectedId)";

    public static final String GET_ALL_PRICING_COMBO_PLAN_INFO =
            "(\n"+
            "SELECT DISTINCT ON  ( P.pricing_draft_id ) \n" +
                    "    0 :::: INT2 AS TYPE,\n" +
                    "    P.ID AS ID,\n" +
                    "    concat ( P.ID, '0000' )::::BIGINT AS UniqueId,\n" +
                    "    P.pricing_name AS NAME\n" +
                    "FROM\n" +
                    "    {h-schema}pricing P \n" +
                    "WHERE\n" +
                    "    ( P.deleted_flag = 1 AND P.approve = 1) AND ( P.pricing_name ILIKE'%' || :filter || '%' ) AND (-1 IN (:serviceIdList) OR concat (P.service_id, '0000' )::::BIGINT IN (:serviceIdList))\n" +
                    "    AND (:customerTypeStr = 'ALL' OR P.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "ORDER BY\n" +
                    "    P.pricing_draft_id, P.approve_time DESC, P.id DESC\n" +
                    ")\n"+
                    "UNION ALL\n" +
                    "(\n"+
                    "SELECT DISTINCT ON( cp.combo_draft_id ) \n" +
                    "    1 :::: INT2 AS TYPE,\n" +
                    "    cp.ID AS ID,\n" +
                    "    concat ( cp.ID, '0001' ) :::: BIGINT AS UniqueId,\n" +
                    "    cp.combo_name AS NAME \n" +
                    "FROM\n" +
                    "    {h-schema}combo_plan cp \n" +
                    "WHERE\n" +
                    "    ( cp.deleted_flag = 1 AND cp.approve = 1) AND ( cp.combo_name ILIKE'%' || :filter || '%' ) AND (-1 IN (:serviceIdList) OR concat (cp.combo_id, '0001' )::::BIGINT IN (:serviceIdList))\n" +
                    "    AND (:customerTypeStr = 'ALL' OR cp.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "ORDER BY\n" +
                    "    cp.combo_draft_id, cp.approve_time DESC, cp.id DESC\n"+
                    ")";
    public static final String GET_ALL_LASTEST_PRICING_COMBO_PLAN_INFO =
        "SELECT \n" +
            "last_pricing_combo.TYPE, \n" +
            "last_pricing_combo.ID as ID, \n" +
            "last_pricing_combo.UniqueId AS UniqueId, \n" +
            "last_pricing_combo.NAME AS NAME \n" +
        "FROM \n" +
        "(\n"+
            "(SELECT DISTINCT ON  ( P.pricing_draft_id ) \n" +
            "    0 :::: INT2 AS TYPE,\n" +
            "    P.ID AS ID,\n" +
            "    concat ( P.ID, '0000' )::::BIGINT AS UniqueId,\n" +
            "    S.user_id AS provider, \n" +
            "    P.status, \n" +
            "    P.deleted_flag, \n" +
            "    P.approve, \n" +
            "    p.customer_type_code, \n" +
            "    P.pricing_name AS NAME\n" +
            "FROM\n" +
            "    {h-schema}pricing P \n" +
            "LEFT JOIN\n" +
            "    {h-schema}services S ON S.id = P.service_id \n" +
            "JOIN {h-schema}users AS provider ON provider.id = S.user_id \n" +
            "WHERE\n" +
            "    (s.product_type is null or s.product_type <> 1) AND (S.deleted_flag = 1 AND S.approve = 1 AND S.status = 1) \n" +
            "ORDER BY\n" +
            "    P.pricing_draft_id, P.approve_time DESC, P.id DESC\n" +
            ")\n"+
            "UNION ALL\n" +
            "(\n"+
            "SELECT DISTINCT ON( cp.combo_draft_id ) \n" +
            "    1 :::: INT2 AS TYPE,\n" +
            "    cp.ID AS ID,\n" +
            "    concat ( cp.ID, '0001' ) :::: BIGINT AS UniqueId,\n" +
            "    c.user_id AS provider, \n" +
            "    cp.status, \n" +
            "    cp.deleted_flag, \n" +
            "    cp.approve, \n" +
            "    cp.customer_type_code, \n" +
            "    cp.combo_name AS NAME \n" +
            "FROM\n" +
            "    {h-schema}combo_plan cp \n" +
            "LEFT JOIN\n" +
            "    {h-schema}combo c ON c.id = cp.combo_id \n" +
            "JOIN {h-schema}users AS provider ON provider.id = c.user_id \n" +
            "WHERE\n" +
            "    (c.deleted_flag = 1 AND c.approve = 1 AND c.status = 1) \n" +
            "ORDER BY\n" +
            "    cp.combo_draft_id, cp.approve_time DESC, cp.id DESC\n"+
            ") \n" +
        ") AS last_pricing_combo \n" +
        "WHERE \n" +
        "    ( last_pricing_combo.deleted_flag = 1 AND last_pricing_combo.approve = 1 AND last_pricing_combo.status = 1) AND \n" +
        "    ( last_pricing_combo.NAME ILIKE'%' || :filter || '%' ) AND " +
        "    (-1 IN (:serviceIdList) OR concat (last_pricing_combo.ID, '0001' )::::BIGINT IN (:serviceIdList)) AND\n" +
        "    (:customerTypeStr = 'ALL' OR last_pricing_combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') AND\n" +
        "    (:userId = -1 OR :userId = last_pricing_combo.provider)   ";

    public static final String GET_ALL_PRICING_COMBO_PLAN =
            "SELECT \n" +
                    "last_pricing_combo.TYPE, \n" +
                    "last_pricing_combo.ID as ID, \n" +
                    "last_pricing_combo.UniqueId AS UniqueId, \n" +
                    "last_pricing_combo.NAME AS NAME \n" +
                    "FROM \n" +
                    "(\n"+
                    "(SELECT DISTINCT ON  ( P.pricing_draft_id ) \n" +
                    "    0 :::: INT2 AS TYPE,\n" +
                    "    P.ID AS ID,\n" +
                    "    concat ( P.ID, '0000' )::::BIGINT AS UniqueId,\n" +
                    "    S.user_id AS provider, \n" +
                    "    P.status, \n" +
                    "    P.deleted_flag, \n" +
                    "    P.approve, \n" +
                    "    p.customer_type_code, \n" +
                    "    P.pricing_name AS NAME\n" +
                    "FROM\n" +
                    "    {h-schema}pricing P \n" +
                    "LEFT JOIN\n" +
                    "    {h-schema}services S ON S.id = P.service_id \n" +
                    "JOIN {h-schema}users AS provider ON provider.id = S.user_id \n" +
                    "WHERE\n" +
                    "    (S.deleted_flag = 1 AND S.approve = 1 AND S.status = 1) \n" +
                    "ORDER BY\n" +
                    "    P.pricing_draft_id, P.approve_time DESC, P.id DESC\n" +
                    ")\n"+
                    "UNION ALL\n" +
                    "(\n"+
                    "SELECT DISTINCT ON( cp.combo_draft_id ) \n" +
                    "    1 :::: INT2 AS TYPE,\n" +
                    "    cp.ID AS ID,\n" +
                    "    concat ( cp.ID, '0001' ) :::: BIGINT AS UniqueId,\n" +
                    "    c.user_id AS provider, \n" +
                    "    cp.status, \n" +
                    "    cp.deleted_flag, \n" +
                    "    cp.approve, \n" +
                    "    cp.customer_type_code, \n" +
                    "    cp.combo_name AS NAME \n" +
                    "FROM\n" +
                    "    {h-schema}combo_plan cp \n" +
                    "LEFT JOIN\n" +
                    "    {h-schema}combo c ON c.id = cp.combo_id \n" +
                    "JOIN {h-schema}users AS provider ON provider.id = c.user_id \n" +
                    "WHERE\n" +
                    "    (c.deleted_flag = 1 AND c.approve = 1 AND c.status = 1) \n" +
                    "ORDER BY\n" +
                    "    cp.combo_draft_id, cp.approve_time DESC, cp.id DESC\n"+
                    ") \n" +
                    ") AS last_pricing_combo \n" +
                    "WHERE \n" +
                    "    ( last_pricing_combo.deleted_flag = 1 AND last_pricing_combo.approve = 1 AND last_pricing_combo.status = 1) AND \n" +
                    "    ( last_pricing_combo.NAME ILIKE'%' || :filter || '%' ) AND " +
                    "    (-1 IN (:serviceIdList) OR concat (last_pricing_combo.ID, '0001' )::::BIGINT IN (:serviceIdList)) AND\n" +
                    "    (:customerTypeStr = 'ALL' OR last_pricing_combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') AND\n" +
                    "    (:userId = -1 OR :userId = last_pricing_combo.provider)   ";

    public static final String GET_ALL_PRICING_COMBO_PLAN_INFO_SELECTED =
            "(\n"+
                    "SELECT DISTINCT ON  ( P.pricing_draft_id ) \n" +
                    "    0 :::: INT2 AS TYPE,\n" +
                    "    P.ID AS ID,\n" +
                    "    concat ( P.ID, '0000' )::::BIGINT AS UniqueId,\n" +
                    "    P.pricing_name AS NAME\n" +
                    "FROM\n" +
                    "    {h-schema}pricing P \n" +
                    "WHERE\n" +
                    "    ( P.deleted_flag = 1 AND P.approve = 1) AND (concat (P.id, '0000' )::::BIGINT IN (:lstSelectedId))\n" +
                    "ORDER BY\n" +
                    "    P.pricing_draft_id, P.approve_time DESC, P.id DESC\n" +
                    ")\n"+
                    "UNION ALL\n" +
                    "(\n"+
                    "SELECT DISTINCT ON( cp.combo_draft_id ) \n" +
                    "    1 :::: INT2 AS TYPE,\n" +
                    "    cp.ID AS ID,\n" +
                    "    concat ( cp.ID, '0001' ) :::: BIGINT AS UniqueId,\n" +
                    "    cp.combo_name AS NAME \n" +
                    "FROM\n" +
                    "    {h-schema}combo_plan cp \n" +
                    "WHERE\n" +
                    "    ( cp.deleted_flag = 1 AND cp.approve = 1) AND (concat (cp.id, '0001' )::::BIGINT IN (:lstSelectedId))\n" +
                    "ORDER BY\n" +
                    "    cp.combo_draft_id, cp.approve_time DESC, cp.id DESC\n"+
                    ")";

    public static final String GET_CONTACT_NAME =
        "( \n" +
            "    SELECT\n" +
            "        0 AS Type,\n" +
            "        en.id AS Id,\n" +
            "        (concat(en.id, '0000'))::::bigint AS UniqueId,\n" +
            "        en.contact_name AS Name\n" +
            "    FROM {h-schema}enterprise AS en\n" +
            "    WHERE (en.archive_status = 0) AND (en.contact_name ILIKE '%' || :filter || '%')\n" +
            "          AND ('ALL' in (:lstCustomerType) OR en.customer_type in (:lstCustomerType))\n" +
            "    ORDER BY en.id\n" +
            ")\n" +
            "UNION ALL\n" +
            "( \n" +
            "    SELECT\n" +
            "        1 AS Type,\n" +
            "        cc.id AS Id,\n" +
            "        (concat(cc.id, '0001'))::::bigint AS UniqueId,\n" +
            "        cc.contact_name AS Name\n" +
            "    FROM {h-schema}customer_contact AS cc\n" +
            "    WHERE (cc.archive_status = 0) AND (cc.contact_name ILIKE '%' || :filter || '%')\n" +
            "    ORDER BY cc.id\n" +
            ")";

    public static final String GET_CONTACT_EMAIL =
        "SELECT\n" +
            "        ROW_NUMBER() OVER(ORDER BY total_email.Email) AS Id,\n" +
            "        total_email.Email\n" +
            "FROM (\n" +
            "    ( \n" +
            "        SELECT\n" +
            "            DISTINCT unnest((string_to_array( btrim( replace(en.contact_emails, '\"', '') , '[]' :::: TEXT ), ',':::: TEXT))) AS Email\n" +
            "        FROM {h-schema}enterprise AS en\n" +
            "        WHERE (en.archive_status = 0) AND (en.contact_emails ILIKE '%' || :filter || '%')\n" +
            "              AND ('ALL' in (:lstCustomerType) OR en.customer_type in (:lstCustomerType))\n" +
            "    )\n" +
            "    UNION\n" +
            "    ( \n" +
            "        SELECT\n" +
            "            DISTINCT unnest((string_to_array( btrim( replace(cc.contact_emails, '\"', '') , '[]' :::: TEXT ), ',':::: TEXT))) AS Email\n" +
            "        FROM {h-schema}customer_contact AS cc\n" +
            "        WHERE (cc.archive_status = 0) AND (cc.contact_emails ILIKE '%' || :filter || '%')\n" +
            "    )\n" +
            ") AS total_email";

    public static final String GET_CONTACT_PHONE =
        "( \n" +
            "    SELECT\n" +
            "        0 AS Type,\n" +
            "        en.id AS Id,\n" +
            "        (concat(en.id, '0000'))::::bigint AS UniqueId,\n" +
            "        en.contact_phones AS Phone\n" +
            "    FROM {h-schema}enterprise AS en\n" +
            "    WHERE (en.archive_status = 0) AND (en.contact_phones ILIKE '%' || :filter || '%')\n" +
            "    ORDER BY en.id\n" +
            ")\n" +
            "UNION ALL\n" +
            "( \n" +
            "    SELECT\n" +
            "        1 AS Type,\n" +
            "        cc.id AS Id,\n" +
            "        (concat(cc.id, '0001'))::::bigint AS UniqueId,\n" +
            "        cc.contact_phones AS Phone\n" +
            "    FROM {h-schema}customer_contact AS cc\n" +
            "    WHERE (cc.archive_status = 0) AND (cc.contact_phones ILIKE '%' || :filter || '%')\n" +
            "    ORDER BY cc.id\n" +
            ")";

    public static final String GET_MARKETING_CAMPAIGN_NAME =
        "select \n " +
            "    mCampaign.id as id,\n" +
            "    mCampaign.name as name \n" +
            "from {h-schema}marketing_campaign as mCampaign \n" +
            "where (mCampaign.name ilike '%' || :filter || '%') and \n" +
            "       mCampaign.deleted_flag = 1 and \n"+
            "       ((-2 = :status) or (-1 = :status and mCampaign.status in (0,1,2,3)) or ( mCampaign.status = :status)) and \n"+
            "       (-1 = :adminProvinceId or mCampaign.province_id = :adminProvinceId)";


    public static final String GET_LIST_ADMIN_NAME =
        "select \n" +
            "    DISTINCT mUsers.email as name \n" +
            "from {h-schema}users as mUsers \n" +
            "    left join {h-schema}users_roles as mUsersRole on mUsers.id = mUsersRole.user_id \n" +
            "    left join {h-schema}role as mRole on mUsersRole.role_id = mRole.id \n" +
            "where mRole.name ilike '%admin%' \n" +
            "    and mUsers.email is not null \n" +
            "    and mUsers.email ilike '%' || :filter || '%'" +
            "    and (-1 = :adminProvinceId or mUsers.province_id = :adminProvinceId)";

    public static final String SQL_BODY_GET_LIST_ENTERPRISE =
        "from \n" +
            "    {h-schema}enterprise as en \n" +
            "where \n" +
            "    (-1 = :adminProvinceId or en.province_id = :adminProvinceId) and \n" +
            "    (-1 in (:lstEnterpriseId) or en.id in (:lstEnterpriseId)) and \n" +
            "    (-1 in (:lstRegionId) or en.region_id in (:lstRegionId)) and \n" +
            "    (-1 in (:lstProvinceId) or en.province_id in (:lstProvinceId)) and \n" +
            "    (-1 in (:lstDistrictId) or en.district_id in (:lstDistrictId)) and \n" +
            "    (-1 in (:lstWardId) or en.ward_id in (:lstWardId)) and \n" +
            "    (-1 in (:lstBusinessAreaId) or en.business_area_id in (:lstBusinessAreaId)) and \n" +
            "    (-1 in (:lstBusinessSizeId) or en.business_size_id in (:lstBusinessSizeId)) and \n" +
            "    (-1 in (:lstCreatedSource) or en.created_source in (:lstCreatedSource)) and \n" +
            "    (-1 = :status or en.status = :status) and \n" +
            "    ('' = :name or lower(en.name) like lower('%' || :name || '%')) and \n" +
            "    ('' = :taxCode or lower(en.tin) like lower('%' || :taxCode || '%')) and \n" +
            "    ('' = :email or lower(en.email) like lower('%' || :email || '%')) and \n" +
            "    ('' = :phone or lower(en.phone) like lower('%' || :phone || '%')) and \n" +
            "    ('' = :repName or lower(en.rep_name) like lower('%' || :repName || '%')) and \n" +
            "    ('ALL' in (:lstCustomerType) OR en.customer_type in (:lstCustomerType))\n and " +
            "    (cast('1970-01-01' as date) = :foundingDateBegin or en.founding_date >= :foundingDateBegin) and \n" +
            "    (cast('1970-01-01' as date) = :foundingDateEnd or en.founding_date <= :foundingDateEnd) and \n" +
            "    (cast('1970-01-01' as date) = :createdAtBegin or cast(en.created_at as date) >= :createdAtBegin) and \n" +
            "    (cast('1970-01-01' as date) = :createdAtEnd or cast(en.created_at as date) <= :createdAtEnd) and \n" +
            "    en.archive_status = 0 and\n" +
            "    (\n" +
            "      (1 = :type and en.email is not null) or\n" +
            "      (2 = :type and en.phone is not null)\n" +
            "    ) and\n" +
            "    ('ALL' in (:lstCustomerType) or coalesce(en.customer_type, 'KHDN') in (:lstCustomerType)) ";

    public static final String GET_LIST_ENTERPRISE =
        "select \n" +
            "    en.id as id, \n" +
            "    en.name as name, \n" +
            "    en.email as email, \n" +
            "    en.phone as phone, \n" +
            "    case \n" +
            "       when en.customer_type = 'KHDN' then 'Doanh Nghiệp'" +
            "       when en.customer_type = 'CN' then 'Cá nhân'" +
            "       else 'Hộ kinh doanh'" +
            "    end as customerType \n" +
            SQL_BODY_GET_LIST_ENTERPRISE;

    public static final String GET_LIST_ENTERPRISE_ALL =
        "select \n" +
            "    en.id \n" +
            SQL_BODY_GET_LIST_ENTERPRISE;

    public static final String COUNT_GET_LIST_ENTERPRISE =
        "select \n" +
            "    count(*) \n" +
            SQL_BODY_GET_LIST_ENTERPRISE;

    public static final String GET_LIST_USER_TIN =
        "SELECT\n"
            + "    u.id AS Id,\n"
            + "    u.tin as Value\n"
            + "FROM {h-schema}users AS u \n"
            + "WHERE \n"
            + "    u.tin IS NOT NULL AND \n"
            + "    u.tin ILIKE '%' || :filter || '%' AND\n"
            + "    ('ALL' in (:lstCustomerType) or u.customer_type in (:lstCustomerType))";

    public static final String GET_LIST_USER_REP_NAME =
        "SELECT\n" +
            "    u.id AS Id,\n" +
            "    u.rep_fullname as Value\n" +
            "FROM {h-schema}users AS u \n" +
            "WHERE " +
            "    u.rep_fullname IS NOT NULL AND " +
            "    u.rep_fullname ILIKE '%' || :filter || '%' AND " +
            "    ('ALL' in (:lstCustomerType) or u.customer_type in (:lstCustomerType))";

    public static final String GET_LIST_SUB_CODE =
        "SELECT\n" +
            "    sub.id AS Id,\n" +
            "    sub.sub_code AS Value\n" +
            "FROM {h-schema}subscriptions AS sub\n" +
            "LEFT JOIN {h-schema}users u ON u.id = sub.user_id \n" +
            "WHERE sub.sub_code IS NOT NULL AND sub.sub_code ILIKE '%' || :filter || '%' \n" +
            "      AND (:customerTypeStr = 'ALL' OR u.customer_type SIMILAR TO '%('|| :customerTypeStr ||')%') \n" ;

    public static final String GET_LIST_SERVICE_CODE =
        "SELECT \n" +
            "    concat(s.id, '0000')::::bigint AS Id,\n" +
            "    s.service_code AS Value\n" +
            "FROM {h-schema}services AS s\n" +
            "WHERE s.service_code IS NOT NULL AND s.service_code ILIKE '%' || :filter || '%' \n" +
            "      AND (:customerTypeStr = 'ALL' OR s.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                "UNION ALL\n" +
            "SELECT \n" +
            "    concat(c.id, '0001')::::bigint AS Id,\n" +
            "    c.combo_code AS Value\n" +
            "FROM {h-schema}combo AS c\n" +
            "WHERE c.combo_code IS NOT NULL AND c.combo_code ILIKE '%' || :filter || '%' \n" +
            "      AND (:customerTypeStr = 'ALL' OR c.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%')";

    public static final String GET_LIST_DEVELOPER_NAME =
        "SELECT DISTINCT\n" +
            "    u.id AS Id,\n" +
            "    u.name AS Value\n" +
            "FROM {h-schema}users AS u\n" +
            "LEFT JOIN {h-schema}users_roles AS ur ON ur.user_id = u.id\n" +
            "WHERE ur.role_id IN (4, 7, 8) AND u.name IS NOT NULL AND u.name ILIKE '%' || :filter || '%'\n" +
            "      AND ('ALL' in (:lstCustomerType) OR u.customer_type in (:lstCustomerType))";

    public static final String GET_PAYMENT_CYCLE_INFO = "SELECT \n" +
            "    ROW_NUMBER() OVER(ORDER BY payment_cycle_info.unit, payment_cycle_info.value) AS id,\n" +
            "    payment_cycle_info.unit,\n" +
            "    payment_cycle_info.value,\n" +
            "    payment_cycle_info.paymentCycle\n" +
            "FROM (\n" +
            "    SELECT DISTINCT\n" +
            "        pricing_combo_plan.cycleType AS unit,\n" +
            "        pricing_combo_plan.paymentCycle AS value,\n" +
            "        CASE\n" +
            "            WHEN pricing_combo_plan.cycleType = 0 THEN concat(pricing_combo_plan.paymentCycle, ' ngày')\n" +
            "            WHEN pricing_combo_plan.cycleType = 1 THEN concat(pricing_combo_plan.paymentCycle, ' tuần')\n" +
            "            WHEN pricing_combo_plan.cycleType = 2 THEN concat(pricing_combo_plan.paymentCycle, ' tháng')\n" +
            "            WHEN pricing_combo_plan.cycleType = 3 THEN concat(pricing_combo_plan.paymentCycle, ' năm')\n" +
            "        END AS paymentCycle\n" +
            "    FROM ( \n" +
            "        (\n" +
            "            SELECT \n" +
            "                    CASE\n" +
            "                            WHEN pmp.id IS NOT NULL THEN CONCAT(pmp.id, '0002')::::BIGINT\n" +
            "                            ELSE CONCAT(latest_pricing.id, '0000')::::BIGINT\n" +
            "                    END AS uniqueId,\n" +
            "                    CASE\n" +
            "                            WHEN pmp.id IS NOT NULL THEN pmp.payment_cycle\n" +
            "                            ELSE latest_pricing.payment_cycle\n" +
            "                    END paymentCycle,\n" +
            "                    CASE \n" +
            "                            WHEN pmp.id IS NOT NULL THEN pmp.circle_type\n" +
            "                            ELSE latest_pricing.cycle_type\n" +
            "                    END cycleType\n" +
            "            FROM (\n" +
            "                    SELECT DISTINCT ON (p.pricing_draft_id)\n" +
            "                            p.id,\n" +
            "                            p.payment_cycle,\n" +
            "                            p.cycle_type\n" +
            "                    FROM {h-schema}pricing p\n" +
            "                    WHERE p.deleted_flag = 1 AND p.approve = 1 AND (-1 IN (:pricingIdList) OR CONCAT(p.id, '0000')::::BIGINT IN (:pricingIdList))\n" +
            "                          AND (:customerTypeStr = 'ALL' OR p.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
            "                    ORDER BY p.pricing_draft_id, p.approve_time DESC\n" +
            "            ) AS latest_pricing\n" +
            "            LEFT JOIN {h-schema}pricing_multi_plan pmp ON pmp.pricing_id = latest_pricing.id\n" +
            "            )\n" +
            "            UNION ALL\n" +
            "            (\n" +
            "            SELECT DISTINCT ON (cp.combo_draft_id)\n" +
            "                    CONCAT(cp.id, '0001')::::BIGINT AS uniqueId,\n" +
            "                    cp.payment_cycle AS paymentCycle,\n" +
            "                    cp.cycle_type AS cycleType\n" +
            "            FROM {h-schema}combo_plan cp\n" +
            "            WHERE cp.deleted_flag = 1 AND cp.approve = 1 AND (-1 IN (:pricingIdList) OR CONCAT(cp.id, '0001')::::BIGINT IN (:pricingIdList))\n" +
            "                  AND (:customerTypeStr = 'ALL' OR cp.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
            "            ORDER BY cp.combo_draft_id, cp.approve_time DESC\n" +
            "            )\n" +
            "    ) AS pricing_combo_plan\n" +
            ") AS payment_cycle_info";

    public static final String UPDATE_DELETE_FLAG_KAFKA_BATCH =
        "WITH cte AS (SELECT bk.id "
            + "FROM {h-schema}batch_kafka bk "
            + "WHERE bk.subscription_id = :subId "
            + "   AND bk.action_type = 2 "
            + "   AND bk.deleted_flag = 0 "
            + "ORDER BY bk.id DESC "
            + "LIMIT 1) "
            + "UPDATE {h-schema}batch_kafka  "
            + "SET deleted_flag = 1 "
            + "WHERE id = (SELECT id FROM cte)";

    public static final String GET_COUPON_MC =
            "(SELECT\n" +
                    "    concat ( CP.ID, '0000' )::::BIGINT AS UniqueId,\n" +
                    "    CP.name AS name \n" +
                    "FROM {h-schema}coupons CP\n" +
                    "WHERE \n" +
                    "    ( CP.deleted_flag = 1 ) \n" +
                    "    AND( CP.name ILIKE'%' || :name || '%' ) \n" +
                    "    AND (:customerTypeStr = 'ALL' OR CP.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "    AND (-1 = :adminProvinceId or CP.province_id = :adminProvinceId) \n" +
                    ")\n" +
                    "UNION ALL\n" +
                    "(SELECT\n" +
                    "    concat ( MC.ID, '0001' )::::BIGINT AS UniqueId,\n" +
                    "    MC.name AS name \n" +
                    "FROM {h-schema}marketing_campaign MC\n" +
                    "LEFT JOIN {h-schema}mc_user_statistic MUS on MC.id = MUS.mc_id \n" +
                    "LEFT JOIN {h-schema}users U on U.id = MUS.user_id\n" +
                    "WHERE \n" +
                    "    ( MC.deleted_flag = 1 ) " +
                    "    AND ( MC.name ILIKE'%' || :name || '%' ) \n" +
                    "    AND (:customerTypeStr = 'ALL' OR U.customer_type SIMILAR TO '%('|| :customerTypeStr ||')%') \n" +
                    "    AND (-1 = :adminProvinceId or MC.province_id = :adminProvinceId)\n" +
                    ")";
    public static final String GET_COUPON_MC_SELECTED =
            "SELECT DISTINCT\n" +
            "       coupon_mc.UniqueId,\n" +
            "       coupon_mc.name\n" +
            "FROM\n" +
            "       (\n" +
            "           (\n" +
            "               SELECT\n" +
            "                   concat ( CP.ID, '0000' )::::BIGINT AS UniqueId,\n" +
            "                   CP.name AS name \n" +
            "               FROM {h-schema}coupons CP\n" +
            "               WHERE \n" +
            "                   ( CP.deleted_flag = 1 ) \n" +
            "                   AND (-1 = :adminProvinceId or CP.province_id = :adminProvinceId) \n" +
            "           )\n" +
            "           UNION ALL\n" +
            "           (\n" +
            "               SELECT\n" +
            "                   concat ( MC.ID, '0001' )::::BIGINT AS UniqueId,\n" +
            "                   MC.name AS name \n" +
            "               FROM {h-schema}marketing_campaign MC\n" +
            "               WHERE \n" +
            "                   ( MC.deleted_flag = 1 ) " +
            "                   AND (-1 = :adminProvinceId or MC.province_id = :adminProvinceId)\n" +
            "           )\n" +
            "       ) as coupon_mc\n" +
            "WHERE \n" +
            "       coupon_mc.UniqueId in (:lstSelected)\n";

    public static final String GET_CUSTOMER_TICKET_NAME =
        "SELECT \n" +
            "   id, \n" +
            "   name AS value \n" +
            "FROM \n" +
            "   {h-schema}customer_ticket \n" +
            "WHERE \n" +
            "   name ILIKE ('%' || :ticketName || '%')";

    public static final String GET_CUSTOMER_TICKET_DESCRIPTION =
        "SELECT \n" +
            "   id, \n" +
            "   description AS value \n" +
            "FROM \n" +
            "   {h-schema}customer_ticket \n" +
            "WHERE \n" +
            "   description ILIKE ('%' || :description || '%')";

    public static final String GET_BILLINGS_CUSTOMER_ADDRESS =
        "SELECT \n" +
            "   id, \n" +
            "   customer_address AS value \n" +
            "FROM \n" +
            "   {h-schema}billings \n" +
            "WHERE \n" +
            "   customer_address ILIKE ('%' || :cusAddress || '%')";

    public static final String GET_LIST_DATA_PARTITION_NAME =
        "SELECT \n" +
            "   id, \n" +
            "   name, \n" +
            "   code \n" +
            "FROM \n" +
            "   {h-schema}crm_data_partition \n" +
            "WHERE \n" +
            "   (:name = '' OR name ILIKE ('%' || :name || '%')) AND \n" +
            "   (:id = -1 OR id = :id) AND \n" +
            "   (:code = '' OR code ILIKE ('%' || :code || '%')) AND \n" +
            "   (:status = -1 OR status = :status)";

    public static final String GET_LIST_DATA_PARTITION_ADMIN =
            "SELECT \n" +
                    "	u.id as adminId,\n" +
                    "	concat_ws(' ', u.last_name, u.first_name) as adminName,\n" +
                    "	u.user_code as adminCode,\n" +
                    "	u.email as adminEmail,\n" +
                    "	u.phone_number as adminPhone\n" +
                    "FROM {h-schema}crm_data_partition crm\n" +
                    "LEFT JOIN {h-schema}users u ON  u.id = any(crm.lst_admin_id)\n" +
                    "WHERE u.id is not null AND\n" +
                    "   (:adminName = '' OR concat_ws(' ', u.last_name, u.first_name) ILIKE ('%' || :adminName || '%')) AND \n" +
                    "   (:adminCode = '' OR u.user_code ILIKE ('%' || :adminCode || '%')) AND \n" +
                    "   (:adminPhone = '' OR u.phone_number ILIKE ('%' || :adminPhone || '%')) AND \n" +
                    "   (:adminEmail = '' OR u.email ILIKE ('%' || :adminEmail || '%')) \n" +
                    "group by u.id ";

    public static final String GET_LIST_SUPPORTER_INFO =
        "SELECT DISTINCT \n"
            + "    users.id AS id, \n"
            + "    CASE \n"
            + "        WHEN affiliate_users.id is null and (users.last_name is not null or users.first_name is not null) THEN CONCAT(users.last_name,' ',users.first_name) \n"
            + "        ELSE users.name \n"
            + "    END AS name, \n"
            + "    ur.numberRole, affiliate_users.id as affId, \n"
            + "    users.phone_number AS phoneNumber, \n"
            + "    users.email AS email, \n"
            + "    CASE \n"
            + "        WHEN users_roles.role_id IN (:roleAdminIds) THEN 'ADMIN' \n"
            + "        WHEN users_roles.role_id IN (:roleDevIds) THEN 'DEV' \n"
            + "        ELSE 'AFFILIATE'   "
            + "    END AS roleName\n"
            + " FROM \n"
            + "    {h-schema}users \n"
            + "    LEFT JOIN ( SELECT  \n"
            + "           ur.user_id as user_id,  \n"
            + "           count(1) numberRole  \n"
            + "    FROM {h-schema}users_roles ur  \n"
            + "       GROUP BY ur.user_id ) ur ON ur.user_id = users.id  "
            + "    LEFT JOIN {h-schema}users_roles ON users_roles.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}affiliate_users ON affiliate_users.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}role ON role.id = users_roles.role_id \n"
            + "    LEFT JOIN {h-schema}report_view_service_combo_uniqueid as view_unique_id ON users.id = view_unique_id.user_id \n"
            + "    LEFT JOIN {h-schema}crm_data_partition ON (users.id = ANY(array_cat(crm_data_partition.lst_admin_id, crm_data_partition.lst_am_id))) \n"
            + "WHERE \n"
            + "    users.deleted_flag = 1 AND users.status = 1 AND users_roles.role_id IN (:roleIds) AND users.id NOT IN (:lstSelectedId) AND \n"
            + "    ('AFFILIATE' <> :type or (((affiliate_users.id is null and users_roles.role_id = 9) or (affiliate_users.id is not null and ur.numberRole > 2 and affiliate_users.affiliate_status = 2)))) AND \n"
            + "    role.deleted_flag = 1 AND role.status = 1 AND \n"
            + "    (( :nameAdmin = '' OR (affiliate_users.id is null and concat(users.last_name || ' ' || users.first_name) ILIKE ('%' || :nameAdmin || '%')))  \n"
            + "    OR ( :nameAdmin = '' OR (affiliate_users.id is not null and users.name ILIKE ('%' || :nameAdmin || '%')))) AND  \n"
            + "    (-1 IN (:lstUserId) OR users.id IN (:lstUserId)) AND \n"
            + "    (:partitionId = -1 OR crm_data_partition.id = :partitionId) AND \n"
            + "    (:serviceComboId = -1 OR view_unique_id.unique_id = :serviceComboId) AND \n"
            + "    (users.email IS NOT NULL AND users.phone_number IS NOT NULL) \n";

    public static final String GET_LIST_MANAGER_INFO =
        "WITH view_list_admin_info AS ( \n"
            + "SELECT DISTINCT \n"
            + "    users.id AS id, \n"
            + "    users.admin_code AS adminCode, \n"
            + "    users.email AS email, \n"
            + "    users.phone_number AS phoneNumber, \n"
            + "    STRING_AGG(DISTINCT CONCAT('[', crm_data_partition.code, '] ', crm_data_partition.name),', ') AS namePartition, \n"
            + "    CASE \n"
            + "        WHEN affiliate_users.id is null THEN CONCAT(users.last_name,' ',users.first_name) \n"
            + "        ELSE users.name \n"
            + "    END AS nameAdmin, \n"
            + "    ur.numberRole, affiliate_users.id as affId \n"
            + "FROM \n"
            + "    {h-schema}users \n"
            + "    LEFT JOIN ( SELECT  \n"
            + "           ur.user_id as user_id,  \n"
            + "           count(1) numberRole  \n"
            + "    FROM {h-schema}users_roles ur  \n"
            + "       GROUP BY ur.user_id ) ur ON ur.user_id = users.id  "
            + "    LEFT JOIN {h-schema}users_roles ON users_roles.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}affiliate_users ON affiliate_users.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}role ON role.id = users_roles.role_id \n"
            + "    LEFT JOIN {h-schema}province ON province.id = users.province_id \n"
            + "    LEFT JOIN {h-schema}crm_data_partition ON users.id = ANY(crm_data_partition.lst_admin_id) OR users.id = ANY(crm_data_partition.lst_am_id) \n"
            + "WHERE \n"
            + "    users.deleted_flag = 1 AND users.status = 1 AND users_roles.role_id IN (:roleAdminIds) AND \n"
            + "    (-1 IN (:lstUserId) OR users.id IN (:lstUserId)) AND\n"
            + "    (:partitionId = -1 OR crm_data_partition.id = :partitionId) AND \n"
            + "    ('AFFILIATE' <> :type or (affiliate_users.id is null or affiliate_users.affiliate_status = 2)) AND \n"
            + "    role.deleted_flag = 1 AND role.status = 1 AND \n"
            + "    (( :nameAdmin = '' OR (affiliate_users.id is null and concat(users.last_name || ' ' || users.first_name) ILIKE ('%' || :nameAdmin || '%')))  \n"
            + "    OR ( :nameAdmin = '' OR (affiliate_users.id is not null and users.name ILIKE ('%' || :nameAdmin || '%')))) AND  \n"
            + "    (users.last_name IS NOT NULL AND users.first_name IS NOT NULL AND users.email IS NOT NULL AND users.phone_number IS NOT NULL) \n"
            + "GROUP BY users.id, ur.numberRole, affiliate_users.id) \n"
            + "SELECT \n"
            + "    view_list_admin_info.* \n"
            + "FROM view_list_admin_info \n"
            + "WHERE view_list_admin_info.id IN( \n"
            + "     SELECT users.id"
            + "     FROM \n"
            + "         {h-schema}users \n"
            + "         LEFT JOIN {h-schema}crm_data_partition ON users.id = ANY(crm_data_partition.lst_admin_id) OR users.id = ANY(crm_data_partition.lst_am_id) \n"
            + "     WHERE \n"
            + "         users.id NOT IN (:lstSelectedId) AND\n"
            + "         (:nameOrCodePartition = '' OR crm_data_partition.code = :nameOrCodePartition))";

    public static final String GET_LIST_ADMIN_PHONE =
        "SELECT DISTINCT"
            + "    users.id AS id, \n"
            + "    users.phone_number AS value, \n"
            + "    ur.numberRole, affiliate_users.id as affId \n"
            + "FROM \n"
            + "    {h-schema}users \n"
            + "    LEFT JOIN ( SELECT  \n"
            + "           ur.user_id as user_id,  \n"
            + "           count(1) numberRole  \n"
            + "    FROM {h-schema}users_roles ur  \n"
            + "       GROUP BY ur.user_id ) ur ON ur.user_id = users.id  "
            + "    LEFT JOIN {h-schema}users_roles ON users_roles.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}affiliate_users ON affiliate_users.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}role ON role.id = users_roles.role_id \n"
            + "WHERE \n"
            + "    users.deleted_flag = 1 AND \n"
            + "    users.status = 1 AND \n"
            + "    users_roles.role_id IN (:roleAdminIds) AND \n"
            + "    ('AFFILIATE' <> :type or (affiliate_users.id is null or affiliate_users.affiliate_status = 2)) AND \n"
            + "    role.deleted_flag = 1 AND role.status = 1 AND \n"
            + "    (-1 IN (:lstUserId) OR users.id IN (:lstUserId)) AND "
            + "    (:phoneNumber = '' OR users.phone_number ILIKE ('%' || :phoneNumber || '%')) AND \n"
            + "    (users.last_name IS NOT NULL AND users.first_name IS NOT NULL AND users.email IS NOT NULL AND users.phone_number IS NOT NULL)";

    public static final String GET_LIST_ADMIN_EMAIL =
        "SELECT DISTINCT"
            + "    users.id AS id, \n"
            + "    users.email AS value, \n"
            + "    ur.numberRole, affiliate_users.id as affId \n"
            + "FROM \n"
            + "    {h-schema}users \n"
            + "    LEFT JOIN ( SELECT  \n"
            + "           ur.user_id as user_id,  \n"
            + "           count(1) numberRole  \n"
            + "    FROM {h-schema}users_roles ur  \n"
            + "       GROUP BY ur.user_id ) ur ON ur.user_id = users.id  "
            + "    LEFT JOIN {h-schema}users_roles ON users_roles.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}affiliate_users ON affiliate_users.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}role ON role.id = users_roles.role_id \n"
            + "WHERE \n"
            + "    users.deleted_flag = 1 AND users.status = 1 AND users_roles.role_id IN (:roleAdminIds) AND \n"
            + "    ('AFFILIATE' <> :type or (affiliate_users.id is null or affiliate_users.affiliate_status = 2)) AND \n"
            + "    role.deleted_flag = 1 AND role.status = 1 AND \n"
            + "    (-1 IN (:lstUserId) OR users.id IN (:lstUserId)) AND \n"
            + "    (:email = '' OR users.email ILIKE ('%' || :email || '%')) AND\n"
            + "    (users.last_name IS NOT NULL AND users.first_name IS NOT NULL AND users.email IS NOT NULL AND users.phone_number IS NOT NULL)";

    public static final String GET_LIST_ADMIN_MANAGER_NAME =
        "SELECT DISTINCT"
            + "    users.id AS id, \n"
            + "    CONCAT(users.last_name,' ',users.first_name) AS value, \n"
            + "    ur.numberRole, affiliate_users.id as affId \n"
            + "FROM \n"
            + "    {h-schema}users \n"
            + "    LEFT JOIN ( SELECT  \n"
            + "           ur.user_id as user_id,  \n"
            + "           count(1) numberRole  \n"
            + "    FROM {h-schema}users_roles ur  \n"
            + "       GROUP BY ur.user_id ) ur ON ur.user_id = users.id  "
            + "    LEFT JOIN {h-schema}users_roles ON users_roles.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}affiliate_users ON affiliate_users.user_id = users.id \n"
            + "    LEFT JOIN {h-schema}role ON role.id = users_roles.role_id \n"
            + "WHERE \n"
            + "    users.deleted_flag = 1 AND users.status = 1 AND users_roles.role_id IN (:roleAdminIds) AND \n"
            + "    role.deleted_flag = 1 AND role.status = 1 AND \n"
            + "    ('AFFILIATE' <> :type or (affiliate_users.id is null or affiliate_users.affiliate_status = 2)) AND \n"
            + "    (-1 IN (:lstUserId) OR users.id IN (:lstUserId)) AND \n"
            + "    ( \n"
            + "         (CONCAT(users.last_name || ' ' || users.first_name) ILIKE ('%' || :nameOrCodeUser || '%')) OR \n"
            + "         (CONCAT(users.first_name || ' ' || users.last_name) ILIKE ('%' || :nameOrCodeUser || '%')) OR \n"
            + "         users.admin_code ILIKE ('%' || :nameOrCodeUser || '%') OR \n"
            + "         :nameOrCodeUser = '' \n"
            + "    ) AND \n"
            + "    (users.last_name IS NOT NULL AND users.first_name IS NOT NULL AND users.email IS NOT NULL AND users.phone_number IS NOT NULL)";

    public static final String GET_LIST_PARTITION_ADMIN_NAME =
        "SELECT DISTINCT "
            + "     users.id, \n"
            + "     users.email, \n"
            + "     CONCAT(users.last_name || ' ' || users.first_name) AS name \n"
            + "FROM \n"
            + "     {h-schema}users \n"
            + "     JOIN {h-schema}crm_data_partition on users.id = ANY(lst_admin_id)\n"
            + "where \n"
            + "     users.status = 1 AND \n"
            + "     (-1 = :partitionId OR crm_data_partition.id = :partitionId) AND \n"
            + "     CONCAT(users.last_name || ' ' || users.first_name) ILIKE ('%' || :adminName || '%') ";

    public static final String GET_LIST_ADMIN_ROLE_NAME =
        "SELECT DISTINCT "
            + "     role.id, \n"
            + "     role.display_name AS name \n"
            + "FROM "
            + "     {h-schema}role \n"
            + "     LEFT JOIN {h-schema}users_roles ON users_roles.role_id = role.id \n"
            + "     LEFT JOIN {h-schema}users ON users.id = users_roles.user_id \n"
            + "     LEFT JOIN {h-schema}role_portal ON role_portal.role_id = role.id \n"
            + "     LEFT JOIN {h-schema}portal ON portal.id = role_portal.portal_id \n"
            + "WHERE \n "
            + "    role.status = 1 AND role.deleted_flag = 1 AND role.display_name IS NOT NULL AND \n"
            + "    portal.name = 'ADMIN' AND \n"
            + "    role.display_name ILIKE ('%' || :filter || '%') ";

    public static final String GET_LIST_PARTITION_AM_NAME =
        "SELECT DISTINCT "
            + "     users.id, \n"
            + "     users.email, \n"
            + "     CONCAT(users.last_name || ' ' || users.first_name) AS name \n"
            + "FROM \n"
            + "     {h-schema}users \n"
            + "     JOIN {h-schema}crm_data_partition on users.id = ANY(lst_am_id)\n"
            + "where \n"
            + "     users.status = 1 AND \n"
            + "     (-1 = :partitionId OR crm_data_partition.id = :partitionId) AND \n"
            + "     CONCAT(users.last_name || ' ' || users.first_name) ILIKE ('%' || :amName || '%') ";


    public static final String GET_LIST_PURCHASE_SERVICE_COMBO_INFO =
        "SELECT DISTINCT \n" +
            "       mview_service_combo.id AS id,\n" +
            "       mview_service_combo.unique_id AS uniqueId,\n" +
            "       mview_service_combo.name AS name\n" +
            "FROM \n" +
            "    {h-schema}report_view_service_combo_uniqueid as mview_service_combo\n" +
            "    LEFT JOIN {h-schema}combo ON combo.id = mview_service_combo.id AND mview_service_combo.is_service = false \n" +
            "    LEFT JOIN {h-schema}combo_plan ON combo_plan.combo_id = combo.id \n " +
            "    LEFT JOIN {h-schema}services ON services.id = mview_service_combo.id AND mview_service_combo.is_service = true \n" +
            "    LEFT JOIN {h-schema}pricing ON pricing.service_id = services.id \n " +
            "    LEFT JOIN {h-schema}subscriptions ON subscriptions.pricing_id = pricing.id or subscriptions.combo_plan_id = combo_plan.id \n" +
            "WHERE \n" +
            "    (mview_service_combo.name ILIKE '%' || :filter || '%') AND \n" +
            "    (:customerId = -1 OR :customerId = subscriptions.user_id)";

    public static final String GET_LIST_PURCHASE_PRICING_COMBO_PLAN =
        "SELECT DISTINCT on (mExportView.draft_unique_id) \n" +
            "    mExportView.id AS id, \n" +
            "    mExportView.unique_id AS uniqueId, \n" +
            "    mExportView.name AS name, \n" +
            "    (mExportView.unique_id % 10) as type -- unique_id pricing: *0000, combo-plan: *0001 -- \n" +
            "FROM \n" +
            "    {h-schema}export_view_pricing_and_combo_plan_namnd AS mExportView \n" +
            "    LEFT JOIN {h-schema}subscriptions ON (subscriptions.pricing_id = mExportView.id AND subscriptions.pricing_id IS NOT NULL) OR \n" +
            "        (subscriptions.combo_plan_id = mExportView.id AND subscriptions.combo_plan_id IS NOT NULL) \n" +
            "WHERE \n" +
            "    (mExportView.name ILIKE '%' || :filter || '%') AND \n" +
            "    (-1 IN (:lstServiceUniqueId) OR mExportView.service_id IN (:lstServiceUniqueId)) AND \n " +
            "    (:customerId = -1 OR :customerId = subscriptions.created_by) \n" +
            "ORDER BY mExportView.draft_unique_id DESC, mExportView.unique_id DESC";

    public static final String GET_LIST_SERVICE_INFO =
        "SELECT DISTINCT \n" +
            "       services.id AS id,\n" +
            "       concat(services.id, '0000')::::bigint as uniqueId,  \n" +
            "       services.service_name AS name\n" +
            "FROM \n" +
            "    {h-schema}services \n" +
            "    LEFT JOIN {h-schema}subscriptions ON subscriptions.service_id = services.id \n" +
            "WHERE \n" +
            "    (services.service_name ILIKE '%' || :filter || '%') AND \n" +
            "    (:customerId = -1 OR :customerId = subscriptions.created_by) AND \n" +
            "    services.deleted_flag = 1";

    public static final String GET_LIST_ADMIN_CODE =
        "select id, admin_code as name from {h-schema}users where admin_code is not null and admin_code ilike '%' || :adminCode || '%'";

    public static final String GET_LIST_PRICING_APPLY_LAYOUT =
        "select distinct \n" +
            "    entity_apply.id as id, \n" +
            "    entity_apply.name \n " +
            "from \n" +
            "    {h-schema}feature_view_field_force_get_list_entity_apply as entity_apply\n" +
            "where \n" +
            "    entity_apply.deleted_flag = 1 and entity_apply.category = :category and " +
            "    (entity_apply.name ILIKE '%' || :filter || '%')";

    public static final String GET_ENTERPRISE_SETUP_ADDRESS_BIZ_D1_BODY =
                    "from {h-schema}enterprise \n" +
                    "where \n" +
                    "   user_id is null \n" +
                    "   and deleted_flag = 1 \n" +
                    "   and setup_address is not null \n" +
                    "   and coalesce(setup_address, '') <> '' \n" +
                    "   and (:address = '' or setup_address ilike '%' || :address || '%')";

    public static final String GET_ENTERPRISE_SETUP_ADDRESS_BIZ_D1 =
            "select setup_address as value \n" +
                    GET_ENTERPRISE_SETUP_ADDRESS_BIZ_D1_BODY;

    public static final String GET_ENTERPRISE_SETUP_ADDRESS_BIZ_COUNT =
            "select count(*) \n" +
                    GET_ENTERPRISE_SETUP_ADDRESS_BIZ_D1_BODY;

    public static final String GET_ENTERPRISE_SETUP_ADDRESS_BIZ_D2 =
            "select setup_address as value \n" +
                    "from {h-schema}enterprise \n" +
                    "where \n" +
                    "   user_id is not null \n" +
                    "   and deleted_flag = 1 \n" +
                    "   and num_subs = 0 \n" +
                    "   and setup_address is not null \n" +
                    "   and coalesce(setup_address, '') <> '' \n" +
                    "   and (:address = '' or setup_address ilike '%' || :address || '%')";

    public static final String GET_ADDRESS_BIZ_D3 =
            "select address as value \n" +
                    "from {h-schema}address \n" +
                    "where \n" +
                    "   type = 1 \n" +
                    "   and user_id in ( \n" +
                    "       select user_id \n" +
                    "       from {h-schema}enterprise \n" +
                    "       where \n" +
                    "           customer_state = 0 \n" +
                    "           and deleted_flag = 1 \n" +
                    "   ) \n" +
                    "   and address is not null \n" +
                    "   and coalesce(address, '') <> '' \n" +
                    "   and (:address = '' or address ilike '%' || :address || '%')";

    public static final String GET_TOTAL_NAME_AFFILIATE =
            "select \n" +
                    "		distinct au.id as id,\n" +
                    "		au.parent_affiliate_code as parentCode,\n" +
                    "		au.affiliate_code as code,\n" +
                    "		au.user_id as userId,\n" +
                    "		u.email as email,\n" +
                    "		au.affiliate_level as level,\n" +
                    "		u.name as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id \n" +
                    "where (au.affiliate_status = 2 or au.is_approved = true)\n" +
                    "   and au.deleted_flag = 1\n" +
                    "   and (-1 IN (:lstUserId) or au.user_id IN (:lstUserId))\n" +
                    "   and u.deleted_flag = 1";

    public static final String GET_NAME_AFFILIATE =
            "select \n" +
                    "    au.id as id,\n" +
                    "    au.parent_affiliate_code as parentCode,\n" +
                    "    au.affiliate_code as code,\n" +
                    "    u.name as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n" +
                    "where (au.affiliate_status = 2 or au.is_approved = true)\n" +
                    "   and au.deleted_flag = 1\n" +
                    "   and u.deleted_flag = 1";

    public static final String GET_EMAIL_AFFILIATE =
            "select \n" +
                    "    au.id as id,\n" +
                    "    au.parent_affiliate_code as parentCode,\n" +
                    "    au.affiliate_code as code,\n" +
                    "    u.email as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n" +
                    "where (au.affiliate_status = 2 or au.is_approved = true)\n" +
                    "   and au.deleted_flag = 1\n" +
                    "   and u.deleted_flag = 1";

    public static final String GET_CODE_AFFILIATE =
            "select \n" +
                    "    au.id as id,\n" +
                    "    au.parent_affiliate_code as parentCode,\n" +
                    "    au.affiliate_code as code,\n" +
                    "    au.affiliate_code as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n" +
                    "where (au.affiliate_status = 2 or au.is_approved = true) \n" +
                    "   and au.deleted_flag = 1\n" +
                    "   and u.deleted_flag = 1";

    public static final String GET_PHONE_AFFILIATE =
            "select \n" +
                    "    au.id as id,\n" +
                    "    au.parent_affiliate_code as parentCode,\n" +
                    "    au.affiliate_code as code,\n" +
                    "    u.phone_number as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n";

    public static final String GET_CREATED_AFFILIATE_LINK =
            "select \n" +
                    " distinct on (u.id)\n" +
                    " al.id as id,\n" +
                    " u.id as userId,\n" +
                    " au.parent_affiliate_code as parentCode,\n" +
                    " au.affiliate_code as code,\n" +
                    " u.name as name,\n" +
                    " concat_ws(' - ', u.name,u.email) as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n" +
                    "left join {h-schema}affiliate_link al on al.assigned_to = u.id\n" +
                    "order by u.id";

    public static final String GET_ASSIGNED_AFFILIATE_LINK =
            "select \n" +
                    " distinct on (u.id)\n" +
                    " al.id as id,\n" +
                    " u.id as userId,\n" +
                    " au.parent_affiliate_code as parentCode,\n" +
                    " au.affiliate_code as code,\n" +
                    " u.name as name,\n" +
                    " concat_ws(' - ', u.name,u.email) as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n" +
                    "left join {h-schema}affiliate_link al on al.assigned_to = u.id\n" +
                    "where au.affiliate_status = 2 or au.is_approved = true\n" +
                    "order by u.id";

    public static final String GET_TIN_AFFILIATE =
            "select \n" +
                    "    au.id as id,\n" +
                    "    au.parent_affiliate_code as parentCode,\n" +
                    "    au.affiliate_code as code,\n" +
                    "    u.tin as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id\n";

    public static final String GET_LIST_TARGET_NAME_CODE =
        "SELECT \n"
            + "     mTarget.id AS id, \n"
            + "     mTarget.name AS name, \n"
            + "     mTarget.code AS code  \n"
            + "FROM "
            + "     {h-schema}crm_revenue_target mTarget \n"
            + "WHERE \n"
            + "    (:objectType = -1 OR mTarget.object_type = :objectType) AND \n"
            + "    (:targetName = '' OR mTarget.name ILIKE ('%' || :targetName || '%')) AND \n"
            + "    (:targetCode = '' OR mTarget.code ILIKE ('%' || :targetCode || '%'))";

    public static final String GET_LIST_DETAIL_TARGET_NAME_CODE =
        "SELECT DISTINCT \n"
            + "     target_id AS id, \n"
            + "     target_name AS name, \n"
            + "     target_code AS code  \n"
            + "FROM \n"
            + "     {h-schema}feature_view_get_actual_revenue_subscriptions as mTargetView \n"
            + "WHERE \n"
            + "    (:objectId = -1 OR object_id = :objectId) AND \n"
            + "    (:objectType = -1 OR object_type = :objectType) AND \n"
            + "    (:targetName = '' OR target_name ILIKE ('%' || :targetName || '%')) AND \n"
            + "    (:targetCode = '' OR target_code ILIKE ('%' || :targetCode || '%')) ";

    public static final String GET_LIST_TARGET_ADMIN_PARTITION_NAME =
        "SELECT DISTINCT \n"
            + "    crm_data_partition.name as name, \n"
            + "    crm_data_partition.id as id \n"
            + "FROM \n"
            + "    {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "    JOIN {h-schema}crm_data_partition ON "
            + "        (mTargetView.object_id = ANY(crm_data_partition.lst_admin_id) "
            + "        OR (mTargetView.object_id = ANY(crm_data_partition.lst_am_id))) \n"
            + "WHERE \n"
            + "    (:targetId = -1 OR mTargetView.target_id = :targetId) \n"
            + "    AND (crm_data_partition.name ILIKE ('%' || :partitionName || '%'))  ";

    public static final String GET_LIST_TARGET_ADMIN_EMAIL =
        "SELECT DISTINCT \n"
            + "     object_id as id,\n"
            + "     email as value \n"
            + "FROM \n"
            + "     {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "WHERE \n"
            + "     (:targetId = -1 OR target_id = :targetId) \n"
            + "     AND (email ILIKE ('%' || :email|| '%')) ";

    public static final String GET_LIST_TARGET_OBJECT_NAME =
        "SELECT DISTINCT \n"
            + "     object_id as id ,\n"
            + "     object_name as name \n"
            + "FROM \n"
            + "     {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "WHERE \n"
            + "     (:targetId = -1 OR target_id = :targetId) \n"
            + "     AND (object_name ILIKE ('%' || :objectName || '%')) ";

    public static final String GET_LIST_TARGET_OBJECT_CODE =
        "SELECT DISTINCT \n"
            + "     object_id as id, \n"
            + "     object_code as value \n"
            + "FROM \n"
            + "     {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "WHERE \n"
            + "     (:targetId = -1 OR target_id = :targetId) \n"
            + "     AND (object_code ILIKE ('%' || :objectCode || '%')) ";

    public static final String GET_LIST_TARGET_CUSTOMER_EMAIL =
        "with mSubTargetApply as ( \n" +
        "    select \n" +
        "        unnest(string_to_array((select f.subcode \n" +
        "        from {h-schema}func_get_actual_revenue_by_target_id(target.object_type, target.start_date, target.end_date, target.id, target.target_type) f(amount, subcode)), ',')) :::: bigint as billId\n" +
        "    from {h-schema}crm_revenue_target target \n" +
        "    where target.id = :targetId \n" +
        "    union \n" +
        "    select \n" +
        "        unnest(string_to_array((select f.subcode \n" +
        "        FROM {h-schema}func_get_actual_revenue_by_target_value_id(target.object_type, target.start_date, target.end_date, targetValue.id, target.target_type) f(amount, subcode)), ',')) :::: bigint as billId\n" +
        "    from {h-schema}crm_revenue_target target \n" +
        "        left join {h-schema}crm_revenue_target_value targetValue ON targetValue.target_id = target.id \n" +
        "    where targetValue.id in (:lstTargetValueId) \n" +
        ") \n" +
        "select distinct \n" +
        "users.email as value \n" +
        "from {h-schema}users \n" +
        "join {h-schema}subscriptions on users.id = subscriptions.user_id \n" +
        "join {h-schema}billings on billings.subscriptions_id = subscriptions.id \n" +
        "join mSubTargetApply on billings.id = mSubTargetApply.billId";

    public static final String GET_LIST_SERVICE_NAME =
        "SELECT \n"
            + "     services.id AS id, \n"
            + "     services.service_name AS name \n"
            + "FROM {h-schema}services \n"
            + "     JOIN {h-schema}categories ON services.categories_id = categories.id AND categories.status = 1 AND categories.deleted_flag = 1 \n"
            + "WHERE \n"
            + "     ('' = :filter OR services.service_name ILIKE ('%' || :filter || '%')) AND \n"
            + "     services.status =  1 AND \n"
            + "     trim(services.service_name) <> '' ";

    public static final String GET_LIST_TARGET_PRODUCT_ADMIN_INFO =
        "SELECT \n "
            + "     users.id AS id, \n"
            + "     CONCAT_WS(' ', users.last_name, users.first_name) AS name, \n"
            + "     users.admin_code AS code, \n"
            + "     users.email AS email \n"
            + "FROM \n"
            + "     {h-schema}feature_view_get_actual_revenue_subscriptions mTargetView\n"
            + "LEFT JOIN \n"
            + "     {h-schema}users ON mTargetView.assignee_id = users.id \n"
            + "WHERE \n"
            + "     ( mTargetView.object_type = 1 ) AND \n"
            + "     ( mTargetView.target_id  = :targetId ) AND \n"
            + "     ( mTargetView.object_id = :serviceId ) AND \n"
            + "     ( '' = :adminName OR users.name ILIKE ('%' || :adminName || '%')) AND \n"
            + "     ( '' = :adminCode OR users.name ILIKE ('%' || :adminCode || '%')) AND \n"
            + "     ( '' = :adminEmail OR users.name ILIKE ('%' || :adminEmail || '%')) ";

    public static final String GET_LIST_SPDV_AFFILIATE_LINK =
            "select\n" +
                    " distinct * \n" +
                    "from  \n" +
                    "(\n" +
                    " select \n" +
                    "  case \n" +
                    "   when se.id is not null then se.id * 10000\n" +
                    "   when co.id is not null then co.id * 10000 + 1\n" +
                    "  end as id,\n" +
                    "  case \n" +
                    "   when se.id is not null then se.service_name\n" +
                    "   when co.id is not null then co.combo_name\n" +
                    "  end as value\n" +
                    " from {h-schema} affiliate_commission_event ace \n" +
                    " left join {h-schema} affiliate_users au on ace.user_id = au.user_id\n" +
                    " left join {h-schema} billings b on ace.billing_id = b.id \n" +
                    " left join {h-schema} subscriptions s on s.id = b.subscriptions_id\n" +
                    " left join {h-schema} affiliate_link al on al.link_code = ace.link_code\n" +
                    " left join {h-schema} pricing pri on s.pricing_id is not null and s.pricing_id = pri.id \n" +
                    " left join {h-schema} combo_plan cp on s.combo_plan_id is not null and s.combo_plan_id = cp.id \n" +
                    " left join {h-schema} services se on se.id = pri.service_id\n" +
                    " left join {h-schema} combo co on co.id = cp.combo_id\n" +
                    " where \n" +
                    "  al.assigned_to in (:userIds)\n" +
                    ") as data\n" +
                    "where '' = :name OR data.value ilike ('%' || :name || '%')";

    public static final String GET_LIST_SPDV_PROVINCE =
            "select\n" +
                    "p.id as id,\n" +
                    "p.name as name,\n" +
                    "p.code as code\n" +
                    "from {h-schema}province as p\n" +
                    "where \n" +
                    "('' = :provinceName or p.name ilike ('%' || :provinceName || '%'))\n" +
                    "ORDER BY p.display_order ASC";

    public static final String GET_LIST_CATEGORY =
            "select " +
                    " id as id,\n" +
                    " name as value\n" +
                    "from {h-schema}categories " +
                    "WHERE \n" +
                    " '' = :name OR name ilike ('%' || :name || '%')";

    public static final String GET_LIST_BILLING_CODE_AFFILIATE =
            "select \n" +
                    " b.id as id,\n" +
                    " b.billing_code as value\n" +
                    "from {h-schema}affiliate_commission_event ace \n" +
                    "left join {h-schema}billings b on ace.billing_id = b.id " +
                    "WHERE \n" +
                    " '' = :name OR b.billing_code ilike ('%' || :name || '%')";

    public static final String GET_LIST_PROVIDER =
        "select\n" +
            "distinct a.userId as id,\n" +
            "a.value\n" +
            "from (\n" +
            " select \n" +
            " se.id * 10 as id,\n" +
            "  u.id as userId,\n" +
            " u.name as value  \n" +
            "from {h-schema}services se \n" +
            "join {h-schema}users u on se.user_id = u.id \n" +
            "where (:devId = -1 or :devId = se.user_id) \n" +
            "union \n" +
            "select \n" +
            " (co.id * 10) + 1 as id,\n" +
            "  u.id as userId,\n" +
            " u.name as value  \n" +
            "from {h-schema}combo co \n" +
            "join {h-schema}users u on co.user_id = u.id\n" +
            "where (:devId = -1 or :devId = co.user_id) \n" +
            ") as a \n" +
            "where \n" +
            " '' = :filter OR a.value ilike ('%' || :filter || '%')\n";

    public static final String GET_LIST_SPDV =
            "select\n" +
                    " a.*\n" +
                    " from (\n" +
                    "  select \n" +
                    "  se.id * 10 as id,\n" +
                    "  se.service_name as value \n" +
                    " from {h-schema}services se \n" +
                    "join {h-schema}users u on se.user_id = u.id \n" +
                    "where\n" +
                    " (:devId = -1 or :devId = u.id) and \n" +
                    " (:adminProvinceId = -1 or :adminProvinceId = u.province_id)\n" +
                    " union \n" +
                    " select \n" +
                    "  (co.id * 10) + 1 as id,\n" +
                    "  co.combo_name as value \n" +
                    " from {h-schema}combo co \n" +
                    "join {h-schema}users u on co.user_id = u.id \n" +
                    "where\n" +
                    " (:devId = -1 or :devId = u.id) and \n" +
                    " (:adminProvinceId = -1 or :adminProvinceId = u.province_id)\n" +
                    " ) as a \n" +
                    " where \n" +
                    "  '' = :filter OR a.value ilike ('%' || :filter || '%')\n" +
                    "  and a.value is not null";
    public static final String DELETE_OTP_EXPIRED =
        "delete from {h-schema}otp where created_at < (current_date - interval '1 month')";
    public static final String GET_LIST_DEV_APPROVED =
            "select u.id as id, u.name as value " +
                    "from {h-schema}users u " +
                    "left join {h-schema}users_roles ur ON u.id = ur.user_id " +
                    "where ur.role_id IN (4, 7, 8) and u.parent_id = -1 and u.deleted_flag = 1 " +
                    "and ('' = :name OR u.name ilike ('%' || :name || '%'))";

    public static final String GET_SERVICE_ID_FROM_LINK =
            "SELECT source_object_id\n" +
                    "FROM {h-schema}affiliate_link\n" +
                    "WHERE source_object_type = :type\n" +
                    "	AND source_type = 1\n" +
                    "	AND deleted_flag = 1\n" +
                    "	AND assigned_to != created_by\n" +
                    "	AND assigned_to = :assignedTo";

    public static final String GET_ALL_LASTEST_SERVICE_COMBO_INFO_BY_LEVEL =
            "SELECT DISTINCT \n" +
                    "		lastest_service_combo.provider AS userId, \n" +
                    "		lastest_service_combo.type AS type,\n" +
                    "		lastest_service_combo.ID AS ID,\n" +
                    "		lastest_service_combo.UniqueId AS UniqueId,\n" +
                    "		lastest_service_combo.Name AS Name\n" +
                    "FROM ( \n" +
                    "(SELECT DISTINCT ON ((COALESCE(s.services_draft_id, s.id))) \n" +
                    "		0 AS type,\n" +
                    "		s.id AS ID,\n" +
                    "		(concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
                    "		s.categories_id AS categories_id, \n" +
                    "		s.deleted_flag AS deleted_flag, \n" +
                    "		s.approve AS approve, \n" +
                    "		s.status AS status, \n" +
                    "		s.user_id AS provider, \n" +
                    "		s.customer_type_code AS customer_type_code,\n" +
                    "		s.service_name AS Name\n" +
                    "FROM {h-schema}services AS s\n" +
                    "JOIN {h-schema}users AS provider ON provider.id = s.user_id \n" +
                    "WHERE s.id in (:service) \n" +
                    "ORDER BY COALESCE(s.services_draft_id, s.id) DESC, s.id DESC \n" +
                    ")\n" +
                    "UNION ALL\n" +
                    "(SELECT DISTINCT ON ((COALESCE(co.combo_draft_id, co.id)))\n" +
                    "	 1 AS type,\n" +
                    "	 co.id AS ID,\n" +
                    "	(concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
                    "	CAST ( UNNEST ( string_to_array( co.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
                    "	co.deleted_flag AS deleted_flag, \n" +
                    "	co.approve AS approve, \n" +
                    "	co.status AS status, \n" +
                    "	co.user_id AS provider, \n" +
                    "	co.customer_type_code AS customer_type_code,\n" +
                    "	co.combo_name AS Name\n" +
                    "FROM {h-schema}combo AS co\n" +
                    "JOIN {h-schema}users AS provider ON provider.id = co.user_id \n" +
                    "WHERE co.id in (:combo) \n" +
                    "ORDER BY COALESCE(co.combo_draft_id, co.id) DESC, co.id DESC \n" +
                    ")) AS lastest_service_combo\n" +
                    "WHERE (lastest_service_combo.deleted_flag = 1 AND lastest_service_combo.approve = 1 AND lastest_service_combo.status = 1)";

    public static final String FIND_AFFILIATE_USER_BY_USER_ID =
            "select \n" +
                    "		au.id as id,\n" +
                    "		au.parent_affiliate_code as parentCode,\n" +
                    "		au.affiliate_code as code,\n" +
                    "		au.user_id as userId,\n" +
                    "		u.email as email,\n" +
                    "		au.affiliate_level as level,\n" +
                    "		u.name as value\n" +
                    "from {h-schema}affiliate_users au \n" +
                    "left join {h-schema}users u on au.user_id = u.id \n" +
                    "where (au.affiliate_status = 2 or au.is_approved = true)\n" +
                    "   and au.deleted_flag = :deletedFlag\n" +
                    "   and u.deleted_flag = :deletedFlag\n" +
                    "   and au.user_id = :userId";
    public static final String GET_SERVICE_COMBO =
            " WITH max_combo_version AS (\n" +
                "         SELECT max(combo_max.id) AS latest_combo_id,\n" +
                "            combo_max.combo_draft_id\n" +
                "           FROM {h-schema}combo combo_max\n" +
                "          GROUP BY combo_max.combo_draft_id\n" +
                "        )\n" +
            "SELECT DISTINCT \n" +
                    "    lastest_service_combo.provider AS userId, \n" +
                    "    lastest_service_combo.type AS type,\n" +
                    "    lastest_service_combo.ID AS ID,\n" +
                    "    lastest_service_combo.UniqueId AS UniqueId,\n" +
                    "    lastest_service_combo.Name AS Name\n" +
                    "FROM ( \n" +
                    "(SELECT DISTINCT ON ((COALESCE(s.services_draft_id, s.id)), s.id)\n " +
                    "    0 AS type,\n" +
                    "    s.id AS ID,\n" +
                    "    (concat(s.id, '0000'))::::bigint AS UniqueId,\n" +
                    "    s.categories_id AS categories_id, \n" +
                    "    s.deleted_flag AS deleted_flag, \n" +
                    "    s.approve AS approve, \n" +
                    "    s.status AS status, \n" +
                    "    s.user_id AS provider, \n" +
                    "    s.customer_type_code AS customer_type_code," +
                    "    s.service_name AS Name\n" +
                    "FROM {h-schema}services AS s\n" +
                    "JOIN {h-schema}users AS provider ON provider.id = s.user_id " +
                    "ORDER BY COALESCE(s.services_draft_id, s.id) DESC, s.id DESC \n" +
                    ")\n" +
                    "UNION ALL\n" +
                    "(SELECT DISTINCT ON ((COALESCE(co.combo_draft_id, co.id)), co.id)\n" +
                    "   1 AS type,\n" +
                    "   co.id AS ID,\n" +
                    "  (concat(co.id, '0001'))::::bigint AS UniqueId,\n" +
                    "  CAST ( UNNEST ( string_to_array( co.categories_id, ',' ) ) AS INTEGER ) AS categories_id,\n" +
                    "  co.deleted_flag AS deleted_flag, \n" +
                    "  co.approve AS approve, \n" +
                    "  co.status AS status, \n" +
                    "  co.user_id AS provider, \n" +
                    "  co.customer_type_code AS customer_type_code," +
                    "  co.combo_name AS Name\n" +
                    "FROM {h-schema}combo AS co\n" +
                    "join max_combo_version maxVersion ON maxVersion.latest_combo_id = co.id \n" +
                    "JOIN {h-schema}users AS provider ON provider.id = co.user_id \n" +
                    "ORDER BY COALESCE(co.combo_draft_id, co.id) DESC, co.id DESC \n" +
                    ")) AS lastest_service_combo\n" +
                    "WHERE ((lastest_service_combo.deleted_flag = 1 AND lastest_service_combo.approve = 1 AND lastest_service_combo.status = 1) AND (lastest_service_combo.Name ILIKE '%' || :filter || '%')\n" +
                    "      AND (-1 IN (:categoryList) OR lastest_service_combo.categories_id IN (:categoryList)) \n" +
                    "      AND (:userId = -1 OR :userId = lastest_service_combo.provider) \n" +
                    "      AND (:customerTypeStr = 'ALL' OR lastest_service_combo.customer_type_code SIMILAR TO '%('|| :customerTypeStr ||')%'))\n";

    public static final String GET_LIST_AFFILIATE_AGENCY =
        "SELECT \n" +
            "    affUser.user_id AS id, \n" +
            "    affUser.affiliate_code AS code, \n" +
            "    COALESCE(users.name, CONCAT_WS(' ', users.last_name, users.first_name)) AS name \n" +
            "FROM {h-schema}affiliate_users AS affUser \n" +
            "   JOIN {h-schema}users ON affUser.user_id = users.id \n" +
            "WHERE \n" +
            "   affUser.affiliate_level = 1 AND \n" +
            "   affUser.is_approved = true AND \n" +
            "   affUser.deleted_flag = 1 AND \n" +
            "   ('' = :search OR COALESCE(users.name, CONCAT_WS(' ', users.last_name, users.first_name)) ILIKE ('%' || :search || '%') \n" +
            "       OR affUser.affiliate_code ILIKE ('%' || :search || '%'))";

    public static final String GET_LIST_ACTION_HISTORY_TYPE =
        "SELECT \n" +
            "   id, name, code, content, predefined AS isPredefined, (code = :selectedType) AS isSelected \n" +
            "FROM {h-schema}action_history_type \n" +
            "WHERE \n" +
            "      COALESCE(sub_category, category) IN (:objectTypes) AND \n" +
            "      (predefined OR -1 IN (:actionTypes) OR CAST(code AS int8) IN (:actionTypes)) AND \n" +
            "      ('' = :search OR name ILIKE ('%' || :search || '%')) \n" +
            "ORDER BY isSelected DESC, name ASC ";

    public static final String GET_LIST_CUSTOM_ACTION_HISTORY_TYPE =
        "SELECT * FROM (" + GET_LIST_ACTION_HISTORY_TYPE + ") AS actType WHERE actType.isPredefined = :isPredefined";

    public static final String GET_LIST_ADMIN_INFO =
        "WITH adminRoleCTE AS ( \n" +
            "    SELECT \n" +
            "           usrRole.user_id, \n" +
            "           COALESCE(STRING_AGG(role.name, ','), '') AS roles \n" +
            "       FROM {h-schema}users_roles AS usrRole \n" +
            "           JOIN {h-schema}role ON usrRole.role_id = role.id \n" +
            "       WHERE usrRole.user_id IN (SELECT DISTINCT user_id FROM vnpt_dev.users_roles WHERE role_id IN (:roleIds)) \n" +
            "       GROUP BY usrRole.user_id \n"+
            ") \n" +
        "SELECT \n" +
            "    users.id AS id, \n" +
            "    CASE \n" +
            "        WHEN COALESCE(users.last_name, users.first_name) IS NOT NULL THEN CONCAT_WS(' ', users.last_name, users.first_name) \n" +
            "        WHEN users.name IS NOT NULL THEN users.name \n" +
            "        ELSE 'Null' \n" +
            "    END AS name, \n" +
            "    users.email AS email, \n" +
            "    users.admin_code AS code, \n" +
            "    adminRoleCTE.roles AS roles, \n" +
            "    (users.id = :currentUserId) AS isCurrentUser, \n" +
            "    (users.id = :selectedUserId) AS isSelectedUser \n" +
            "FROM {h-schema}users \n" +
            "    JOIN adminRoleCTE ON adminRoleCTE.user_id = users.id \n" +
            "WHERE \n" +
            "      users.deleted_flag = 1 AND \n" +
            "      ('' = :search OR \n" +
            "        CONCAT_WS(' ', users.last_name, users.first_name) ILIKE ('%' || :search || '%') OR \n" +
            "        users.email ILIKE ('%' || :search || '%') \n" +
            "      ) \n" +
            "ORDER BY isSelectedUser DESC, isCurrentUser DESC \n";

    public static final String GET_DATA_USER_ADMIN_NVKD_CODE =
            "SELECT distinct users.admin_code as value, users.id as id \n" +
                    "FROM {h-schema}users \n" +
                    "WHERE \n" +
                    "   users.deleted_flag = 1 and users.admin_code is not null and admin_code <> '' \n" +
                    "   AND (users.id IN (SELECT DISTINCT user_id FROM {h-schema}users_roles WHERE role_id IN (:roleIds))) \n" +
                    "   AND ('' = :search OR users.admin_code ILIKE ('%' || :search || '%')) \n" +
                    "   AND (-1 = :id OR users.id = :id)";

    public static final String GET_DATA_COMBOBOX_ADMIN_DBDL_IMPORT_CREATEBY =
            "SELECT distinct u.email as label, u.email as value \n" +
                    "FROM {h-schema}import_migration im \n" +
                    "   join {h-schema}users u ON u.id = im.created_by \n" +
                    "WHERE (:id = -1 or im.created_by = :id) and (:label = '' or lower(u.email) LIKE ('%' || lower(:label) || '%'))";

    public static final String GET_DATA_COMBOBOX_ADMIN_DBDL_IMPORT_EMAIL =
            "SELECT distinct email as label, email as value \n" +
                    "FROM {h-schema}users \n" +
                    "WHERE (:id = -1 or id = :id) and (:label = '' or lower(email) LIKE ('%' || lower(:label) || '%')) \n" +
                    "   and import_migration_id in (select id from {h-schema}import_migration)";

    public static final String GET_DATA_COMBOBOX_ADMIN_DBDL_IMPORT_SGT =
            "SELECT distinct u.id as id,"
                    + "    CASE  "
                    + "        When u.customer_type = 'CN' then u.rep_personal_cert_number   "
                    + "        ELSE u.tin   "
                    + "    END AS value,  "
                    + "    CASE  "
                    + "        When u.customer_type = 'CN' then u.rep_personal_cert_number   "
                    + "        ELSE u.tin   "
                    + "    END AS label  "
                    + "FROM {h-schema}import_migration im "
                    + "     JOIN {h-schema}users u ON u.import_migration_id = im.id "
                    + "WHERE (:id = -1 or u.id = :id) "
                    + "     and (:label = '' OR u.tin ILIKE ('%' || lower(:label) || '%') OR u.rep_personal_cert_number ILIKE ('%' || lower(:label) || '%'))";

    public static final String GET_DATA_COMBOBOX_ADMIN_TRAN_LIST_MST =
            "SELECT distinct customer_tin as label, customer_tin as value \n" +
                    "FROM {h-schema}transaction_log \n" +
                    "WHERE customer_tin is not null and customer_tin <> '' and (:id = -1 or id = :id) and (:label = '' or lower(customer_tin) LIKE ('%' || lower(:label) || '%'))";

    public static final String GET_DATA_COMBOBOX_ADMIN_TRAN_LIST_NDD =
            "SELECT distinct u.rep_fullname as label, u.rep_fullname as value \n" +
                    "FROM {h-schema}transaction_log tl \n" +
                    "   join {h-schema}users u ON u.id = tl.customer_id \n" +
                    "WHERE u.rep_fullname is not null and u.rep_fullname <> '' and (:id = -1 or u.id = :id) and (:label = '' or lower(u.rep_fullname) LIKE ('%' || lower(:label) || '%'))";

    public static final String GET_DATA_COMBOBOX_ADMIN_TRAN_LIST_SCTCN =
            "SELECT distinct customer_identity as label, customer_identity as value \n" +
                    "FROM {h-schema}transaction_log \n" +
                    "WHERE customer_identity is not null and customer_identity <> '' and (:id = -1 or id = :id) and (:label = '' or lower(customer_identity) LIKE ('%' || lower(:label) || '%'))";

    public static final String COMBINED_SEARCH_SUPPORTERS =
        "select distinct on (users.id)\n" +
            "    users.id as id, \n" +
            "    case \n" +
            "        when affUser.id is null then concat_ws(' ', users.last_name, users.first_name) \n" +
            "        else users.name \n" +
            "    end as name, \n" +
            "    uRoleCount.role_count as roleCount, \n" +
            "    affUser.id as affiliateId, \n" +
            "    users.phone_number AS phone, \n" +
            "    users.email as email, \n" +
            "    case \n" +
            "        when users_roles.role_id IN (:adminRoleIds) then 'ADMIN' \n" +
            "        when users_roles.role_id IN (:devRoleIds) then 'DEV' \n" +
            "        else 'AFFILIATE' \n" +
            "    end as roleType, \n" +
            "    users.admin_code as adminCode,\n" +
            "    string_agg( distinct crm_data_partition.name, ', ') as lstPartitionName\n" +
            "from {h-schema}users \n" +
            "    left join (select user_id as user_id, count(*) role_count from {h-schema}users_roles group by user_id ) uRoleCount ON uRoleCount.user_id = users.id \n" +
            "    left join {h-schema}users_roles ON users_roles.user_id = users.id \n" +
            "    left join {h-schema}affiliate_users as affUser on affUser.user_id = users.id and affUser.deleted_flag = 1\n" +
            "    left join {h-schema}role on role.id = users_roles.role_id \n" +
            "    left join {h-schema}report_view_service_combo_uniqueid as view_unique_id on users.id = view_unique_id.user_id \n" +
            "    left join {h-schema}crm_data_partition on (users.id = ANY(array_cat(crm_data_partition.lst_admin_id, crm_data_partition.lst_am_id))) \n" +
            "where \n" +
            "    users.deleted_flag = 1 and \n" +
            "    users.status = 1 and \n" +
            "    users_roles.role_id IN (:roleIds) and \n" +
            "    (-1 in (:selectedIds) or users.id not in (:selectedIds)) and\n" +
            "    ('AFFILIATE' <> :roleType OR (((affUser.id is null and users_roles.role_id = 9) or (affUser.id is not null and uRoleCount.role_count > 2 and affUser.affiliate_status = 2)))) and \n" +
            "    role.deleted_flag = 1 and role.status = 1 and \n" +
            "    ('' = :keyword or \n" +
            "       (:isName = 1 and \n" +
            "         ( \n" +
            "           (affUser.id is null and concat_ws(' ', users.last_name, users.first_name) ilike ('%' || :keyword || '%')) or \n" +
            "           (affUser.id is not null and users.name ilike ('%' || :keyword || '%')) \n" +
            "         ) \n" +
            "       ) or \n" +
            "       (:isEmail = 1 and coalesce(users.email, '') ilike ('%' || :keyword || '%')) or \n" +
            "       (:isPhone = 1 and coalesce(users.phone_number, '') ilike ('%' || :keyword || '%')) \n" +
            "    ) and \n" +
            "    (:partitionId = -1 OR crm_data_partition.id = :partitionId) and \n" +
            "    (:serviceComboId = -1 OR view_unique_id.unique_id = :serviceComboId) \n" +
            "group by users.id, affUser.id, uRoleCount.role_count, users_roles.role_id\n" +
            "order by users.id, roleType ";

    public static final String GET_LIST_DEPARTMENT_BY_PARENT_ID =
        "SELECT \n" +
            "    id AS id, \n" +
            "    department_name AS name, \n" +
            "    department_code AS code \n" +
            "FROM {h-schema}departments \n" +
            "WHERE \n" +
            "     deleted_flag = 1 AND \n" +
            "     status = 1 AND \n" +
            "     user_id = :parentId AND \n" +
            "     (:search = '' OR (department_name ILIKE '%' || :search || '%') OR (department_code ILIKE '%' || :search || '%'))";

    public static final String GET_DATA_COMBOBOX_ADMIN_COMBO_PLAN_LIST_CREATED_BY =
            "SELECT DISTINCT \n" +
                    "   CASE \n" +
                    "       WHEN u.id IN (SELECT DISTINCT user_id FROM {h-schema}users_roles WHERE role_id IN (1,3,9,2,5,12)) then trim(CONCAT_WS(' ', u.last_name, u.first_name)) \n" +
                    "       ELSE trim(u.name) \n" +
                    "   END AS label, " +
                    "   CASE \n" +
                    "       WHEN u.id IN (SELECT DISTINCT user_id FROM {h-schema}users_roles WHERE role_id IN (1,3,9,2,5,12)) then trim(CONCAT_WS(' ', u.last_name, u.first_name)) \n" +
                    "       ELSE trim(u.name) \n" +
                    "   END AS value \n" +
                    "FROM {h-schema}users u \n" +
                    "WHERE u.deleted_flag = 1 \n" +
                    "   AND trim(CONCAT_WS(' ', u.last_name, u.first_name)) <> '' \n" +
                    "   AND (:label = '' " +
                    "       or lower(trim(CONCAT_WS(' ', u.last_name, u.first_name))) LIKE ('%' || lower(:label) || '%')\n" +
                    "       or lower(trim(u.name)) LIKE ('%' || lower(:label) || '%'))\n" +
                    "   AND ( \n" +
                    "       (u.id IN (SELECT DISTINCT user_id FROM {h-schema}users_roles WHERE role_id IN (SELECT id FROM {h-schema}role WHERE name = 'ROLE_DEVELOPER'))) \n " +
                    "       OR (u.id IN (SELECT DISTINCT user_id FROM {h-schema}users_roles WHERE role_id IN (1,3,9,2,5,12)))\n" +
                    "   )";

    public static final String GET_DATA_COMBOBOX_ADMIN_COMBO_PLAN_LIST_SERVICE =
            "SELECT distinct services.service_name as label, services.id as value \n" +
                    "FROM {h-schema}combo_pricing \n" +
                    "   LEFT JOIN {h-schema}pricing on combo_pricing.object_type = 'PRICING' and pricing.id = combo_pricing.object_id AND pricing.deleted_flag = 1  \n" +
                    "   LEFT JOIN {h-schema}variant_draft ON combo_pricing.object_type = 'DEVICE_VARIANT' " +
                    "       and variant_draft.id = (select max(id) from {h-schema}variant_draft where variant_id = combo_pricing.object_id and deleted_flag = 1 and approved = 1 GROUP BY variant_id)  \n" +
                    "   LEFT JOIN {h-schema}services ON (" +
                    "           (combo_pricing.object_type = 'DEVICE_VARIANT' and services.id = variant_draft.service_id) " +
                    "           OR (combo_pricing.object_type = 'PRICING' and services.id = pricing.service_id)" +
                    "           OR (combo_pricing.object_type = 'DEVICE_NO_VARIANT' and services.id = combo_pricing.object_id)" +
                    "       ) AND services.deleted_flag = 1  \n" +
                    "WHERE \n" +
                    "   combo_pricing.id in ( \n" +
                    "       SELECT combo_pricing.id \n" +
                    "       FROM {h-schema}combo_plan_draft \n" +
                    "           LEFT JOIN {h-schema}combo_plan ON combo_plan.id = ( \n" +
                    "               select max(id) from {h-schema}combo_plan where combo_plan_draft_id = combo_plan_draft.id and deleted_flag = 1 and approve = 1 GROUP BY combo_plan_draft_id) \n" +
                    "           LEFT JOIN {h-schema}combo_pricing ON (combo_plan.id is not null and combo_pricing.id_combo_plan = combo_plan.id) or (combo_plan.id is null and combo_pricing.combo_plan_draft_id = combo_plan_draft.id) \n" +
                    "       WHERE combo_plan_draft.deleted_flag = 1 AND combo_plan_draft.combo_draft_id = :comboDraftId \n" +
                    "   ) and \n" +
                    "   services.service_name is not null \n" +
                    "   and services.service_name <> '' \n" +
                    "   and (:label = '' or lower(services.service_name) LIKE ('%' || lower(:label) || '%')) \n";
}
