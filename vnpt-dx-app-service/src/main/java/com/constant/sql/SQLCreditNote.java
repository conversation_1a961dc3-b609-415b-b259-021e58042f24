package com.constant.sql;

/**
 * <AUTHOR> Halt
 * @version    : 1.0
 * 16/06/2021
 */
public final class SQLCreditNote {
    public static final String FIND_ALL_BY_SUBSCRIPTION_ID =
        "SELECT "
            + "    cn.id AS id, "
            + "    cn.code AS code, "
            + "    coalesce(b.cart_code, b.billing_code) AS billingCode, "
            + "    cn.money_refund AS moneyRefund, "
            + "    cn.created_at AS createdAt, "
            + "    cn.status AS status, "
            + "    cn.note_type AS creditNoteType, "
            + "    rcn.reason, "
            + "    rcn.CONTENT "
            + "FROM "
            + "    {h-schema}subscriptions s "
            + "JOIN {h-schema}billings b ON "
            + "    s.id = b.subscriptions_id "
            + "    AND s.deleted_flag = 1 "
            + "JOIN {h-schema}credit_note cn ON "
            + "    cn.billing_id_apply = b.id "
            + "    AND cn.deleted_flag = 1 "
            + "LEFT JOIN {h-schema}reason_credit_note rcn ON "
            + "    rcn.credit_note_id = cn.id "
            + "    AND rcn.deleted_flag = 1 "
            + "WHERE cn.deleted_flag = 1 AND cn.status <> 3 AND "
            + "    s.id = :subscriptionsId "
            + "    AND (:code = '' "
            + "    OR cn.code = :code ) "
            + "    AND ( :status = -1 "
            + "    OR cn.status = :status ) ";

    public static final String GET_DETAIL_CREDIT_NOTE =
        "SELECT "
            + "    credit.id AS id, "
            + "    credit.code AS code, "
            + "    credit.status AS status, "
            + " CASE "
            + "    WHEN u.customer_type = 'CN' THEN concat(u.last_name, ' ',  u.first_name) "
            + "    ELSE u.name end AS companyName, "
            + " CASE "
            + "    WHEN u.customer_type = 'CN' THEN u.rep_personal_cert_number "
            + "    ELSE u.tin end as taxCode,"
            + "    credit.created_at AS createdAt, "
            + "    COALESCE(credit.money_refund, 0) AS moneyRefund, "
            + "    COALESCE(credit.money_refund, 0) AS creditNoteValue, "
            + "    b.id AS billingId, "
            + "    b.cart_code is not null AS isCart, "
            + "    credit.billing_id_apply AS billingIdApply, "
            + "    COALESCE(b.cart_code, b.billing_code) AS billingCode, "
            + "    b.current_payment_date AS paymentDate, "
            + "    b.status AS billingStatus, "
            + "    COALESCE(b.total_amount, 0) AS billingValue, "
            + "    COALESCE(b.total_amount_after_adjustment, 0) AS paymentValue, "
            + "    COALESCE ( u.address, concat_ws(', ', sd.name, w.name, d.name, p.name)) AS address, "
            + "    COALESCE(s4.service_name, c2.combo_name) AS serviceName, "
            + "    COALESCE(p3.pricing_name, cp2.combo_name) AS pricingName, "
            + " CASE "
            + "     WHEN credit.billing_id_apply IS NULL THEN COALESCE(s2.user_id , c.user_id) "
            + "     ELSE COALESCE(se.user_id, cb.user_id) "
            + " END AS providerId, "
            + "    u.province_id AS provinceId, "
            + "    s.user_id AS userId, "
            +  " CASE"
            +  "   WHEN u.customer_type = 'HKD' then 'HOUSE_HOLD'  "
            +  "   WHEN u.customer_type = 'CN' then 'PERSONAL'   "
            +  " ELSE 'ENTERPRISE' end AS customerType "
            + "         FROM {h-schema}credit_note credit "
            + "                  JOIN {h-schema}subscriptions s ON credit.subscription_id = s.id "
            + "                  LEFT JOIN {h-schema}billings b on credit.billing_id_apply = b.id "
            + "                  LEFT JOIN {h-schema}pricing p2 ON p2.id = s.pricing_id "
            + "                  LEFT JOIN {h-schema}services s2 ON s2.id = s.service_id "
            + "                  LEFT JOIN {h-schema}combo_plan cp ON cp.id = s.combo_plan_id "
            + "                  LEFT JOIN {h-schema}combo c ON c.id = cp.combo_id "
            + "                  LEFT JOIN {h-schema}users u ON s.user_id = u.id "
            + "                  LEFT JOIN {h-schema}province p ON p.id = u.province_id "
            + "                  LEFT JOIN {h-schema}district d ON d.id = u.district_id AND d.province_id = u.province_id "
            + "                  LEFT JOIN {h-schema}ward w ON w.id = u.ward_id AND w.district_id = u.district_id AND w.province_code = u.province_code "
            +
            "        LEFT JOIN {h-schema}street sd ON sd.id = u.street_id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id AND sd.province_code = u.province_code "
            + "                  LEFT JOIN {h-schema}subscriptions s3 ON b.subscriptions_id = s3.id "
            + "                  LEFT JOIN {h-schema}pricing p3 ON p3.id = s3.pricing_id "
            + "                  LEFT JOIN {h-schema}services s4 ON s4.id = p3.service_id "
            + "                  LEFT JOIN {h-schema}combo_plan cp2 ON cp2.id = s3.combo_plan_id "
            + "                  LEFT JOIN {h-schema}combo c2 ON c2.id = cp2.combo_id "
            + "                  LEFT JOIN {h-schema}subscriptions sb ON b.subscriptions_id = sb.id "
            + "                  LEFT JOIN {h-schema}services se ON se.id = sb.service_id "
            + "                  LEFT JOIN {h-schema}combo_plan cpb ON cpb.id = s3.combo_plan_id "
            + "                  LEFT JOIN {h-schema}combo cb ON cb.id = cpb.combo_id"
            + "         WHERE credit.id = :id AND credit.deleted_flag = 1";

    private static final String SQL_FROM_CREDIT_NOTE =
        "SELECT    "
            + "    cn.id AS id,  "
            + "    cn.money_refund AS creditNoteValue,  "
            + "    cn.code AS code,  "
            + "    cn.created_at AS createdAt,  "
            + "    CASE  "
            + "        WHEN cn.status = 0 THEN 'REFUNDING'  "
            + "        WHEN cn.status = 1 THEN 'ADJUSTED'  "
            + "        WHEN cn.status = 2 THEN 'REFUNDED'  "
            + "        WHEN cn.status = 3 THEN 'DISABLE'  "
            + "    END AS status,  "
            + "    CASE  "
            + "        WHEN cn.status = 0 THEN 'B'  "
            + "        WHEN cn.status = 1 THEN 'C'  "
            + "        WHEN cn.status = 2 THEN 'A'  "
            + "        WHEN cn.status = 3 THEN 'D'  "
            + "    END AS statusSort,  "
            + "    COALESCE(b.billing_code,'') AS billingCode,  "
            + "    b.total_amount AS billingValue,  "
            + " CASE "
            + "    WHEN u.customer_type = 'CN' THEN concat(u.last_name, ' ',  u.first_name) "
            + "    ELSE u.name end AS companyName, "
            + "    CASE  "
            + "        WHEN cn.status = 0 OR (cn.billing_id_apply IS NULL OR cn.billing_id_apply < 0) OR cn.status = 3 THEN 0 "
            + "        ELSE cn.money_refund  "
            + "    END AS moneyRefund "
            + "         FROM {h-schema}credit_note cn "
            + "                  JOIN {h-schema}subscriptions s ON cn.subscription_id = s.id "
            + "                  LEFT JOIN {h-schema}users u ON s.user_id = u.id AND u.deleted_flag = 1 AND u.status = 1 "
            + "                  LEFT JOIN {h-schema}billings b ON cn.billing_id_apply = b.id "
            + "                  AND cn.status NOT IN (1, 3) "
            + "                  LEFT JOIN {h-schema}pricing p2 ON p2.id = s.pricing_id AND p2.deleted_flag = 1"
            + "                  LEFT JOIN {h-schema}services s2 ON s2.id = p2.service_id AND s2.deleted_flag = 1"
            + "                  LEFT JOIN {h-schema}combo_plan cp ON cp.id = s.combo_plan_id AND cp.deleted_flag = 1"
            + "                  LEFT JOIN {h-schema}combo c2 ON c2.id = cp.combo_id AND c2.deleted_flag = 1"
            + "                  LEFT JOIN {h-schema}subscriptions s3 ON b.subscriptions_id = s3.id "
            + "                  LEFT JOIN {h-schema}services se ON se.id = s3.service_id "
            + "                  LEFT JOIN {h-schema}combo_plan cp2 ON cp2.id = s3.combo_plan_id "
            + "                  LEFT JOIN {h-schema}combo c3 ON c3.id = cp2.combo_id";
    public static final String FIND_ALL_CREDIT_NOTE =
            SQL_FROM_CREDIT_NOTE
            + "         WHERE cn.deleted_flag = 1 AND (b.status NOT IN (3, 5, 6) OR b.status IS NULL) AND (LOWER(cn.code) LIKE ('%' || LOWER(:code) || '%') OR '' = :code) "
            + "           AND ('' = :customerName OR LOWER(u.name) SIMILAR TO ('%' || LOWER(:customerName)  || '%') OR LOWER(concat(u.last_name, ' ', u.first_name)) SIMILAR TO ('%' ||  LOWER(:customerName) || '%')) "
            + "           AND (:userId = -1 OR u.id = :userId ) "
            + "           AND ((:providerId = -1 "
            + "           OR se.user_id = :providerId "
            + "           OR c3.user_id = :providerId) "
            + "           OR (cn.billing_id_apply IS NULL AND (:providerId = -1 "
            + "           OR s2.user_id = :providerId "
            + "           OR c2.user_id = :providerId))) "
            + "           AND (:provinceId = - 1 OR u.province_id = :provinceId) "
            + "           AND (-1 = :status OR cn.status = :status)";

    public static final String FIND_ALL_CREDIT_NOTE_ADMIN =
            SQL_FROM_CREDIT_NOTE
            + "         WHERE cn.deleted_flag = 1 AND (b.status NOT IN (3, 5, 6) OR b.status IS NULL) "
            + "           AND (:provinceId = - 1 OR u.province_id = :provinceId) "
            + "           AND (-1 = :status OR cn.status = :status)"
            + "           AND ( "
            + "                  :value = '' or \n"
            + "                  (:searchCode = 1 and (LOWER(cn.code) LIKE ('%' || LOWER(:value) || '%'))) or \n"
            + "                  (:searchCustomerName = 1 and LOWER(u.name) SIMILAR TO ('%' || LOWER(:value)  || '%') OR LOWER(concat(u.last_name, ' ', u.first_name)) SIMILAR TO ('%' ||  LOWER(:value) || '%'))  \n"
            + "                ) ";

    public static final String GET_CREDIT_NOTE_BY_BILL_ID =
        "SELECT NEW com.dto.creditNote.CreditNoteBillDTO("
        + "cn.id, cn.code, cn.moneyRefund) "
        + "FROM CreditNote cn "
        + "     WHERE cn.deletedFlag <> 0 "
        + "     AND cn.status <> 4 "
        + "     AND cn.billingId = :id " ;

    public static final String GET_ALL_CREDIT_NOTE_BY_BILL_ID =
            "SELECT NEW com.dto.creditNote.CreditNoteBillDTO("
            + "cn.id, cn.code, cn.moneyRefund) "
            + "FROM CreditNote cn "
            + "     WHERE cn.deletedFlag <> 0 "
            + "     AND cn.status <> 4 "
            + "     AND (cn.billingId = :billingId or cn.billingIdApply = :billingId) " ;

    public static final String GET_LIST_OTHER_CREDIT_NOTE_OF_BILL =
        "SELECT NEW com.dto.creditNote.CreditNoteBillDTO("
            + "cn.id, cn.code, cn.moneyRefund) "
            + "FROM CreditNote cn "
            + "     WHERE cn.deletedFlag <> 0 "
            + "     AND cn.status <> 4 "
            + "     AND cn.billingIdApply = :billingId "
            + "     AND cn.id <> :creditNoteId" ;

    public static final String GET_LIST_CREDIT_NOTE_BY_SUBSCRIPTION_ID_AND_STATUS =
        "SELECT new com.dto.creditNote.CreditNoteCalculateDTO( "
            + "    cn.id, "
            + "    cn.code, "
            + "    concat(COALESCE(s2.serviceName, c.comboName), '/', COALESCE(p.pricingName, cp.comboName)), "
            + "    cnt.taxId, "
            + "    CASE  "
            + "        WHEN t.id IS NOT NULL THEN concat(t.name, ' ',cnt.taxValue * 100, ' %') "
            + "        ELSE bt.taxValueName "
            + "    END, "
            + "    COALESCE(cnt.taxValue, 0), "
            + "    cn.moneyRefund, "
            + "    cn.moneyRefund, "
            + "    -1, "
            + "    b2.subscriptionsId, "
            + "    cn.subscriptionId) "
            + "FROM CreditNote cn  "
            + "LEFT JOIN CreditNoteTax cnt ON cnt.creditNoteId = cn.id "
            + "LEFT JOIN Tax t ON t.id = cnt.taxId  "
            + "LEFT JOIN BillTax bt ON bt.id = cnt.taxId  "
            + "LEFT JOIN Subscription s ON s.id = cn.subscriptionId  "
            + "LEFT JOIN Pricing p ON p.id = s.pricingId  "
            + "LEFT JOIN ServiceEntity s2 ON p.serviceId = s2.id  "
            + "LEFT JOIN ComboPlan cp ON cp.id = s.comboPlanId  "
            + "LEFT JOIN Combo c ON c.id = cp.comboId "
            + "LEFT JOIN Bills b ON b.id = cn.billingId "
            + "LEFT JOIN Bills b2 ON b.id = cn.billingIdApply "
            + "WHERE (cn.subscriptionId = :subscriptionId OR (s.userId = :userId AND cn.billingId IS NULL)) AND cn.status = :status AND cn.deletedFlag <> 0 "
            + "AND (cn.billingId IS NULL OR b.status NOT IN (3, 6))";

    public static final String GET_LIST_CREDIT_NOTE_BY_USER_ID_AND_STATUS =
        "SELECT new com.dto.creditNote.CreditNoteCalculateDTO( "
            + "    cn.id, "
            + "    cn.code, "
            + "CASE "
            + "     WHEN cn.name is not null THEN cn.name "
            + "     ELSE concat(COALESCE(s2.serviceName, c.comboName), '/', COALESCE(p.pricingName, cp.comboName)) "
            + "END, "
            + "    cnt.taxId, "
            + "    CASE  "
            + "        WHEN t.id IS NOT NULL THEN concat(t.name, ' ',cnt.taxValue * 100, ' %') "
            + "        ELSE bt.taxValueName "
            + "    END, "
            + "    COALESCE(cnt.taxValue, 0), "
            + "    COALESCE(cn.moneyRefund, 0), "
            + "    COALESCE(cn.moneyRefund, 0), "
            + "    cn.billingId) "
            + "FROM CreditNote cn  "
            + "LEFT JOIN CreditNoteTax cnt ON cnt.creditNoteId = cn.id "
            + "LEFT JOIN Tax t ON t.id = cnt.taxId  "
            + "LEFT JOIN BillTax bt ON bt.id = cnt.taxId  "
            + "LEFT JOIN Subscription s ON s.id = cn.subscriptionId  "
            + "LEFT JOIN Pricing p ON p.id = s.pricingId  "
            + "LEFT JOIN ServiceEntity s2 ON p.serviceId = s2.id  "
            + "LEFT JOIN ComboPlan cp ON cp.id = s.comboPlanId  "
            + "LEFT JOIN Combo c ON c.id = cp.comboId  "
            + "LEFT JOIN Bills b ON b.id = cn.billingId "
            + "WHERE s.userId = :userId AND cn.status = :status AND cn.deletedFlag <> 0 AND "
            + " ((:type NOT IN (3, 4) AND (s.status IN (3, 4) OR s.awaitingCancel = 1)) OR (:type IN (3, 4) AND"
            + " (:subscriptionId = -1L OR cn.subscriptionId = :subscriptionId))) "
            + " AND (cn.billingId IS NULL OR b.status NOT IN (3, 6))";

    public static final String GET_LIST_CREDIT_NOTE_BY_ID =
        "SELECT new com.dto.creditNote.CreditNoteCalculateDTO( "
            + "    cn.id, "
            + "    cn.code, "
            + "   concat(COALESCE(s2.serviceName, c.comboName), '/', COALESCE(p.pricingName, cp.comboName)),  "
            + "    cnt.taxId, "
            + "    CASE  "
            + "        WHEN t.id IS NOT NULL THEN concat(t.name, ' ',cnt.taxValue * 100, ' %') "
            + "        ELSE bt.taxValueName "
            + "    END, "
            + "    COALESCE(cnt.taxValue, 0), "
            + "    COALESCE(cn.moneyRefund, 0), "
            + "    COALESCE(cn.moneyRefund, 0)) "
            + "FROM CreditNote cn  "
            + "LEFT JOIN CreditNoteTax cnt ON cnt.creditNoteId = cn.id "
            + "LEFT JOIN Tax t ON t.id = cnt.taxId  "
            + "LEFT JOIN BillTax bt ON bt.id = cnt.taxId  "
            + "LEFT JOIN Subscription s ON s.id = cn.subscriptionId  "
            + "LEFT JOIN Pricing p ON p.id = s.pricingId  "
            + "LEFT JOIN ServiceEntity s2 ON p.serviceId = s2.id  "
            + "LEFT JOIN ComboPlan cp ON cp.id = s.comboPlanId  "
            + "LEFT JOIN Combo c ON c.id = cp.comboId "
            + "LEFT JOIN Bills b ON b.id = cn.billingId "
            + "WHERE ((:type = 1 AND cn.billingId = :id AND cn.status <> 3) OR (:type = 2 AND cn.billingIdApply = :id AND cn.status <> 3) "
            + "      OR (:type = 3 AND cn.subscriptionId = :id AND cn.status = 1 AND (cn.billingId IS NULL OR b.status NOT IN (3, 6)))"
            + "      OR (:type = 4 AND cn.subscriptionId = :id AND cn.status = 2)) AND  cn.deletedFlag <> 0 ";

    public static final String GET_LIST_CREDIT_NOTE_BY_LIST_ID =
            "SELECT new com.dto.creditNote.CreditNoteCalculateDTO( "
                    + "    cn.id, "
                    + "    cn.code, "
                    + "   concat(COALESCE(s2.serviceName, c.comboName), '/', COALESCE(p.pricingName, cp.comboName)),  "
                    + "    cnt.taxId, "
                    + "    CASE  "
                    + "        WHEN t.id IS NOT NULL THEN concat(t.name, ' ',cnt.taxValue * 100, ' %') "
                    + "        ELSE bt.taxValueName "
                    + "    END, "
                    + "    COALESCE(cnt.taxValue, 0), "
                    + "    COALESCE(cn.moneyRefund, 0), "
                    + "    COALESCE(cn.moneyRefund, 0)) "
                    + "FROM CreditNote cn  "
                    + "LEFT JOIN CreditNoteTax cnt ON cnt.creditNoteId = cn.id "
                    + "LEFT JOIN Tax t ON t.id = cnt.taxId  "
                    + "LEFT JOIN BillTax bt ON bt.id = cnt.taxId  "
                    + "LEFT JOIN Subscription s ON s.id = cn.subscriptionId  "
                    + "LEFT JOIN Pricing p ON p.id = s.pricingId  "
                    + "LEFT JOIN ServiceEntity s2 ON p.serviceId = s2.id  "
                    + "LEFT JOIN ComboPlan cp ON cp.id = s.comboPlanId  "
                    + "LEFT JOIN Combo c ON c.id = cp.comboId "
                    + "LEFT JOIN Bills b ON b.id = cn.billingId "
                    + "WHERE ((:type = 1 AND cn.billingId in (:lstId) AND cn.status <> 3) OR (:type = 2 AND cn.billingIdApply in (:lstId) AND cn.status <> 3) "
                    + "      OR (:type = 3 AND cn.subscriptionId in (:lstId) AND cn.status = 1 AND (cn.billingId IS NULL OR b.status NOT IN (3, 6)))"
                    + "      OR (:type = 4 AND cn.subscriptionId in (:lstId) AND cn.status = 2)) AND  cn.deletedFlag <> 0 ";

    public static final String GET_LIST_CHANGE_CREDIT_NOTE_BY_BILLING_ID =
        "SELECT new com.dto.creditNote.CreditNoteCalculateDTO( "
            + "    cn.creditNoteId, "
            + "    cn.code, "
            + "    cn.name, "
            + "    cn.taxId, "
            + "    cn.taxName, "
            + "    COALESCE(cn.taxValue, 0), "
            + "    COALESCE(cn.amountRefund, 0), "
            + "    COALESCE(cn.remainingAmountRefund, 0), "
            + "    cn.type) "
            + "FROM ChangeCreditNote cn  "
            + "WHERE cn.billingId = :id AND cn.status = 0";

    public static final String UPDATE_STATUS_BY_ID =
        "UPDATE {h-schema}credit_note "
            + "SET status = :status, billing_id_apply = :billingApplyId, money_refund = :amountRefund, name = :name "
            + "WHERE id = :id";

    public static final String UPDATE_CHANGE_CREDIT_NOTE_STATUS_BY_ID =
        "UPDATE {h-schema}change_credit_note "
            + "SET status = :status "
            + "WHERE billing_id = :id";

    public static final String UPDATE_STATUS_BY_BILLING_APPLY =
        "UPDATE {h-schema}credit_note "
            + "SET status = :status "
            + "WHERE billing_id_apply = :id "
            + "AND status <> 4";

    public static final String DELETE_CREDIT_NOTE_BY_BILLING_ID =
        "UPDATE {h-schema}credit_note "
            + "SET deleted_flag = 0 "
            + "WHERE billing_id = :billingId or billing_id_apply = :billingId " ;

    public static final String GET_LIST_STATUS_CREDIT_NOTE =
        "SELECT new com.entity.credit_note.CreditNote( "
            + "cn.id, "
            + "cn.status, "
            + "cn.code) "
            + "FROM CreditNote cn "
            + "WHERE cn.id IN :creditNoteIdList";
    public static final String FIND_CREDIT_NOTE_DISABLE = "SELECT cn.* FROM {h-schema}credit_note cn WHERE cn.id = :id AND cn.status IN (0, 1) AND cn.deleted_flag = 1";

    public static final String GET_CHANGE_CREDIT_NOTE_BY_BILLING_ID =
         " ORDER BY cn.id DESC" ;

    public static final String GET_CREDIT_NOTE_TAXES_DETAIL =
        "select\n" +
            "     subscriptions.pricing_id as pricingId,\n" +
            "     subscriptions.combo_plan_id as comboPlanId,\n" +
            "     tax.id as taxId,\n" +
            "     tax.name as taxName,\n" +
            "     coalesce(pricingTax.percent, comboPlanTax.percent, 0.0) as percent,\n" +
            "     coalesce(pricingTax.has_tax, comboPlanTax.has_tax, 0) as hasTax \n" +
            "from {h-schema}credit_note as creditNote\n" +
            "     join {h-schema}credit_note_tax as creNoteTax on creditNote.id = creNoteTax.credit_note_id\n" +
            "     join {h-schema}subscriptions on creditNote.subscription_id = subscriptions.id\n" +
            "     left join {h-schema}pricing_tax as pricingTax\n" +
            "       on subscriptions.pricing_id is not null and subscriptions.combo_plan_id is null and\n" +
            "          pricingTax.pricing_id = subscriptions.pricing_id and pricingTax.has_tax = 1\n" +
            "     left join {h-schema}combo_tax as comboPlanTax\n" +
            "       on subscriptions.combo_plan_id is not null and subscriptions.pricing_id is null and\n" +
            "          comboPlanTax.id_combo_plan = subscriptions.combo_plan_id and comboPlanTax.has_tax = 1\n" +
            "     left join {h-schema}tax on tax.id = pricingTax.tax_id or tax.id = comboPlanTax.tax_id\n" +
            "where creditNote.id = :creditNoteId";
}
