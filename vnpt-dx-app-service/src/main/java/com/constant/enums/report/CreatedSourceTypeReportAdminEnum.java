package com.constant.enums.report;

import java.util.HashMap;
import java.util.Map;

public enum CreatedSourceTypeReportAdminEnum {
    ALL(-1), ONESME(1), AM(2), AFFILIATE_MASOFFER(3), DEV_ADMIN(4), DHSXKD(5) , AFFILIATE_ACCESSTRADE(6),
    AFFILIATE_ONESME(7), AFFILIATE_APINFO(8);

    public final int value;

    CreatedSourceTypeReportAdminEnum(int value) {
        this.value = value;
    }

    private static final Map<Integer, CreatedSourceTypeEnum> map = new HashMap<>();

    static {
        for (CreatedSourceTypeEnum source : CreatedSourceTypeEnum.values()) {
            map.put(source.value, source);
        }
    }

    public static CreatedSourceTypeEnum valueOf(int value) {
        return map.get(value);
    }
}
