/**
 * 
 */
package com.constant.enums.transactionLog;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR> Hunglv2
 * @version    	: 1.0
 * Aug 17, 2022
 */
@Getter
public enum SystemIntegrateEnum {
    SPDV("SC1"),
    DHSXKD("SC2"),
    E_INVOICE("SC3"),
    E_CONTRACT("SC4"),
    VNPT_PAY("SC5");

    SystemIntegrateEnum(String string) {
        this.value = string;
    }

    private final String value;

    private static final Map<String, SystemIntegrateEnum> map = new HashMap<>();

    static {
        for (SystemIntegrateEnum systemNameIntegrate : SystemIntegrateEnum.values()) {
            map.put(systemNameIntegrate.value, systemNameIntegrate);
        }
    }

    public static SystemIntegrateEnum getValueOf(String value) {
        return map.get(value);
    }
}
