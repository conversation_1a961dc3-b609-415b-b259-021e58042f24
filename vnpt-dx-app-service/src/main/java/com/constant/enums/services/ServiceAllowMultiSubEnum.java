package com.constant.enums.services;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceAllowMultiSubEnum {
    NO(0), YES(1);
    public final int value;

    public static ServiceAllowMultiSubEnum fromValue(Integer value) {
        for (ServiceAllowMultiSubEnum serviceProductTypeEnum : ServiceAllowMultiSubEnum.values()) {
            if (Objects.equals(value, serviceProductTypeEnum.value)) {
                return serviceProductTypeEnum;
            }
        }
        return NO;
    }
}
