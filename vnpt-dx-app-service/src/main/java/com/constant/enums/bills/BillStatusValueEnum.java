package com.constant.enums.bills;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON>
 * @version : 1.0 10/11/2021
 */
public enum BillStatusValueEnum {
    INIT(0, "Khởi tạo"),

    WAITING(1, "Chờ thanh toán"),

    PAID(2, "Đã thanh toán"),

    FAILURE(3, "Thanh toán thất bại"),

    OUT_OF_DATE(4, "Quá hạn thanh toán"),

    DELETED(5, "Đã xóa"),

    UNSET(-1, "Chưa cài đặt");

    public Integer code;

    public String value;

    BillStatusValueEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    private static final Map<Integer, BillStatusValueEnum> map = new HashMap<>();

    static {
        for (BillStatusValueEnum status : BillStatusValueEnum.values()) {
            map.put(status.code, status);
        }
    }

    public static BillStatusValueEnum valueOf(Integer code) {
        return map.get(code);
    }
}
