package com.constant.enums.coupon;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> Halt3
 * @version    : 1.0
 * 24/09/2021
 */
@AllArgsConstructor
public enum CouponPricingTypeEnum {
    ALL(-1), PRICING(0), COMBO_PLAN(1);

    public final Integer value;

    private static final Map<Integer, CouponPricingTypeEnum> map = new HashMap<>();

    static {
        for (CouponPricingTypeEnum pricingTypeEnum : CouponPricingTypeEnum.values()) {
            map.put(pricingTypeEnum.value, pricingTypeEnum);
        }
    }

    public static CouponPricingTypeEnum valueOf(int value) {
        return map.get(value);
    }
}
