package com.config.props;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import lombok.Data;

@Data
@Configuration
@PropertySource(value = "classpath:email_template.properties", encoding = "UTF-8")
public class EmailProperties {

    @Value("${send.mail.hotline}")
    private String hotLine;
}
