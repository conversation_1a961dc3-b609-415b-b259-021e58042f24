package com.scheduled.batch.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.onedx.common.constants.enums.emails.EmailSendStatusEnum;
import com.onedx.common.constants.enums.emails.EmailTemplateTypeEnum;
import com.onedx.common.constants.enums.emails.EmailTypeEnum;
import com.onedx.common.entity.emails.EmailTemplate;
import com.onedx.common.entity.emails.MailSendHistory;
import com.onedx.common.helpers.sendSmsVinaphone.DataCodingEnum;
import com.onedx.common.helpers.sendSmsVinaphone.SendSmsResult;
import com.onedx.common.helpers.sendSmsVinaphone.SendSmsVinaphoneUtils;
import com.onedx.common.helpers.sendSmsVinaphone.SmsVinaphoneConfiguration;
import com.onedx.common.repository.emails.EmailRepository;
import com.onedx.common.repository.emails.mailTemplate.EmailTemplateRepository;
import lombok.extern.slf4j.Slf4j;

@Component("batch-send-sms")
@Slf4j
public class SendSmsTask {

    private final SmsVinaphoneConfiguration configuration;
    private EmailTemplateRepository mailTemplateRepository;
    private EmailRepository emailRepository;
    private final SendSmsVinaphoneUtils sendSmsUtils;

    private final Map<String, EmailTemplate> mailTemplateMap = new HashMap<>();

    @Autowired
    public void setEmailRepository(EmailRepository emailRepository) {
        this.emailRepository = emailRepository;
    }

    @Autowired
    public void setMailTemplateRepository(EmailTemplateRepository mailTemplateRepository) {
        this.mailTemplateRepository = mailTemplateRepository;
    }

    public SendSmsTask (SmsVinaphoneConfiguration configuration, SendSmsVinaphoneUtils sendSmsUtils) {
        this.configuration = configuration;
        this.sendSmsUtils = sendSmsUtils;
    }

    public void initData() {
        List<EmailTemplate> emailTemplateList = mailTemplateRepository.findAllByParentCodeIsNotNullAndSendType(EmailTemplateTypeEnum.SMS.value);
        for (EmailTemplate template : emailTemplateList) {
            mailTemplateMap.put(template.getCode(), template);
        }
    }

    @Transactional
    @Description("Batch method for sending SMS")
    public void sendSms() {
        if (!configuration.getEnable()) return;

        long start = System.currentTimeMillis();
        log.info("sendSms: start");
        if (mailTemplateMap.isEmpty()) initData();

        Pageable page = PageRequest.of(0, configuration.getMaxSmsSendPerTime(), Sort.by("id").ascending());
        Page<MailSendHistory> bySendStatus = emailRepository.findBySendStatusAndType(EmailSendStatusEnum.NOT_YET_SENT.value, EmailTypeEnum.SMS.value, page);
        List<MailSendHistory> all = bySendStatus.getContent();
        Set<Long> ids = new HashSet<>();
        for (MailSendHistory mail : all) ids.add(mail.getId());
        if (CollectionUtils.isEmpty(ids))
            return;

        emailRepository.updateStatusByIdIn(ids, EmailSendStatusEnum.PROCESSING.value);

        List<MailSendHistory> result = new LinkedList<>();
        try {
            int sizeOfSeparatedList = (all.size() / 4) + 1;
            List<List<MailSendHistory>> separatedList = ListUtils.partition(all, sizeOfSeparatedList);
            List<Future<List<MailSendHistory>>> resultInFutures = new ArrayList<>();
            ExecutorService service = Executors.newFixedThreadPool(separatedList.size());
            for (List<MailSendHistory> mailSendHistories : separatedList) {
                resultInFutures.add(service.submit(new SendVinaphoneSmsCallable(mailSendHistories)));
            }
            // wait cho den khi cac task finish
            for (Future<List<MailSendHistory>> future : resultInFutures) {
                result.addAll(future.get());
            }
            service.shutdown();

            //update kết quả
            emailRepository.saveAll(result);
        } catch (InterruptedException | ExecutionException e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
        }
        //update kết quả gửi sms

        log.info("sendSms: end after {} ms", System.currentTimeMillis() - start);
    }

    private class SendVinaphoneSmsCallable implements Callable<List<MailSendHistory>> {

        private final List<MailSendHistory> mailSendHistoryList;

        public SendVinaphoneSmsCallable(List<MailSendHistory> mailSendHistoryList) {
            this.mailSendHistoryList = mailSendHistoryList;
        }

        @Override
        public List<MailSendHistory> call() throws Exception {
            for (MailSendHistory mail: mailSendHistoryList) {
                if (StringUtils.isBlank(mail.getReceiver()) || !mailTemplateMap.containsKey(mail.getSmsTemplateCode())) {
                    mail.setSendStatus(EmailSendStatusEnum.ERROR.value);
                    continue;
                }
                EmailTemplate emailTemplate = mailTemplateMap.get(mail.getSmsTemplateCode());
                SendSmsResult result = sendSmsUtils.sendSms(
                    mail.getSmsParams(),
                    emailTemplate.getTemplateSmsId(),
                    mail.getReceiver(),
                    DataCodingEnum.valueOf(emailTemplate.getSmsDataCoding())
                );

                mail.setSmsRequest(result.getRequest());
                mail.setSmsResponse(result.getResponse());
                if (result.getStatus()) {
                    mail.setSendStatus(EmailSendStatusEnum.SENT.value);
                } else {
                    mail.setSendStatus(EmailSendStatusEnum.ERROR.value);
                }
            }
            return mailSendHistoryList;
        }
    }
}
