package com.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import com.dto.addons.AddonDTO;
import com.entity.addons.Addon;
import com.onedx.common.config.NonBuilderMapperConfig;
import com.onedx.common.entity.EntityConvert;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 22/5/2021
 */
@Mapper(componentModel = "spring", uses = {EnumFieldMapper.class}, config = NonBuilderMapperConfig.class)
public interface AddonMapper extends EntityConvert<AddonDTO, Addon> {

    @Mapping(source = "bonusType", target = "bonusType", qualifiedByName = "convertBonusTypeEnumToDTO")
    @Mapping(source = "pricingPlan", target = "pricingPlan", qualifiedByName = "convertPricingPlanToDTO")
    @Mapping(source = "portal", target = "portal", qualifiedByName = "convertPortalTypeToDTO")
    @Mapping(source = "type", target = "type", qualifiedByName = "convertTimeTypeToDTO")
    @Mapping(source = "status", target = "status", qualifiedByName = "convertStatusToDTO")
    @Mapping(source = "allowPriceChange", target = "allowPriceChange", qualifiedByName = "convertYesNoToDTO")
    @Mapping(target = "allowChangeQuantity", ignore = true)
    @Mapping(source = "allowReturn", target = "allowReturn", qualifiedByName = "convertYesNoToDTO")
    @Mapping(source = "comboId", target = "comboId")
    AddonDTO toDto(Addon e);

    @Mapping(source = "bonusType", target = "bonusType", qualifiedByName = "convertBonusTypeEnumToDB")
    @Mapping(source = "pricingPlan", target = "pricingPlan", qualifiedByName = "convertPricingPlanToDB")
    @Mapping(source = "portal", target = "portal", qualifiedByName = "convertPortalTypeToDB")
    @Mapping(source = "type", target = "type", qualifiedByName = "convertTimeTypeToDB")
    @Mapping(source = "status", target = "status", qualifiedByName = "convertStatusToDB")
    @Mapping(source = "allowPriceChange", target = "allowPriceChange", qualifiedByName = "convertYesNoToEntity")
    @Mapping(target = "allowChangeQuantity", ignore = true)
    @Mapping(source = "allowReturn", target = "allowReturn", qualifiedByName = "convertYesNoToEntity")
    @Mapping(source = "comboId", target = "comboId")
    Addon toEntity(AddonDTO e);
}
