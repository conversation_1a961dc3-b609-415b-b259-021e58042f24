package com.mapper;

import com.dto.coupons.CouponDetailDTO;
import com.entity.coupons.Coupon;
import com.onedx.common.entity.EntityConvert;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR> HaiTD
 * @version    : 1.0
 * 10/5/2021
 */
@Mapper(componentModel = "spring", uses = {EnumFieldMapper.class})
public interface CouponMapper extends EntityConvert<CouponDetailDTO, Coupon> {
    @Mapping(source = "status", target = "status", qualifiedByName = "convertStatusToDTO")
    @Mapping(source = "isVAT", target = "isVAT", qualifiedByName = "convertIsVatCouponToDTO")
    @Mapping(source = "codeType", target = "codeType", qualifiedByName = "convertCouponCodeTypeToDTO")
    @Mapping(source = "approve", target = "approve", qualifiedByName = "convertApproveTypeCouponToDTO")
    @Mapping(source = "discountType", target = "discountType", qualifiedByName = "convertDiscountTypeCouponToDTO")
    @Mapping(source = "promotionType", target = "promotionType", qualifiedByName = "convertPromotionTypeCouponToDTO")
    @Mapping(source = "totalBillType", target = "totalBillType", qualifiedByName = "convertIsVatCouponToDTO")
    @Mapping(source = "timesUsedType", target = "timesUsedType", qualifiedByName = "convertTimeUsedTypeCouponToDTO")
    @Mapping(source = "enterpriseType", target = "enterpriseType", qualifiedByName = "convertTypeApplyCouponToDTO")
    @Mapping(source = "pricingType", target = "pricingType", qualifiedByName = "convertTypeApplyCouponToDTO")
    @Mapping(source = "addonsType", target = "addonsType", qualifiedByName = "convertTypeApplyCouponToDTO")
    @Mapping(source = "type", target = "type", qualifiedByName = "convertLimitedTypeCouponToDTO")
    @Mapping(source = "supplierType", target = "supplierType", qualifiedByName = "convertTypeApplyCouponToDTO")
    @Mapping(source = "portal", target = "portalType", qualifiedByName = "convertPortalTypeToDTO")
    @Mapping(source = "discountSupplierType", target = "discountSupplierType", qualifiedByName = "convertDiscountSupplierTypeDTO")
    @Mapping(source = "isConfirm", target = "isConfirm", qualifiedByName = "convertIsVatCouponToDTO")
    @Mapping(source = "customerTypeCode", target = "customerTypeCode", qualifiedByName = "convertCustomerTypeEnumToDTO")
    @Mapping(source = "visibleStatus", target = "visibleStatus", qualifiedByName = "convertVisibleStatusToDTO")
    CouponDetailDTO toDto(Coupon e);

    @Mapping(source = "status", target = "status", qualifiedByName = "convertStatusToDB")
    @Mapping(source = "isVAT", target = "isVAT", qualifiedByName = "convertIsVatCouponToDB")
    @Mapping(source = "codeType", target = "codeType", qualifiedByName = "convertCouponCodeTypeToDB")
    @Mapping(source = "approve", target = "approve", qualifiedByName = "convertApproveTypeCouponToDB")
    @Mapping(source = "discountType", target = "discountType", qualifiedByName = "convertDiscountTypeCouponToDB")
    @Mapping(source = "promotionType", target = "promotionType", qualifiedByName = "convertPromotionTypeCouponToDB")
    @Mapping(source = "totalBillType", target = "totalBillType", qualifiedByName = "convertIsVatCouponToDB")
    @Mapping(source = "timesUsedType", target = "timesUsedType", qualifiedByName = "convertTimeUsedTypeCouponToDB")
    @Mapping(source = "enterpriseType", target = "enterpriseType", qualifiedByName = "convertTypeApplyCouponToDB")
    @Mapping(source = "pricingType", target = "pricingType", qualifiedByName = "convertTypeApplyCouponToDB")
    @Mapping(source = "addonsType", target = "addonsType", qualifiedByName = "convertTypeApplyCouponToDB")
    @Mapping(source = "type", target = "type", qualifiedByName = "convertLimitedTypeCouponToDB")
    @Mapping(source = "supplierType", target = "supplierType", qualifiedByName = "convertTypeApplyCouponToDB")
    @Mapping(source = "portalType", target = "portal", qualifiedByName = "convertPortalTypeToDB")
    @Mapping(source = "discountSupplierType", target = "discountSupplierType", qualifiedByName = "convertDiscountSupplierTypeToDB")
    @Mapping(source = "isConfirm", target = "isConfirm", qualifiedByName = "convertIsVatCouponToDB")
    @Mapping(source = "customerTypeCode", target = "customerTypeCode", qualifiedByName = "convertCustomerTypeEnumToDB")
    @Mapping(source = "visibleStatus", target = "visibleStatus", qualifiedByName = "convertVisibleStatusToDB")
    Coupon toEntity(CouponDetailDTO dto);
}
