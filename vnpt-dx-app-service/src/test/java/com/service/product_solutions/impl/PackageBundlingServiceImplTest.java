package com.service.product_solutions.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.dto.product_solustions.PackageBundlingCreateDTO.PackagePlanCreateDTO;
import com.dto.product_solustions.PackageBundlingCreateDTO.PackagePlanCreateDTO.PackageAddonCreateDTO;
import com.entity.product_solutions.PackageItemAddons;
import com.entity.product_solutions.PackageItemPrices;
import com.entity.product_solutions.PackageItemPromotions;
import com.entity.product_solutions.PackageItems;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.repository.product_solutions.PackageAddonRepository;
import com.repository.product_solutions.PackageItemAddonPromotionsRepository;
import com.repository.product_solutions.PackageItemPriceRepository;
import com.repository.product_solutions.PackageItemPromotionsRepository;
import com.repository.product_solutions.PackageItemRepository;
import com.service.utils.constants.JSONBConstant;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class PackageBundlingServiceImplTest {

    @Mock
    private PackageItemRepository packageItemRepository;

    @Mock
    private PackageItemPriceRepository packageItemPricesRepository;

    @Mock
    private PackageAddonRepository packageAddonRepository;

    @Mock
    private PackageItemPromotionsRepository packageItemPromotionsRepository;

    @Mock
    private PackageItemAddonPromotionsRepository packageItemAddonPromotionsRepository;

    @InjectMocks
    private PackageBundlingServiceImpl packageBundlingService;

    private PackageItems mockPackageItem;
    private List<PackageItemPrices> mockItemPrices;
    private List<PackageItemAddons> mockAddons;
    private List<PackageItemPromotions> mockPromotions;
    private List<Long> mockCouponIds;

    @BeforeEach
    public void setup() {
        // Chuẩn bị dữ liệu test
        mockPackageItem = createMockPackageItem();
        mockItemPrices = createMockItemPrices();
        mockAddons = createMockAddons();
        mockPromotions = createMockPromotions();
        mockCouponIds = Arrays.asList(101L, 102L);

        // Setup mock repository
        when(packageItemRepository.findById(anyLong())).thenReturn(Optional.of(mockPackageItem));
        when(packageItemPricesRepository.findByPackageItemId(anyLong())).thenReturn(mockItemPrices);
        when(packageAddonRepository.findByPackageItemId(anyLong())).thenReturn(mockAddons);
        when(packageItemPromotionsRepository.findByPackageItemId(anyLong())).thenReturn(mockPromotions);
        when(packageItemAddonPromotionsRepository.findByPackageItemAddonId(anyLong())).thenReturn(new ArrayList<>());
    }

    @Test
    public void testBuildPackageItems_shouldReturnCorrectPackagePlanCreateDTOs() {
        // Arrange
        List<Long> packageItemIds = Arrays.asList(1L);
        Long componentId = 201L;
        String type = "PRICING";
        String eventType = "UPGRADED";

        // Act
        List<PackagePlanCreateDTO> result = packageBundlingService.buildPackageItems(packageItemIds, componentId, type, eventType);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PackagePlanCreateDTO dto = result.get(0);
        assertEquals(1001L, dto.getPricingId());
        assertEquals(2001L, dto.getMultiPlanId());
        assertEquals(3001L, dto.getVariantId());
        assertEquals(2, dto.getQuantity());
        assertEquals(new BigDecimal("199.99"), dto.getTotalAmount());
        assertEquals(new BigDecimal("99.99"), dto.getPriceUpdate());
        assertEquals(new BigDecimal("99.99"), dto.getPricePreTax());
        
        // Verify addons
        assertNotNull(dto.getPackageAddon());
        assertEquals(1, dto.getPackageAddon().size());
        
        PackageAddonCreateDTO addonDto = dto.getPackageAddon().get(0);
        assertEquals(501L, addonDto.getAddonId());
        assertEquals(601L, addonDto.getAddonPlanId());
        assertEquals(1, addonDto.getQuantity());
        assertEquals(new BigDecimal("49.99"), addonDto.getTotalAmount());
        
        log.info("testBuildPackageItems_shouldReturnCorrectPackagePlanCreateDTOs: done");
    }

    @Test
    public void testBuildPackageItems_shouldHandleEmptyPackageItemIds() {
        // Arrange
        List<Long> packageItemIds = new ArrayList<>();
        Long componentId = 201L;
        String type = "PRICING";
        String eventType = "UPGRADED";

        // Act
        List<PackagePlanCreateDTO> result = packageBundlingService.buildPackageItems(packageItemIds, componentId, type, eventType);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
        
        log.info("testBuildPackageItems_shouldHandleEmptyPackageItemIds: done");
    }

    @Test
    public void testBuildPackageItems_shouldHandleNullPackageItem() {
        // Arrange
        List<Long> packageItemIds = Arrays.asList(999L); // ID không tồn tại
        Long componentId = 201L;
        String type = "PRICING";
        String eventType = "UPGRADED";
        
        when(packageItemRepository.findById(999L)).thenReturn(Optional.empty());

        // Act
        List<PackagePlanCreateDTO> result = packageBundlingService.buildPackageItems(packageItemIds, componentId, type, eventType);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
        
        log.info("testBuildPackageItems_shouldHandleNullPackageItem: done");
    }

    // Helper methods to create mock data
    private PackageItems createMockPackageItem() {
        PackageItems item = new PackageItems();
        item.setId(1L);
        item.setPlanId(2001L);
        item.setQuantity(2L);
        item.setTotalAmount(new BigDecimal("199.99"));
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(JSONBConstant.PRICING_ID, 1001L);
        metadata.put(JSONBConstant.VARIANT_ID, 3001L);
        metadata.put(JSONBConstant.SERVICE_ID, 4001L);
        metadata.put(JSONBConstant.SERVICE_DRAFT_ID, 5001L);
        item.setMetadata(metadata);
        
        return item;
    }
    
    private List<PackageItemPrices> createMockItemPrices() {
        PackageItemPrices price = new PackageItemPrices();
        price.setId(1L);
        price.setPackageItemId(1L);
        price.setPricingPlan(PricingPlanEnum.MONTHLY);
        price.setUnitFrom(1L);
        price.setUnitTo(10L);
        price.setPrice(new BigDecimal("99.99"));
        
        return Arrays.asList(price);
    }
    
    private List<PackageItemAddons> createMockAddons() {
        PackageItemAddons addon = new PackageItemAddons();
        addon.setId(1L);
        addon.setPackageItemId(1L);
        addon.setAddonId(501L);
        addon.setAddonPlanId(601L);
        addon.setQuantity(1L);
        addon.setTotalAmount(new BigDecimal("49.99"));
        
        return Arrays.asList(addon);
    }
    
    private List<PackageItemPromotions> createMockPromotions() {
        PackageItemPromotions promotion = new PackageItemPromotions();
        promotion.setId(1L);
        promotion.setPackageItemId(1L);
        promotion.setCouponId(101L);
        
        return Arrays.asList(promotion);
    }
}