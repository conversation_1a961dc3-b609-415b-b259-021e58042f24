# PackageBundlingServiceImplTest - Unit Test Documentation

## Tổng quan
File test này được thiết kế để kiểm tra phương thức `buildPackageItems` trong class `PackageBundlingServiceImpl`. Đ<PERSON><PERSON> là một unit test sử dụng Mockito để mock các dependencies và JUnit 5 để thực hiện các assertion.

## Cấu trúc Test

### Dependencies được Mock
- `PackageItemRepository` - Repository để truy vấn PackageItems
- `PackageItemPriceRepository` - Repository để truy vấn giá của package items
- `PackageAddonRepository` - Repository để truy vấn addons của package items
- `PackageItemPromotionsRepository` - Repository để truy vấn promotions của package items
- `PackageItemAddonPromotionsRepository` - Repository để truy vấn addon promotions

### Test Cases

#### 1. `testBuildPackageItems_shouldReturnCorrectPackagePlanCreateDTOs`
- **<PERSON><PERSON><PERSON> đích**: Kiểm tra phương thức hoạt động đúng với dữ liệu hợp lệ
- **Input**: List chứa 1 packageItemId (1L), componentId=201L, type="PRICING", eventType="UPGRADED"
- **Expected**: Trả về 1 PackagePlanCreateDTO với đầy đủ thông tin được map từ mock data

#### 2. `testBuildPackageItems_shouldHandleEmptyPackageItemIds`
- **Mục đích**: Kiểm tra xử lý khi input là list rỗng
- **Input**: List rỗng
- **Expected**: Trả về list rỗng

#### 3. `testBuildPackageItems_shouldHandleNullPackageItem`
- **Mục đích**: Kiểm tra xử lý khi PackageItem không tồn tại
- **Input**: List chứa ID không tồn tại (999L)
- **Expected**: Trả về list rỗng (method bỏ qua các item null)

#### 4. `testBuildPackageItems_shouldHandleMultiplePackageItems`
- **Mục đích**: Kiểm tra xử lý nhiều package items
- **Input**: List chứa 3 packageItemIds (1L, 2L, 3L)
- **Expected**: Trả về 3 PackagePlanCreateDTO tương ứng

#### 5. `testBuildPackageItems_shouldHandlePackageItemWithoutPrices`
- **Mục đích**: Kiểm tra xử lý khi package item không có prices
- **Input**: PackageItem hợp lệ nhưng không có prices
- **Expected**: Vẫn tạo được DTO, chỉ thiếu thông tin pricing

#### 6. `testBuildPackageItems_shouldHandlePackageItemWithoutAddons`
- **Mục đích**: Kiểm tra xử lý khi package item không có addons
- **Input**: PackageItem hợp lệ nhưng không có addons
- **Expected**: Vẫn tạo được DTO với list addons rỗng

## Mock Data Structure

### PackageItems Mock
```java
- id: 1L
- planId: 2001L
- quantity: 2L
- totalAmount: 199.99
- metadata: {
    "pricingId": 1001L,
    "variantId": 3001L,
    "serviceId": 4001L,
    "serviceDraftId": 5001L
  }
```

### PackageItemPrices Mock
```java
- id: 1L
- packageItemId: 1L
- pricingPlan: MONTHLY
- unitFrom: 1L
- unitTo: 10L
- price: 99.99
```

### PackageItemAddons Mock
```java
- id: 1L
- packageItemId: 1L
- addonId: 501L
- addonPlanId: 601L
- quantity: 1L
- totalAmount: 49.99
```

### PackageItemPromotions Mock
```java
- id: 1L
- packageItemId: 1L
- couponId: 101L
```

## Cách chạy Test

### Sử dụng Maven
```bash
mvn test -Dtest=PackageBundlingServiceImplTest
```

### Sử dụng script có sẵn
**Windows:**
```cmd
run-package-bundling-tests.bat
```

**Linux/Mac:**
```bash
chmod +x run-package-bundling-tests.sh
./run-package-bundling-tests.sh
```

### Trong IDE
- Mở file test trong IDE
- Right-click và chọn "Run Tests" hoặc "Run PackageBundlingServiceImplTest"

## Lưu ý quan trọng

1. **Dependencies**: Đảm bảo tất cả dependencies cần thiết đã được thêm vào pom.xml:
   - JUnit 5
   - Mockito
   - Spring Boot Test

2. **Mock Setup**: Tất cả mock được setup trong method `@BeforeEach setup()`

3. **Assertions**: Sử dụng JUnit 5 assertions để kiểm tra kết quả

4. **Logging**: Mỗi test case có log để theo dõi quá trình thực thi

## Troubleshooting

### Lỗi thường gặp:
1. **Import errors**: Kiểm tra các import statements
2. **Mock not working**: Đảm bảo `@ExtendWith(MockitoExtension.class)` được thêm
3. **Compilation errors**: Kiểm tra các entity classes và DTOs có tồn tại không

### Debug tips:
- Thêm breakpoints trong test methods
- Sử dụng `log.info()` để debug
- Kiểm tra mock setup trong `@BeforeEach`
