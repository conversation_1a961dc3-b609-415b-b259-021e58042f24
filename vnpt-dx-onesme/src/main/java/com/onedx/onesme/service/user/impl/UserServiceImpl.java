package com.onedx.onesme.service.user.impl;

import org.springframework.stereotype.Service;
import com.onedx.common.exception.ErrorKey;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.Resources;
import com.onedx.onesme.repository.user.UserRepository;
import com.onedx.onesme.service.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final ExceptionFactory exceptionFactory;

    @Override
    public String getUserName(Long userId) {
        return userRepository.findById(userId)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.USER, ErrorKey.ID, String.valueOf(userId)))
            .getName();
    }
}
