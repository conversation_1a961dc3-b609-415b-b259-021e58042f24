DELETE FROM vnpt_dev.apis WHERE api_code = 'API_QLKHLH_LAY_CHI_TIET_CA_NHAN';
INSERT INTO vnpt_dev.apis (id, api_path, api_code, method)
VALUES ((SELECT MAX(id) + 1 FROM vnpt_dev.apis),
    '/api/customer-contact/get-personal-detail',
    'API_QLKHLH_LAY_CHI_TIET_CA_NHAN',
    'GET');

INSERT INTO vnpt_dev.api_permission (id, api_id, permission_portal_id, map_permission_portal, delete_flag)
VALUES
    (
        (SELECT MAX(id) + 1 FROM vnpt_dev.api_permission),
        (SELECT id FROM vnpt_dev.apis WHERE api_code = 'API_QLKHLH_LAY_CHI_TIET_CA_NHAN' ORDER BY id DESC LIMIT 1),
        (SELECT pp.id FROM vnpt_dev.permission p JOIN vnpt_dev.permission_portal pp ON p.id = pp.permission_id WHERE p.code = 'QUAN_LY_KHACH_HANG_LIEN_HE_XEM_CHI_TIET' AND pp.portal_id = 1 LIMIT 1),
        1,
        1
    );

REFRESH MATERIALIZED VIEW vnpt_dev.role_permission_api;