server:
  port: 8086
  servlet:
    context-path: /migrate
spring:
  data:
    mongodb:
      user: vnpt-mongo
      password: vnptmongo@2021
      uri: mongodb://************:27017/masterdata
  flyway:
    enabled: false
  datasource:
    url: ******************************************************************
    username: vnptposgre
    password: vnpt@654321
    platform: postgres
  jpa:
    show-sql: true
    hibernate:
      # Hibernate_sequence' doesn't exist
      use-new-id-generator-mappings: false
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
  redis:
    host: ************
    port: 6379
    password: vnptredis@2021

web:
  host: https://staging.onesme.vn
batch:
  active: false

migrate:
  core_thread_async_migrate: 5
  max_thread_async_migrate: 5
  queue_capacity_async_migrate: 500
  core_thread_async_call_api: 30
  max_thread_async_call_api: 100
  queue_capacity_async_call_api: 50
  # Times millisecond
  times_thread_sleep_call_api: 40000
  times_thread_sleep_queue: 20000

dhsxkd:
  url-api-migration-info: https://api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracuu_o2o
  url-api-sme-tracking-order: https://api-dev-onebss.vnpt.vn/esb/mediagw/sme_tracking_order
  timeout: 30000
  timeout-migration: 300000
  header:
    token:
      key: erp-token
      value: API-CSKH-ECB5835C0D98B09884AC5B811799D02F60D235655AF57A1976523B04155B3DE6
    acc:
      key: erp-acc
      value: dhsxkd
    pwd:
      key: erp-pwd
      value: apidhsx@1857610
    tokenId:
      key: token-id
      value: DEV_ERP
    tokenKey:
      key: token-key
      value: 255bd67b877a9e0fe96c47233bc7f2ca
