package com.service.schedule.impl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import com.common.Constant;
import com.common.Constant.CronExpression;
import com.common.util.AuthUtil;
import com.onedx.common.exception.MessageKeyConstant;
import com.entity.migration.Migration;
import com.entity.migration.schedule.Schedule;
import com.onedx.common.constants.enums.migration.MigrateTypeEnum;
import com.onedx.common.constants.enums.migration.RepeatPeriodicEnum;
import com.onedx.common.constants.enums.migration.RepeatTypeEnum;
import com.exception.Resources;
import com.onedx.common.entity.schedule.ScheduleStatistic;
import com.onedx.common.exception.ErrorKey.MigrationSchedule;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.repository.schedule.ScheduleStatisticRepository;
import com.repository.schedule.ScheduleRepository;
import com.scheduled.CronTaskRegistrar;
import com.scheduled.SchedulingRunnable;
import com.service.schedule.ScheduleService;

/**
 * <AUTHOR> HuyLD
 * @version : 1.0 15/04/2022
 */
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private CronTaskRegistrar cronTaskRegistrar;

    @Autowired
    private ScheduleStatisticRepository statisticRepository;

    @Autowired
    private MessageSource messageSource;

    @Override
    public void createMigrateSchedule(Migration migration, LocalDateTime startTime) {
        // Chỉ tạo với lịch lặp lại = Định kỳ / 1 lần có lịch
        if (Objects.equals(RepeatTypeEnum.ONCE.getValue(), migration.getRepeatType()) &&
            Objects.equals(MigrateTypeEnum.NOW.getValue(), migration.getMigrateType())) {
            return;
        }
        if (Objects.equals(MigrateTypeEnum.SCHEDULE.getValue(), migration.getMigrateType()) && migration.getMigrateSchedule() != null) {
            startTime = migration.getMigrateSchedule();
        } else if (startTime == null) {
            throwErrorInvalidData(MigrationSchedule.START_TIME);
        }
        String cronExpression = formatTimeScheduleToCronExpression(startTime,
            migration.getRepeatPeriodic() == null ? RepeatPeriodicEnum.ONCE : RepeatPeriodicEnum.valueOf(migration.getRepeatPeriodic()));

        // Save schedule in DB
        Schedule schedule = initScheduleEntity(migration, cronExpression, startTime);
        scheduleRepository.save(schedule);
        // Create job
        SchedulingRunnable schedulingRunnable = initSchedulingRunnable(migration.getCode(), RepeatTypeEnum.valueOf(migration.getRepeatType()));
        cronTaskRegistrar.addCronTask(schedulingRunnable, cronExpression);
    }

    @Override
    public void addMigrateScheduleOnApplication(String migrateCode, RepeatTypeEnum repeatType, String cronExpression) {
        SchedulingRunnable schedulingRunnable = initSchedulingRunnable(migrateCode, repeatType);
        cronTaskRegistrar.addCronTask(schedulingRunnable, cronExpression);
    }

    @Override
    public void removeMigrateScheduleOnApplication(String migrateCode, RepeatTypeEnum repeatType) {
        SchedulingRunnable schedulingRunnable = initSchedulingRunnable(migrateCode, repeatType);
        cronTaskRegistrar.removeCronTask(schedulingRunnable);
    }

    @Override
    public SchedulingRunnable initSchedulingRunnable(String migrateCode, RepeatTypeEnum repeatType) {
        SchedulingRunnable schedulingRunnable = SchedulingRunnable
            .builder()
            .beanName(Constant.Batch.BEAN_NAME_MIGRATION)
            .methodName(RepeatTypeEnum.PERIODIC.equals(repeatType) ?
                Constant.Batch.METHOD_NAME_MIGRATION : Constant.Batch.METHOD_NAME_MIGRATION_ONCE)
            .params(migrateCode)
            .build();
        return schedulingRunnable;
    }

    @Override
    public void updateMigrateScheduleWithMonthYearOption(Migration migration) {
        if (migration.getCode() == null || migration.getRepeatPeriodic() == null
            || !Objects.equals(RepeatTypeEnum.PERIODIC.getValue(), migration.getRepeatType())
            || !Arrays.asList(RepeatPeriodicEnum.MONTH.getValue(), RepeatPeriodicEnum.YEAR.getValue()).contains(migration.getRepeatPeriodic())) {
            return;
        }
        String cronExpression = formatTimeScheduleToCronExpression(migration.getNextMigrateTimes(),
            RepeatPeriodicEnum.valueOf(migration.getRepeatPeriodic()));

        // Save schedule in DB
        Schedule schedule = scheduleRepository.findByMethodParams(migration.getCode())
            .orElse(initScheduleEntity(migration, cronExpression, migration.getNextMigrateTimes()));
        scheduleRepository.save(schedule);

        // Remove batch cũ trong app
        RepeatTypeEnum repeatType = RepeatTypeEnum.valueOf(migration.getRepeatType());
        removeMigrateScheduleOnApplication(migration.getCode(), repeatType);
        // Add batch mới vào app
        addMigrateScheduleOnApplication(migration.getCode(), repeatType, cronExpression);
    }

    /**
     * Khởi tạo Schedule
     *
     * @return schedule
     */
    private Schedule initScheduleEntity(Migration migration, String cronExpression, LocalDateTime startTime) {
        Schedule schedule = new Schedule();
        schedule.setBeanName(Constant.Batch.BEAN_NAME_MIGRATION);
        schedule.setMethodName(Objects.equals(RepeatTypeEnum.PERIODIC.getValue(), migration.getRepeatType()) ?
            Constant.Batch.METHOD_NAME_MIGRATION : Constant.Batch.METHOD_NAME_MIGRATION_ONCE);
        schedule.setMethodParams(migration.getCode());
        schedule.setCronExpression(cronExpression);
        schedule.setJobStatus(1);
        schedule.setCreatedAt(startTime);
        schedule.setCreatedBy(AuthUtil.getCurrentUserId().toString());
        return schedule;
    }

    /**
     * Tạo CRON Expression với chuỗi 6 ký tự
     *
     * @return a CRON Formatted String.
     */
    private String formatTimeScheduleToCronExpression(LocalDateTime startTime, RepeatPeriodicEnum repeatPeriodic) {
        if (startTime == null) {
            throwErrorInvalidData(MigrationSchedule.START_TIME);
        }
        if (repeatPeriodic == null) {
            throwErrorInvalidData(MigrationSchedule.REPEAT_PERIODIC);
        }
        String second = String.valueOf(startTime.getSecond());
        String minute = String.valueOf(startTime.getMinute());
        String hour = String.valueOf(startTime.getHour());
        String dayOfMonth = String.valueOf(startTime.getDayOfMonth());
        String month = startTime.getMonth().name().substring(0, 3);
        String dayOfWeek = CronExpression.NO_SPECIFIC;

        switch (repeatPeriodic) {
            case HOUR:
                hour = CronExpression.EVERY;
                dayOfMonth = CronExpression.NO_SPECIFIC;
                month = CronExpression.EVERY;
                dayOfWeek = CronExpression.EVERY;
                break;
            case DAY:
                dayOfMonth = CronExpression.NO_SPECIFIC;
                month = CronExpression.EVERY;
                dayOfWeek = CronExpression.EVERY;
                break;
            case WEEK:
                dayOfMonth = CronExpression.NO_SPECIFIC;
                month = CronExpression.EVERY;
                dayOfWeek = startTime.getDayOfWeek().name().substring(0, 3);
                break;
            case MONTH:
                month = CronExpression.EVERY;
                dayOfWeek = CronExpression.NO_SPECIFIC;
                break;
            case YEAR:
            case ONCE:
                // Đồng bộ chạy 1 lần hoặc theo năm
                month = String.valueOf(startTime.getMonth().getValue());
                dayOfWeek = CronExpression.NO_SPECIFIC;
                break;
            default:
                break;
        }
        return generateCronExpression(second, minute, hour, dayOfMonth, month, dayOfWeek);
    }

    /**
     * Tạo CRON Expression với chuỗi 6 ký tự
     *
     * @param second     mandatory = yes. allowed values = {@code  0-59    * / , -}
     * @param minute     mandatory = yes. allowed values = {@code  0-59    * / , -}
     * @param hour       mandatory = yes. allowed values = {@code 0-23   * / , -}
     * @param dayOfMonth mandatory = yes. allowed values = {@code 1-31  * / , - ? L W}
     * @param month      mandatory = yes. allowed values = {@code 1-12 or JAN-DEC    * / , -}
     * @param dayOfWeek  mandatory = yes. allowed values = {@code 0-6 or SUN-SAT * / , - ? L #}
     * @return a CRON Formatted String.
     */
    private String generateCronExpression(final String second, final String minute, final String hour,
            final String dayOfMonth, final String month, final String dayOfWeek) {
        return String.format("%1$s %2$s %3$s %4$s %5$s %6$s", second, minute, hour, dayOfMonth, month, dayOfWeek);
    }

    /**
     * Throw error
     */
    private void throwErrorInvalidData(String errorKey) {
        String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA,
            new String[] {errorKey}, LocaleContextHolder.getLocale());
        throw new BadRequestException(msg, Resources.MIGRATION_SCHEDULE, errorKey,
            MessageKeyConstant.INVALID_DATA);
    }

    @Override
    public void updateMigrateSchedule(Migration migration) {
        Schedule schedule = scheduleRepository.findByMethodParams(migration.getCode()).orElse(new Schedule());

        String cronExpression = formatTimeScheduleToCronExpression(migration.getNextMigrateTimes(),
            migration.getRepeatPeriodic() == null ? RepeatPeriodicEnum.ONCE : RepeatPeriodicEnum.valueOf(migration.getRepeatPeriodic()));

        // Remove batch cũ trong app
        RepeatTypeEnum repeatType = RepeatTypeEnum.valueOf(migration.getRepeatType());
        removeMigrateScheduleOnApplication(migration.getCode(), repeatType);

        if (RepeatTypeEnum.ONCE.getValue().equals(migration.getRepeatType())) {
            if (Objects.equals(MigrateTypeEnum.NOW.getValue(), migration.getMigrateType())) {
                //chạy 1 lần, chạy xong sẽ xóa lịch
                scheduleRepository.deleteMigrationSchedule(migration.getCode());
            } else {
                // Add batch mới vào app
                addMigrateScheduleOnApplication(migration.getCode(), repeatType, cronExpression);
                schedule = updateScheduleEntity(schedule, migration, cronExpression, migration.getNextMigrateTimes());
            }
        } else {
            // Add batch mới vào app
            addMigrateScheduleOnApplication(migration.getCode(), repeatType, cronExpression);
            schedule = updateScheduleEntity(schedule, migration, cronExpression, migration.getNextMigrateTimes());
        }

        scheduleRepository.save(schedule);
    }

    /**
     * Update Schedule
     */
    private Schedule updateScheduleEntity(Schedule schedule, Migration migration, String cronExpression, LocalDateTime startTime) {
        schedule.setBeanName(Constant.Batch.BEAN_NAME_MIGRATION);
        schedule.setMethodName(Objects.equals(RepeatTypeEnum.PERIODIC.getValue(), migration.getRepeatType()) ?
            Constant.Batch.METHOD_NAME_MIGRATION : Constant.Batch.METHOD_NAME_MIGRATION_ONCE);
        schedule.setMethodParams(migration.getCode());
        schedule.setCronExpression(cronExpression);
        schedule.setJobStatus(1);
        schedule.setCreatedAt(startTime);
        schedule.setCreatedBy(AuthUtil.getCurrentUserId().toString());
        return schedule;
    }

    @Override
    public void onCronJobSuccess(String beanName, String methodName, LocalDateTime startTime, Long executionTime) {
        ScheduleStatistic cronJob = findCronJob(beanName, methodName);
        cronJob.setLastStartAt(startTime);
        cronJob.setLastSuccessAt(LocalDateTime.now());
        cronJob.setExecutionTime(executionTime);
        statisticRepository.save(cronJob);
    }

    @Override
    public void onCronJobFailure(String beanName, String methodName, LocalDateTime startTime, Long executionTime, Exception error) {
        ScheduleStatistic cronJob = findCronJob(beanName, methodName);
        cronJob.setLastStartAt(startTime);
        cronJob.setLastFailureAt(LocalDateTime.now());
        cronJob.setExecutionTime(executionTime);
        if (error != null) {
            cronJob.setLastError(ExceptionUtils.getStackTrace(error));
        }
        statisticRepository.save(cronJob);
    }

    private ScheduleStatistic findCronJob(String beanName, String methodName) {
        return statisticRepository.findFirstByBeanNameAndMethodName(beanName, methodName)
            .orElse(new ScheduleStatistic(beanName, methodName));
    }
}
